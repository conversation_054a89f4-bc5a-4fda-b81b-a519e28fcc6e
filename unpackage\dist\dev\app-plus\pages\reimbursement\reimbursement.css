
.page[data-v-8504ebae] {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header[data-v-8504ebae] {
	background: rgba(255, 255, 255, 0.1);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
	padding: 1.375rem 1rem 1rem;
}
.header-content[data-v-8504ebae] {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.back-btn[data-v-8504ebae], .add-btn[data-v-8504ebae] {
	color: white;
	font-size: 1.125rem;
	font-weight: bold;
	width: 1.875rem;
	text-align: center;
}
.header-title[data-v-8504ebae] {
	color: white;
	font-size: 1.125rem;
	font-weight: bold;
}
.stats-cards[data-v-8504ebae] {
	display: flex;
	padding: 1rem;
	gap: 0.5rem;
}
.stat-card[data-v-8504ebae] {
	flex: 1;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 0.5rem;
	padding: 0.75rem;
	text-align: center;
}
.stat-label[data-v-8504ebae] {
	display: block;
	font-size: 0.75rem;
	color: #666;
	margin-bottom: 0.25rem;
}
.stat-value[data-v-8504ebae] {
	display: block;
	font-size: 1rem;
	font-weight: bold;
}
.stat-value.pending[data-v-8504ebae] {
	color: #f59e0b;
}
.stat-value.approved[data-v-8504ebae] {
	color: #10b981;
}
.stat-value.total[data-v-8504ebae] {
	color: #6366f1;
}
.filter-tabs[data-v-8504ebae] {
	display: flex;
	margin: 0 1rem 1rem;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 0.375rem;
	padding: 0.25rem;
}
.filter-tab[data-v-8504ebae] {
	flex: 1;
	text-align: center;
	padding: 0.5rem;
	border-radius: 0.25rem;
	transition: all 0.3s;
}
.filter-tab.active[data-v-8504ebae] {
	background: rgba(255, 255, 255, 0.2);
}
.filter-tab uni-text[data-v-8504ebae] {
	color: white;
	font-size: 0.875rem;
}
.reimbursement-list[data-v-8504ebae] {
	padding: 0 1rem;
}
.reimbursement-item[data-v-8504ebae] {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 0.5rem;
	padding: 0.75rem;
	margin-bottom: 0.5rem;
}
.item-header[data-v-8504ebae] {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 0.5rem;
}
.item-info[data-v-8504ebae] {
	flex: 1;
}
.item-title[data-v-8504ebae] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.item-category[data-v-8504ebae] {
	font-size: 0.75rem;
	color: #666;
	background: #f3f4f6;
	padding: 0.125rem 0.375rem;
	border-radius: 0.375rem;
}
.item-amount[data-v-8504ebae] {
	text-align: right;
}
.amount[data-v-8504ebae] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.status[data-v-8504ebae] {
	font-size: 0.75rem;
	padding: 0.125rem 0.375rem;
	border-radius: 0.375rem;
	color: white;
}
.status.pending[data-v-8504ebae] {
	background: #f59e0b;
}
.status.approved[data-v-8504ebae] {
	background: #10b981;
}
.status.rejected[data-v-8504ebae] {
	background: #ef4444;
}
.item-details[data-v-8504ebae] {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.375rem;
}
.item-date[data-v-8504ebae], .item-company[data-v-8504ebae] {
	font-size: 0.75rem;
	color: #666;
}
.item-description[data-v-8504ebae] {
	font-size: 0.75rem;
	color: #666;
	line-height: 1.4;
	margin-bottom: 0.375rem;
}
.attachments[data-v-8504ebae] {
	border-top: 0.03125rem solid #f3f4f6;
	padding-top: 0.375rem;
}
.attachment-label[data-v-8504ebae] {
	font-size: 0.75rem;
	color: #666;
	margin-bottom: 0.25rem;
}
.attachment-list[data-v-8504ebae] {
	display: flex;
	flex-wrap: wrap;
	gap: 0.25rem;
}
.attachment-item[data-v-8504ebae] {
	font-size: 0.6875rem;
	color: #6366f1;
	background: #f0f9ff;
	padding: 0.125rem 0.25rem;
	border-radius: 0.25rem;
}
.empty-state[data-v-8504ebae] {
	text-align: center;
	padding: 3.75rem 1rem;
}
.empty-icon[data-v-8504ebae] {
	font-size: 3.75rem;
	display: block;
	margin-bottom: 0.75rem;
}
.empty-text[data-v-8504ebae] {
	font-size: 1rem;
	color: white;
	font-weight: bold;
	display: block;
	margin-bottom: 0.375rem;
}
.empty-desc[data-v-8504ebae] {
	font-size: 0.875rem;
	color: rgba(255, 255, 255, 0.7);
	display: block;
}

/* 弹窗样式 */
.popup-overlay[data-v-8504ebae] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.popup-content[data-v-8504ebae] {
	background: white;
	border-radius: 0.5rem;
	width: 20rem;
	max-height: 80vh;
	overflow-y: auto;
}
.popup-header[data-v-8504ebae] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 1rem;
	border-bottom: 0.03125rem solid #f3f4f6;
}
.popup-title[data-v-8504ebae] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-8504ebae] {
	font-size: 1.5rem;
	color: #666;
	line-height: 1;
}
.form-group[data-v-8504ebae] {
	padding: 0.75rem 1rem;
	border-bottom: 0.03125rem solid #f3f4f6;
}
.form-label[data-v-8504ebae] {
	display: block;
	font-size: 0.875rem;
	color: #333;
	margin-bottom: 0.5rem;
}
.form-input[data-v-8504ebae], .form-textarea[data-v-8504ebae] {
	width: 100%;
	padding: 0.5rem;
	border: 0.0625rem solid #e5e7eb;
	border-radius: 0.25rem;
	font-size: 0.875rem;
	box-sizing: border-box;
}
.picker-input[data-v-8504ebae] {
	padding: 0.5rem;
	border: 0.0625rem solid #e5e7eb;
	border-radius: 0.25rem;
	font-size: 0.875rem;
	color: #333;
}
.popup-actions[data-v-8504ebae] {
	display: flex;
	gap: 0.5rem;
	padding: 1rem;
}
.cancel-btn[data-v-8504ebae], .confirm-btn[data-v-8504ebae] {
	flex: 1;
	padding: 0.75rem;
	border-radius: 0.25rem;
	font-size: 0.875rem;
	border: none;
}
.cancel-btn[data-v-8504ebae] {
	background: #f3f4f6;
	color: #666;
}
.confirm-btn[data-v-8504ebae] {
	background: #667eea;
	color: white;
}
