<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="back-btn" @click="goBack" :style="{ color: themeColors.textReverse }">←</text>
			<text class="header-title" :style="{ color: themeColors.textReverse }">快速操作指南</text>
		</view>
		
		<!-- 功能说明 -->
		<view class="content">
			<!-- 基础功能 -->
			<view class="section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-title" :style="{ color: themeColors.textPrimary }">💰 基础记账</text>
				</view>
				
				<view class="function-item">
					<view class="function-icon">💸</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">快速支出</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">直接跳转到支出记账页面，快速记录日常花费如餐饮、交通、购物等</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">💰</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">快速收入</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">直接跳转到收入记账页面，快速记录工资、奖金、投资收益等</text>
					</view>
				</view>
			</view>
			
			<!-- 数据分析 -->
			<view class="section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-title" :style="{ color: themeColors.textPrimary }">📊 数据分析</text>
				</view>
				
				<view class="function-item">
					<view class="function-icon">📈</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">统计报表</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">查看详细的收支统计，包含饼图、折线图等可视化数据，了解消费习惯</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">🔍</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">搜索记录</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">快速搜索历史记录，支持按金额、备注、分类等条件查找特定交易</text>
					</view>
				</view>
			</view>
			
			<!-- 账户管理 -->
			<view class="section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-title" :style="{ color: themeColors.textPrimary }">🏦 账户管理</text>
				</view>
				
				<view class="function-item">
					<view class="function-icon">🏦</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">账户管理</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">管理多个账户（现金、银行卡、支付宝等），查看余额，进行转账操作</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">📈</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">预算管理</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">为不同分类设置月度预算，实时监控支出进度，避免超支</text>
					</view>
				</view>
			</view>
			
			<!-- 目标管理 -->
			<view class="section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-title" :style="{ color: themeColors.textPrimary }">🎯 目标管理</text>
				</view>
				
				<view class="function-item">
					<view class="function-icon">🎯</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">储蓄目标</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">设置储蓄目标如"买车基金"、"旅行基金"，跟踪存钱进度</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">⏰</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">账单提醒</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">设置周期性提醒，如房租、水电费、信用卡还款等固定支出</text>
					</view>
				</view>
			</view>
			
			<!-- 进阶功能 -->
			<view class="section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-title" :style="{ color: themeColors.textPrimary }">🚀 进阶功能</text>
				</view>
				
				<view class="function-item">
					<view class="function-icon">👨‍👩‍👧‍👦</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">家庭账本</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">创建家庭共享账本，多人协作记账，管理家庭财务支出</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">📊</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">投资追踪</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">记录股票、基金等投资，追踪收益率和投资组合表现</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">💳</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">借债管理</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">管理借入借出记录，跟踪还款进度，避免遗忘重要债务</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">🧾</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">报销管理</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">管理公司报销申请，分类整理报销单据，提高工作效率</text>
					</view>
				</view>
				
				<view class="function-item">
					<view class="function-icon">🔄</view>
					<view class="function-info">
						<text class="function-name" :style="{ color: themeColors.textPrimary }">周期记账</text>
						<text class="function-desc" :style="{ color: themeColors.textSecondary }">设置周期性记账模板，自动生成重复性收支记录</text>
					</view>
				</view>
			</view>
			
			<!-- 使用技巧 -->
			<view class="section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-title" :style="{ color: themeColors.textPrimary }">💡 使用技巧</text>
				</view>
				
				<view class="tip-item">
					<text class="tip-title" :style="{ color: themeColors.primary }">快速记账</text>
					<text class="tip-content" :style="{ color: themeColors.textSecondary }">点击首页右下角的悬浮按钮可以最快速度开始记账</text>
				</view>
				
				<view class="tip-item">
					<text class="tip-title" :style="{ color: themeColors.primary }">语音记账</text>
					<text class="tip-content" :style="{ color: themeColors.textSecondary }">在记账页面使用语音功能，说"买菜花了30元"即可自动填写</text>
				</view>
				
				<view class="tip-item">
					<text class="tip-title" :style="{ color: themeColors.primary }">拍照记账</text>
					<text class="tip-content" :style="{ color: themeColors.textSecondary }">拍摄发票小票，系统会自动识别金额和商家信息</text>
				</view>
				
				<view class="tip-item">
					<text class="tip-title" :style="{ color: themeColors.primary }">数据备份</text>
					<text class="tip-content" :style="{ color: themeColors.textSecondary }">定期在设置页面进行数据同步，确保数据安全</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import ThemeManager from '@/utils/themeManager.js';

export default {
	data() {
		return {
			themeColors: ThemeManager.getColors()
		};
	},
	onLoad() {
		// 添加主题监听器
		ThemeManager.addListener(this.onThemeChange);
	},
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		// 主题变化回调
		onThemeChange(theme) {
			this.themeColors = theme.colors;
		}
	}
};
</script>

<style scoped>
.page {
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 头部样式 */
.header {
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
}

.back-btn {
	font-size: 36rpx;
	margin-right: 20rpx;
	font-weight: bold;
}

.header-title {
	font-size: 44rpx;
	font-weight: bold;
	flex: 1;
	text-align: center;
	margin-right: 56rpx; /* 平衡返回按钮的宽度 */
}

/* 内容区域 */
.content {
	padding: 0 40rpx;
}

.section {
	margin-bottom: 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-header {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 功能项样式 */
.function-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 30rpx;
	padding-bottom: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.function-item:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.function-icon {
	font-size: 48rpx;
	margin-right: 24rpx;
	margin-top: 8rpx;
	flex-shrink: 0;
}

.function-info {
	flex: 1;
}

.function-name {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.function-desc {
	font-size: 28rpx;
	line-height: 1.6;
	display: block;
}

/* 使用技巧样式 */
.tip-item {
	margin-bottom: 24rpx;
	padding: 24rpx;
	background: rgba(102, 126, 234, 0.05);
	border-radius: 16rpx;
	border-left: 6rpx solid #667eea;
}

.tip-item:last-child {
	margin-bottom: 0;
}

.tip-title {
	font-size: 30rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.tip-content {
	font-size: 26rpx;
	line-height: 1.5;
	display: block;
}
</style>
