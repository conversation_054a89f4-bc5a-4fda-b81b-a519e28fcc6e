/**
 * 应用配置文件
 * 包含API密钥和其他配置信息
 */

export const config = {
    // 硅基流动语音识别配置
    siliconFlow: {
        apiUrl: 'https://api.siliconflow.cn/v1/audio/transcriptions',
        apiKey: 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl',
        model: 'FunAudioLLM/SenseVoiceSmall'
    },
    
    // 语音识别配置
    speech: {
        // 录音质量设置
        audioConstraints: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
        },
        
        // 支持的音频格式
        supportedMimeTypes: [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/mp4',
            'audio/wav'
        ],
        
        // 最大录音时长（秒）
        maxRecordingDuration: 60,
        
        // 最小录音时长（秒）
        minRecordingDuration: 1
    },
    
    // 应用设置
    app: {
        name: '碎米记账',
        version: '1.0.0',
        
        // 默认设置
        defaults: {
            currency: '¥',
            language: 'zh-CN'
        }
    }
};

export default config;
