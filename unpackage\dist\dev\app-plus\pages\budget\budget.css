
.page[data-v-950a2c7a] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-950a2c7a] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-950a2c7a] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-950a2c7a], .header-action[data-v-950a2c7a] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-950a2c7a] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 预算总览 */
.budget-overview[data-v-950a2c7a] {
	background: white;
	margin: -0.625rem 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	display: flex;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.overview-item[data-v-950a2c7a] {
	flex: 1;
	text-align: center;
}
.overview-label[data-v-950a2c7a] {
	font-size: 0.8125rem;
	color: #666;
	display: block;
	margin-bottom: 0.3125rem;
}
.overview-value[data-v-950a2c7a] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.overview-value.expense[data-v-950a2c7a] {
	color: #ff4757;
}
.overview-value.positive[data-v-950a2c7a] {
	color: #4CAF50;
}
.overview-value.negative[data-v-950a2c7a] {
	color: #ff4757;
}

/* 预算进度 */
.budget-progress[data-v-950a2c7a] {
	background: white;
	margin: 0 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.progress-header[data-v-950a2c7a] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.625rem;
}
.progress-title[data-v-950a2c7a] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
}
.progress-percent[data-v-950a2c7a] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #667eea;
}
.progress-bar[data-v-950a2c7a] {
	height: 0.5rem;
	background: #f0f0f0;
	border-radius: 0.25rem;
	overflow: hidden;
}
.progress-fill[data-v-950a2c7a] {
	height: 100%;
	background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
	border-radius: 0.25rem;
	transition: width 0.3s ease;
}
.progress-fill.over-budget[data-v-950a2c7a] {
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
}

/* 预算列表 */
.budgets-section[data-v-950a2c7a] {
	margin: 0 1.25rem;
}
.section-title[data-v-950a2c7a] {
	padding: 0.625rem 0;
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.empty-state[data-v-950a2c7a] {
	background: white;
	border-radius: 0.625rem;
	padding: 2.5rem 1.25rem;
	text-align: center;
}
.empty-icon[data-v-950a2c7a] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-950a2c7a] {
	font-size: 0.875rem;
	color: #999;
}
.budgets-list[data-v-950a2c7a] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
}
.budget-item[data-v-950a2c7a] {
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.budget-item[data-v-950a2c7a]:last-child {
	border-bottom: none;
}
.budget-item[data-v-950a2c7a]:active {
	background-color: #f8f9fa;
}
.budget-header[data-v-950a2c7a] {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 0.5rem;
}
.budget-info[data-v-950a2c7a] {
	flex: 1;
}
.budget-name[data-v-950a2c7a] {
	font-size: 1rem;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.budget-category[data-v-950a2c7a] {
	font-size: 0.8125rem;
	color: #666;
}
.budget-amount[data-v-950a2c7a] {
	text-align: right;
}
.spent-amount[data-v-950a2c7a] {
	font-size: 1rem;
	font-weight: bold;
	color: #ff4757;
}
.total-amount[data-v-950a2c7a] {
	font-size: 0.875rem;
	color: #666;
}
.budget-progress-bar[data-v-950a2c7a] {
	height: 0.375rem;
	background: #f0f0f0;
	border-radius: 0.1875rem;
	overflow: hidden;
	margin-bottom: 0.375rem;
}
.budget-progress-fill[data-v-950a2c7a] {
	height: 100%;
	background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
	border-radius: 0.1875rem;
	transition: width 0.3s ease;
}
.budget-progress-fill.over-budget[data-v-950a2c7a] {
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
}
.budget-footer[data-v-950a2c7a] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.progress-text[data-v-950a2c7a] {
	font-size: 0.75rem;
	color: #666;
}
.remaining-text[data-v-950a2c7a] {
	font-size: 0.75rem;
	font-weight: 500;
}
.remaining-text.positive[data-v-950a2c7a] {
	color: #4CAF50;
}
.remaining-text.negative[data-v-950a2c7a] {
	color: #ff4757;
}

/* 弹窗样式 */
.popup-content[data-v-950a2c7a] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	max-height: 80vh;
}
.popup-header[data-v-950a2c7a] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-950a2c7a] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-950a2c7a] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-section[data-v-950a2c7a] {
	margin-bottom: 1.25rem;
}
.form-item[data-v-950a2c7a] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-950a2c7a] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-input[data-v-950a2c7a] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
}
.category-selector[data-v-950a2c7a] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
}
.selector-text[data-v-950a2c7a] {
	font-size: 0.875rem;
	color: #333;
}
.selector-text.placeholder[data-v-950a2c7a] {
	color: #999;
}
.selector-arrow[data-v-950a2c7a] {
	font-size: 0.875rem;
	color: #ccc;
}
.period-selector[data-v-950a2c7a] {
	display: flex;
	gap: 0.5rem;
}
.period-item[data-v-950a2c7a] {
	flex: 1;
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	text-align: center;
	transition: all 0.3s;
}
.period-item.active[data-v-950a2c7a] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.period-text[data-v-950a2c7a] {
	font-size: 0.875rem;
	font-weight: 500;
}
.popup-actions[data-v-950a2c7a] {
	display: flex;
	gap: 0.625rem;
}
.delete-btn[data-v-950a2c7a], .cancel-btn[data-v-950a2c7a], .save-btn[data-v-950a2c7a] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.delete-btn[data-v-950a2c7a] {
	background: #ff4757;
	color: white;
}
.cancel-btn[data-v-950a2c7a] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-950a2c7a] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

/* 分类列表 */
.categories-list[data-v-950a2c7a] {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.625rem;
	max-height: 12.5rem;
	overflow-y: auto;
}
.category-item[data-v-950a2c7a] {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	transition: background-color 0.2s;
}
.category-item[data-v-950a2c7a]:active {
	background-color: #e9ecef;
}
.category-icon[data-v-950a2c7a] {
	font-size: 1rem;
	margin-bottom: 0.25rem;
}
.category-name[data-v-950a2c7a] {
	font-size: 0.75rem;
	color: #333;
	text-align: center;
}
