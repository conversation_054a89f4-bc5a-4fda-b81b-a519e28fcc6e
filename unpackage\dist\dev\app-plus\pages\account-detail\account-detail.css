
.page[data-v-e1309c04] {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 3.75rem;
}

/* 头部样式 */
.header[data-v-e1309c04] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-content[data-v-e1309c04] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-e1309c04], .header-action[data-v-e1309c04] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-e1309c04] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 账户信息 */
.account-info[data-v-e1309c04] {
	margin: -0.625rem 1.25rem 1.25rem;
}
.account-card[data-v-e1309c04] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.account-header[data-v-e1309c04] {
	display: flex;
	align-items: center;
	margin-bottom: 0.9375rem;
}
.account-icon[data-v-e1309c04] {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 1.25rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.75rem;
}
.icon-text[data-v-e1309c04] {
	font-size: 1rem;
}
.account-details[data-v-e1309c04] {
	flex: 1;
}
.account-name[data-v-e1309c04] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.account-type[data-v-e1309c04] {
	font-size: 0.8125rem;
	color: #666;
}
.balance-section[data-v-e1309c04] {
	text-align: center;
	padding-top: 0.9375rem;
	border-top: 0.03125rem solid #f0f0f0;
}
.balance-label[data-v-e1309c04] {
	font-size: 0.8125rem;
	color: #666;
	display: block;
	margin-bottom: 0.375rem;
}
.balance-amount[data-v-e1309c04] {
	font-size: 1.5rem;
	font-weight: bold;
}
.balance-amount.positive[data-v-e1309c04] {
	color: #4CAF50;
}
.balance-amount.negative[data-v-e1309c04] {
	color: #F44336;
}
.balance-amount.zero[data-v-e1309c04] {
	color: #666;
}

/* 统计信息 */
.stats-section[data-v-e1309c04] {
	margin: 0 1.25rem 1.25rem;
}
.stats-card[data-v-e1309c04] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	display: flex;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.stat-item[data-v-e1309c04] {
	flex: 1;
	text-align: center;
}
.stat-value[data-v-e1309c04] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.stat-label[data-v-e1309c04] {
	font-size: 0.75rem;
	color: #666;
}
.stat-divider[data-v-e1309c04] {
	width: 0.03125rem;
	background: #f0f0f0;
	margin: 0 0.9375rem;
}

/* 筛选器 */
.filter-section[data-v-e1309c04] {
	margin: 0 1.25rem 0.9375rem;
}
.filter-scroll[data-v-e1309c04] {
	white-space: nowrap;
}
.filter-list[data-v-e1309c04] {
	display: flex;
	gap: 0.5rem;
	padding: 0 0 0.625rem;
}
.filter-item[data-v-e1309c04] {
	padding: 0.5rem 1rem;
	background: white;
	border-radius: 1.5625rem;
	font-size: 0.8125rem;
	color: #666;
	white-space: nowrap;
	transition: all 0.3s;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.filter-item.active[data-v-e1309c04] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

/* 交易记录 */
.records-section[data-v-e1309c04] {
	margin: 0 1.25rem;
}
.empty-state[data-v-e1309c04] {
	background: white;
	border-radius: 0.625rem;
	padding: 3.125rem 1.25rem;
	text-align: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.empty-icon[data-v-e1309c04] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-e1309c04] {
	font-size: 0.875rem;
	color: #666;
}
.records-list[data-v-e1309c04] {
	display: flex;
	flex-direction: column;
	gap: 0.9375rem;
}
.record-group[data-v-e1309c04] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.group-header[data-v-e1309c04] {
	background: #f8f9fa;
	padding: 0.75rem 1.25rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 0.03125rem solid #f0f0f0;
}
.group-date[data-v-e1309c04] {
	font-size: 0.875rem;
	font-weight: 500;
	color: #333;
}
.group-summary[data-v-e1309c04] {
	display: flex;
	gap: 0.625rem;
}
.group-income[data-v-e1309c04] {
	font-size: 0.75rem;
	color: #4CAF50;
}
.group-expense[data-v-e1309c04] {
	font-size: 0.75rem;
	color: #F44336;
}
.group-records[data-v-e1309c04] {
	display: flex;
	flex-direction: column;
}
.record-item[data-v-e1309c04] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.record-item[data-v-e1309c04]:last-child {
	border-bottom: none;
}
.record-item[data-v-e1309c04]:active {
	background-color: #f8f9fa;
}
.record-left[data-v-e1309c04] {
	display: flex;
	align-items: center;
	flex: 1;
}
.record-icon[data-v-e1309c04] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.75rem;
}
.record-emoji[data-v-e1309c04] {
	font-size: 0.75rem;
}
.record-info[data-v-e1309c04] {
	flex: 1;
}
.record-category[data-v-e1309c04] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.1875rem;
}
.record-note[data-v-e1309c04] {
	font-size: 0.75rem;
	color: #666;
	display: block;
	margin-bottom: 0.1875rem;
}
.record-time[data-v-e1309c04] {
	font-size: 0.6875rem;
	color: #999;
}
.record-right[data-v-e1309c04] {
	text-align: right;
}
.record-amount[data-v-e1309c04] {
	font-size: 0.875rem;
	font-weight: bold;
	display: block;
	margin-bottom: 0.1875rem;
}
.record-amount.expense[data-v-e1309c04] {
	color: #F44336;
}
.record-amount.income[data-v-e1309c04] {
	color: #4CAF50;
}
.record-amount.transfer[data-v-e1309c04] {
	color: #FF9800;
}
.record-balance[data-v-e1309c04] {
	font-size: 0.6875rem;
	color: #999;
}

/* 更多操作弹窗 */
.popup-overlay[data-v-e1309c04] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.actions-popup[data-v-e1309c04] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	width: 100%;
}
.action-item[data-v-e1309c04] {
	display: flex;
	align-items: center;
	padding: 0.9375rem 0;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.action-item[data-v-e1309c04]:last-child {
	border-bottom: none;
}
.action-item[data-v-e1309c04]:active {
	background-color: #f8f9fa;
}
.action-item.danger[data-v-e1309c04] {
	color: #ff4757;
}
.action-icon[data-v-e1309c04] {
	font-size: 1rem;
	margin-right: 0.75rem;
}
.action-text[data-v-e1309c04] {
	font-size: 1rem;
	font-weight: 500;
}

/* 悬浮按钮 */
.fab[data-v-e1309c04] {
	position: fixed;
	right: 1.25rem;
	bottom: 1.25rem;
	width: 3.75rem;
	height: 3.75rem;
	border-radius: 1.875rem;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 0.25rem 0.9375rem rgba(102, 126, 234, 0.3);
	z-index: 100;
}
.fab-icon[data-v-e1309c04] {
	font-size: 1.5rem;
	color: white;
	font-weight: bold;
}
