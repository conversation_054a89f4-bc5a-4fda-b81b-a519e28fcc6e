
.page[data-v-a222a3c7] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 弹窗样式 */
.popup-overlay[data-v-a222a3c7] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

/* 头部样式 */
.header[data-v-a222a3c7] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-a222a3c7] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-a222a3c7] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-a222a3c7] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 表单样式 */
.transfer-form[data-v-a222a3c7] {
	padding: 1.25rem;
}
.form-section[data-v-a222a3c7] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.section-title[data-v-a222a3c7] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
	margin-bottom: 0.625rem;
}

/* 账户选择器 */
.account-selector[data-v-a222a3c7] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	transition: background-color 0.2s;
}
.account-selector[data-v-a222a3c7]:active {
	background-color: #e9ecef;
}
.selected-account[data-v-a222a3c7] {
	display: flex;
	align-items: center;
	flex: 1;
}
.account-icon[data-v-a222a3c7] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.625rem;
}
.icon-text[data-v-a222a3c7] {
	font-size: 0.75rem;
}
.account-info[data-v-a222a3c7] {
	flex: 1;
}
.account-name[data-v-a222a3c7] {
	font-size: 0.875rem;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 0.125rem;
}
.account-balance[data-v-a222a3c7] {
	font-size: 0.75rem;
	color: #666;
}
.placeholder[data-v-a222a3c7] {
	flex: 1;
}
.placeholder-text[data-v-a222a3c7] {
	font-size: 0.875rem;
	color: #999;
}
.selector-arrow[data-v-a222a3c7] {
	font-size: 0.875rem;
	color: #ccc;
}

/* 金额输入 */
.amount-input-container[data-v-a222a3c7] {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0.625rem;
}
.currency[data-v-a222a3c7] {
	font-size: 1.5rem;
	color: #333;
	font-weight: bold;
	margin-right: 0.3125rem;
}
.amount-input[data-v-a222a3c7] {
	font-size: 1.875rem;
	color: #333;
	font-weight: bold;
	text-align: center;
	border: none;
	outline: none;
	min-width: 6.25rem;
	background: transparent;
}

/* 备注输入 */
.note-input[data-v-a222a3c7] {
	width: 100%;
	min-height: 3.75rem;
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	outline: none;
	font-size: 0.875rem;
	color: #333;
	resize: none;
}

/* 日期时间 */
.datetime-row[data-v-a222a3c7] {
	display: flex;
	gap: 0.625rem;
}
.datetime-item[data-v-a222a3c7] {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
}
.datetime-label[data-v-a222a3c7] {
	font-size: 0.875rem;
	color: #333;
}
.datetime-value[data-v-a222a3c7] {
	font-size: 0.875rem;
	color: #667eea;
}

/* 操作按钮 */
.action-buttons[data-v-a222a3c7] {
	padding: 0 1.25rem 1.25rem;
}
.transfer-btn[data-v-a222a3c7] {
	width: 100%;
	height: 3.125rem;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
}
.transfer-btn[data-v-a222a3c7]:active {
	transform: scale(0.98);
}

/* 弹窗样式 */
.popup-content[data-v-a222a3c7] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	max-height: 60vh;
}
.popup-header[data-v-a222a3c7] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-a222a3c7] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-a222a3c7] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.accounts-list[data-v-a222a3c7] {
	max-height: 12.5rem;
	overflow-y: auto;
}
.account-item[data-v-a222a3c7] {
	display: flex;
	align-items: center;
	padding: 0.9375rem 0.625rem;
	border-radius: 0.375rem;
	margin-bottom: 0.5rem;
	background: #f8f9fa;
	transition: background-color 0.2s;
}
.account-item[data-v-a222a3c7]:active {
	background-color: #e9ecef;
}
.account-item[data-v-a222a3c7]:last-child {
	margin-bottom: 0;
}
