
.page[data-v-61b111f5] {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header[data-v-61b111f5] {
	background: rgba(255, 255, 255, 0.1);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
	padding: 1.375rem 1rem 1rem;
}
.header-content[data-v-61b111f5] {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.back-btn[data-v-61b111f5], .add-btn[data-v-61b111f5] {
	color: white;
	font-size: 1.125rem;
	font-weight: bold;
	width: 1.875rem;
	text-align: center;
}
.header-title[data-v-61b111f5] {
	color: white;
	font-size: 1.125rem;
	font-weight: bold;
}
.stats-cards[data-v-61b111f5] {
	display: flex;
	padding: 1rem;
	gap: 0.5rem;
}
.stat-card[data-v-61b111f5] {
	flex: 1;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 0.5rem;
	padding: 0.75rem;
	text-align: center;
}
.stat-label[data-v-61b111f5] {
	display: block;
	font-size: 0.75rem;
	color: #666;
	margin-bottom: 0.25rem;
}
.stat-value[data-v-61b111f5] {
	display: block;
	font-size: 1rem;
	font-weight: bold;
}
.stat-value.lent[data-v-61b111f5] {
	color: #10b981;
}
.stat-value.borrowed[data-v-61b111f5] {
	color: #ef4444;
}
.filter-tabs[data-v-61b111f5] {
	display: flex;
	margin: 0 1rem 1rem;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 0.375rem;
	padding: 0.25rem;
}
.filter-tab[data-v-61b111f5] {
	flex: 1;
	text-align: center;
	padding: 0.5rem;
	border-radius: 0.25rem;
	transition: all 0.3s;
}
.filter-tab.active[data-v-61b111f5] {
	background: rgba(255, 255, 255, 0.2);
}
.filter-tab uni-text[data-v-61b111f5] {
	color: white;
	font-size: 0.875rem;
}
.debt-list[data-v-61b111f5] {
	padding: 0 1rem;
}
.debt-item[data-v-61b111f5] {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 0.5rem;
	padding: 0.75rem;
	margin-bottom: 0.5rem;
}
.debt-header[data-v-61b111f5] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.5rem;
}
.debt-person[data-v-61b111f5] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.person-name[data-v-61b111f5] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.debt-type[data-v-61b111f5] {
	font-size: 0.75rem;
	padding: 0.125rem 0.375rem;
	border-radius: 0.375rem;
	color: white;
}
.debt-type.lent[data-v-61b111f5] {
	background: #10b981;
}
.debt-type.borrowed[data-v-61b111f5] {
	background: #ef4444;
}
.debt-amount .amount[data-v-61b111f5] {
	font-size: 1rem;
	font-weight: bold;
}
.debt-amount .amount.lent[data-v-61b111f5] {
	color: #10b981;
}
.debt-amount .amount.borrowed[data-v-61b111f5] {
	color: #ef4444;
}
.debt-progress[data-v-61b111f5] {
	margin-bottom: 0.5rem;
}
.progress-info[data-v-61b111f5] {
	display: flex;
	justify-content: space-between;
	margin-bottom: 0.25rem;
}
.progress-text[data-v-61b111f5] {
	font-size: 0.75rem;
	color: #666;
}
.progress-percent[data-v-61b111f5] {
	font-size: 0.75rem;
	color: #10b981;
	font-weight: bold;
}
.progress-bar[data-v-61b111f5] {
	height: 0.25rem;
	background: #f3f4f6;
	border-radius: 0.125rem;
	overflow: hidden;
}
.progress-fill[data-v-61b111f5] {
	height: 100%;
	background: linear-gradient(90deg, #10b981, #34d399);
	transition: width 0.3s;
}
.debt-info[data-v-61b111f5] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.25rem;
}
.debt-date[data-v-61b111f5] {
	font-size: 0.75rem;
	color: #666;
}
.debt-status[data-v-61b111f5] {
	font-size: 0.75rem;
	padding: 0.125rem 0.375rem;
	border-radius: 0.375rem;
}
.debt-status.pending[data-v-61b111f5] {
	background: #fef3c7;
	color: #d97706;
}
.debt-status.completed[data-v-61b111f5] {
	background: #d1fae5;
	color: #065f46;
}
.debt-note[data-v-61b111f5] {
	font-size: 0.75rem;
	color: #666;
	line-height: 1.4;
}
.empty-state[data-v-61b111f5] {
	text-align: center;
	padding: 3.75rem 1rem;
}
.empty-icon[data-v-61b111f5] {
	font-size: 3.75rem;
	display: block;
	margin-bottom: 0.75rem;
}
.empty-text[data-v-61b111f5] {
	font-size: 1rem;
	color: white;
	font-weight: bold;
	display: block;
	margin-bottom: 0.375rem;
}
.empty-desc[data-v-61b111f5] {
	font-size: 0.875rem;
	color: rgba(255, 255, 255, 0.7);
	display: block;
}

/* 弹窗样式 */
.popup-overlay[data-v-61b111f5] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.popup-content[data-v-61b111f5] {
	background: white;
	border-radius: 0.5rem;
	width: 20rem;
	max-height: 80vh;
	overflow-y: auto;
}
.popup-header[data-v-61b111f5] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 1rem;
	border-bottom: 0.03125rem solid #f3f4f6;
}
.popup-title[data-v-61b111f5] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-61b111f5] {
	font-size: 1.5rem;
	color: #666;
	line-height: 1;
}
.form-group[data-v-61b111f5] {
	padding: 0.75rem 1rem;
	border-bottom: 0.03125rem solid #f3f4f6;
}
.form-label[data-v-61b111f5] {
	display: block;
	font-size: 0.875rem;
	color: #333;
	margin-bottom: 0.5rem;
}
.type-tabs[data-v-61b111f5] {
	display: flex;
	gap: 0.5rem;
}
.type-tab[data-v-61b111f5] {
	flex: 1;
	text-align: center;
	padding: 0.5rem;
	border: 0.0625rem solid #e5e7eb;
	border-radius: 0.25rem;
	transition: all 0.3s;
}
.type-tab.active[data-v-61b111f5] {
	border-color: #667eea;
	background: #667eea;
}
.type-tab uni-text[data-v-61b111f5] {
	font-size: 0.875rem;
	color: #666;
}
.type-tab.active uni-text[data-v-61b111f5] {
	color: white;
}
.form-input[data-v-61b111f5], .form-textarea[data-v-61b111f5] {
	width: 100%;
	padding: 0.5rem;
	border: 0.0625rem solid #e5e7eb;
	border-radius: 0.25rem;
	font-size: 0.875rem;
	box-sizing: border-box;
}
.picker-input[data-v-61b111f5] {
	padding: 0.5rem;
	border: 0.0625rem solid #e5e7eb;
	border-radius: 0.25rem;
	font-size: 0.875rem;
	color: #333;
}
.popup-actions[data-v-61b111f5] {
	display: flex;
	gap: 0.5rem;
	padding: 1rem;
}
.cancel-btn[data-v-61b111f5], .confirm-btn[data-v-61b111f5] {
	flex: 1;
	padding: 0.75rem;
	border-radius: 0.25rem;
	font-size: 0.875rem;
	border: none;
}
.cancel-btn[data-v-61b111f5] {
	background: #f3f4f6;
	color: #666;
}
.confirm-btn[data-v-61b111f5] {
	background: #667eea;
	color: white;
}
