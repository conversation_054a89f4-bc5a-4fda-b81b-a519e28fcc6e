<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">分类管理</text>
				<text class="header-action" @click="addCategory">+</text>
			</view>
		</view>
		
		<!-- 分类类型切换 -->
		<view class="type-tabs">
			<view 
				class="tab-item" 
				:class="{active: currentType === 'expense'}"
				@click="switchType('expense')"
			>
				<text class="tab-text">支出分类</text>
			</view>
			<view 
				class="tab-item" 
				:class="{active: currentType === 'income'}"
				@click="switchType('income')"
			>
				<text class="tab-text">收入分类</text>
			</view>
		</view>
		
		<!-- 分类列表 -->
		<view class="categories-section">
			<view v-if="currentCategories.length === 0" class="empty-state">
				<text class="empty-icon">📁</text>
				<text class="empty-text">暂无分类，点击右上角添加</text>
			</view>
			
			<view v-else class="categories-list">
				<view 
					v-for="category in currentCategories" 
					:key="category.id"
					class="category-item"
					@click="editCategory(category)"
				>
					<view class="category-left">
						<view class="category-icon" :style="{backgroundColor: category.color}">
							<text class="icon-text">{{category.icon}}</text>
						</view>
						<view class="category-info">
							<text class="category-name">{{category.name}}</text>
							<text class="category-group">{{getGroupName(category.groupId)}}</text>
						</view>
					</view>
					<view class="category-right">
						<text class="category-arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加/编辑分类弹窗 -->
		<view v-if="showPopup" class="popup-overlay" @click="closePopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingCategory ? '编辑分类' : '添加分类'}}</text>
					<text class="popup-close" @click="closePopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">分类名称</text>
						<input 
							class="form-input" 
							v-model="categoryForm.name" 
							placeholder="请输入分类名称"
							maxlength="10"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">选择图标</text>
						<view class="icon-selector">
							<view 
								v-for="icon in categoryIcons" 
								:key="icon"
								class="icon-item"
								:class="{active: categoryForm.icon === icon}"
								@click="selectIcon(icon)"
							>
								<text class="icon-text">{{icon}}</text>
							</view>
						</view>
					</view>
					
					<view class="form-item">
						<text class="form-label">选择颜色</text>
						<view class="color-selector">
							<view 
								v-for="color in categoryColors" 
								:key="color"
								class="color-item"
								:class="{active: categoryForm.color === color}"
								:style="{backgroundColor: color}"
								@click="selectColor(color)"
							></view>
						</view>
					</view>
				</view>
				
				<view class="popup-actions">
					<button v-if="editingCategory" class="delete-btn" @click="deleteCategory">删除</button>
					<button class="cancel-btn" @click="closePopup">取消</button>
					<button class="save-btn" @click="saveCategory">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createCategory } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			categories: [],
			currentType: 'expense',
			showPopup: false,
			editingCategory: null,
			categoryForm: {
				name: '',
				icon: '💰',
				color: '#4CAF50',
				groupId: 'daily'
			},
			categoryIcons: [
				'🍽️', '🚗', '🛍️', '🎬', '🏥', '📚', '🏠', '💡', '💸',
				'💰', '💼', '🎁', '🎯', '📱', '✈️', '🏨', '📎', '🎓'
			],
			categoryColors: [
				'#4CAF50', '#2196F3', '#FF9800', '#F44336', '#9C27B0',
				'#607D8B', '#795548', '#FF5722', '#3F51B5', '#009688'
			]
		};
	},
	computed: {
		currentCategories() {
			return this.categories.filter(cat => cat.type === this.currentType);
		}
	},
	onLoad() {
		this.loadCategories();
	},
	methods: {
		loadCategories() {
			this.categories = this.getDefaultCategories();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		switchType(type) {
			this.currentType = type;
		},
		
		addCategory() {
			this.editingCategory = null;
			this.resetForm();
			this.showPopup = true;
		},
		
		editCategory(category) {
			this.editingCategory = category;
			this.categoryForm = {
				name: category.name,
				icon: category.icon,
				color: category.color,
				groupId: category.groupId
			};
			this.showPopup = true;
		},
		
		closePopup() {
			this.showPopup = false;
		},
		
		resetForm() {
			this.categoryForm = {
				name: '',
				icon: '💰',
				color: '#4CAF50',
				groupId: 'daily'
			};
		},
		
		selectIcon(icon) {
			this.categoryForm.icon = icon;
		},
		
		selectColor(color) {
			this.categoryForm.color = color;
		},
		
		saveCategory() {
			if (!this.categoryForm.name.trim()) {
				uni.showToast({
					title: '请输入分类名称',
					icon: 'none'
				});
				return;
			}
			
			if (this.editingCategory) {
				// 编辑分类
				const index = this.categories.findIndex(c => c.id === this.editingCategory.id);
				if (index !== -1) {
					this.categories[index] = {
						...this.editingCategory,
						name: this.categoryForm.name.trim(),
						icon: this.categoryForm.icon,
						color: this.categoryForm.color,
						updatedAt: Date.now()
					};
				}
			} else {
				// 添加新分类
				const newCategory = createCategory({
					name: this.categoryForm.name.trim(),
					icon: this.categoryForm.icon,
					color: this.categoryForm.color,
					type: this.currentType,
					groupId: 'daily'
				});
				this.categories.push(newCategory);
			}
			
			this.closePopup();
			
			uni.showToast({
				title: this.editingCategory ? '分类已更新' : '分类已添加',
				icon: 'success'
			});
		},
		
		deleteCategory() {
			if (!this.editingCategory) return;
			
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个分类吗？',
				success: (res) => {
					if (res.confirm) {
						const index = this.categories.findIndex(c => c.id === this.editingCategory.id);
						if (index !== -1) {
							this.categories.splice(index, 1);
							this.closePopup();
							
							uni.showToast({
								title: '分类已删除',
								icon: 'success'
							});
						}
					}
				}
			});
		},
		
		getGroupName(groupId) {
			return '日常消费';
		},
		
		getDefaultCategories() {
			return [
				// 支出分类
				{ id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily', color: '#FF9800' },
				{ id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily', color: '#2196F3' },
				{ id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily', color: '#9C27B0' },
				{ id: 'entertainment', name: '娱乐', icon: '🎬', type: 'expense', groupId: 'daily', color: '#F44336' },
				{ id: 'healthcare', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily', color: '#4CAF50' },
				{ id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily', color: '#607D8B' },
				{ id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily', color: '#795548' },
				{ id: 'utilities', name: '水电费', icon: '💡', type: 'expense', groupId: 'daily', color: '#FF5722' },
				
				// 收入分类
				{ id: 'salary', name: '工资', icon: '💰', type: 'income', groupId: 'daily', color: '#4CAF50' },
				{ id: 'bonus', name: '奖金', icon: '🎁', type: 'income', groupId: 'daily', color: '#FF9800' },
				{ id: 'investment', name: '投资收益', icon: '📈', type: 'income', groupId: 'investment', color: '#2196F3' },
				{ id: 'other_income', name: '其他收入', icon: '💼', type: 'income', groupId: 'other', color: '#9C27B0' }
			];
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 类型切换 */
.type-tabs {
	background: white;
	display: flex;
	margin: 20rpx 40rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
	flex: 1;
	padding: 30rpx;
	text-align: center;
	background: #f8f9fa;
	transition: all 0.3s;
}

.tab-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 分类列表 */
.categories-section {
	margin: 0 40rpx;
}

.empty-state {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.categories-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.category-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.category-item:last-child {
	border-bottom: none;
}

.category-item:active {
	background-color: #f8f9fa;
}

.category-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.category-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.icon-text {
	font-size: 24rpx;
}

.category-info {
	flex: 1;
}

.category-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.category-group {
	font-size: 26rpx;
	color: #666;
}

.category-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 弹窗内容样式 */
.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

/* 图标选择器 */
.icon-selector {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	gap: 16rpx;
}

.icon-item {
	width: 80rpx;
	height: 80rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}

.icon-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	transform: scale(1.1);
}

.icon-item .icon-text {
	font-size: 32rpx;
}

/* 颜色选择器 */
.color-selector {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 16rpx;
}

.color-item {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	border: 4rpx solid transparent;
	transition: all 0.3s;
}

.color-item.active {
	border-color: #333;
	transform: scale(1.2);
}

/* 操作按钮 */
.popup-actions {
	display: flex;
	gap: 20rpx;
}

.delete-btn, .cancel-btn, .save-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.delete-btn {
	background: #ff4757;
	color: white;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
</style>
