
.page[data-v-4b036deb] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-4b036deb] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-4b036deb] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-4b036deb] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-4b036deb] {
	font-size: 1.125rem;
	font-weight: bold;
}
.header-actions[data-v-4b036deb] {
	display: flex;
	gap: 0.625rem;
}
.quick-btn[data-v-4b036deb] {
	font-size: 1rem;
	padding: 0.25rem 0.375rem;
	border-radius: 0.375rem;
	background: rgba(255, 255, 255, 0.2);
	transition: all 0.2s;
}
.quick-btn[data-v-4b036deb]:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.3);
}
.header-action[data-v-4b036deb] {
	font-size: 0.875rem;
}

/* 类型切换 */
.type-tabs[data-v-4b036deb] {
	background: white;
	margin: 0.625rem 1.25rem;
	border-radius: 0.625rem;
	display: flex;
	padding: 0.25rem;
}
.tab-item[data-v-4b036deb] {
	flex: 1;
	text-align: center;
	padding: 0.625rem;
	border-radius: 0.5rem;
	transition: all 0.3s;
}
.tab-item.active[data-v-4b036deb] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.tab-text[data-v-4b036deb] {
	font-size: 0.875rem;
	font-weight: 500;
}

/* 金额输入 */
.amount-section[data-v-4b036deb] {
	background: white;
	margin: 0 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.875rem 1.25rem;
	text-align: center;
}
.amount-input-container[data-v-4b036deb] {
	display: flex;
	align-items: center;
	justify-content: center;
}
.currency[data-v-4b036deb] {
	font-size: 1.5rem;
	color: #333;
	font-weight: bold;
	margin-right: 0.3125rem;
}
.amount-input[data-v-4b036deb] {
	font-size: 1.875rem;
	color: #333;
	font-weight: bold;
	text-align: center;
	border: none;
	outline: none;
	min-width: 6.25rem;
}

/* 分类区域 */
.category-section[data-v-4b036deb] {
	background: white;
	margin: 0 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
}
.section-title[data-v-4b036deb] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	margin-bottom: 0.9375rem;
}

/* 分组切换 */
.group-tabs[data-v-4b036deb] {
	display: flex;
	margin-bottom: 0.9375rem;
	background: #f8f9fa;
	border-radius: 0.5rem;
	padding: 0.1875rem;
}
.group-tab[data-v-4b036deb] {
	flex: 1;
	text-align: center;
	padding: 0.5rem;
	border-radius: 0.375rem;
	transition: all 0.3s;
}
.group-tab.active[data-v-4b036deb] {
	background: white;
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
}
.group-text[data-v-4b036deb] {
	font-size: 0.8125rem;
	color: #666;
}
.group-tab.active .group-text[data-v-4b036deb] {
	color: #333;
	font-weight: 500;
}

/* 分类网格 */
.categories-grid[data-v-4b036deb] {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 0.625rem;
}
.category-item[data-v-4b036deb] {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0.625rem;
	border-radius: 0.5rem;
	background: #f8f9fa;
	transition: all 0.3s;
}
.category-item.active[data-v-4b036deb] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.category-icon[data-v-4b036deb] {
	font-size: 1rem;
	margin-bottom: 0.25rem;
}
.category-name[data-v-4b036deb] {
	font-size: 0.75rem;
	text-align: center;
}
.category-item.active .category-name[data-v-4b036deb] {
	color: white;
}

/* 备注区域 */
.note-section[data-v-4b036deb] {
	background: white;
	margin: 0 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
}
.note-input[data-v-4b036deb] {
	width: 100%;
	min-height: 3.75rem;
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	outline: none;
	font-size: 0.875rem;
	color: #333;
	resize: none;
}

/* 标签区域 */
.tags-section[data-v-4b036deb] {
	background: white;
	margin: 0 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
}
.section-title[data-v-4b036deb] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.add-tag-btn[data-v-4b036deb] {
	font-size: 1rem;
	color: #667eea;
	font-weight: bold;
}
.selected-tags[data-v-4b036deb] {
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
	margin-top: 0.625rem;
}
.tag-chip[data-v-4b036deb] {
	display: flex;
	align-items: center;
	padding: 0.375rem 0.625rem;
	border-radius: 0.625rem;
	color: white;
	font-size: 0.75rem;
}
.tag-text[data-v-4b036deb] {
	margin-right: 0.25rem;
}
.tag-remove[data-v-4b036deb] {
	font-size: 0.875rem;
	font-weight: bold;
	opacity: 0.8;
}
.no-tags[data-v-4b036deb] {
	margin-top: 0.625rem;
	text-align: center;
	padding: 1.25rem;
}
.no-tags-text[data-v-4b036deb] {
	font-size: 0.8125rem;
	color: #999;
}

/* 标签选择弹窗 */
.popup-overlay[data-v-4b036deb] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.popup-content[data-v-4b036deb] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	max-height: 70vh;
	overflow-y: auto;
}
.popup-header[data-v-4b036deb] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-4b036deb] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-4b036deb] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.tags-grid[data-v-4b036deb] {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.625rem;
	margin-bottom: 1.25rem;
}
.tag-option[data-v-4b036deb] {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0.625rem;
	border-radius: 0.5rem;
	background: #f8f9fa;
	transition: all 0.3s;
}
.tag-option.selected[data-v-4b036deb] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.tag-icon[data-v-4b036deb] {
	width: 1.5625rem;
	height: 1.5625rem;
	border-radius: 0.78125rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 0.375rem;
}
.tag-icon .icon-text[data-v-4b036deb] {
	font-size: 0.625rem;
}
.tag-name[data-v-4b036deb] {
	font-size: 0.75rem;
	text-align: center;
}
.tag-option.selected .tag-name[data-v-4b036deb] {
	color: white;
}
.no-tags-hint[data-v-4b036deb] {
	text-align: center;
	padding: 1.875rem 1.25rem;
}
.hint-text[data-v-4b036deb] {
	font-size: 0.875rem;
	color: #999;
	display: block;
	margin-bottom: 0.9375rem;
}
.go-settings-btn[data-v-4b036deb] {
	padding: 0.625rem 1.25rem;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 0.625rem;
	font-size: 0.875rem;
}
.popup-actions[data-v-4b036deb] {
	padding-top: 0.625rem;
	border-top: 0.03125rem solid #f0f0f0;
}
.confirm-btn[data-v-4b036deb] {
	width: 100%;
	height: 2.75rem;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
}

/* 日期时间 */
.datetime-section[data-v-4b036deb] {
	background: white;
	margin: 0 1.25rem 0.625rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
}
.datetime-item[data-v-4b036deb] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem 0;
	border-bottom: 0.03125rem solid #f0f0f0;
}
.datetime-item[data-v-4b036deb]:last-child {
	border-bottom: none;
}
.datetime-label[data-v-4b036deb] {
	font-size: 0.875rem;
	color: #333;
}
.datetime-value[data-v-4b036deb] {
	font-size: 0.875rem;
	color: #667eea;
}

/* 操作按钮 */
.action-buttons[data-v-4b036deb] {
	padding: 0 1.25rem 1.25rem;
}
.save-btn[data-v-4b036deb] {
	width: 100%;
	height: 3.125rem;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
}
.save-btn[data-v-4b036deb]:active {
	transform: scale(0.98);
}

/* 查看模式操作 */
.view-actions[data-v-4b036deb] {
	display: flex;
	gap: 0.625rem;
	padding: 0 1.25rem 1.25rem;
}
.edit-btn[data-v-4b036deb] {
	flex: 1;
	height: 3.125rem;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
}
.delete-btn[data-v-4b036deb] {
	flex: 1;
	height: 3.125rem;
	background: #ff4757;
	color: white;
	border: none;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
}
.edit-btn[data-v-4b036deb]:active,
.delete-btn[data-v-4b036deb]:active {
	transform: scale(0.98);
}
