/**
 * 主题混入
 * 为页面提供主题相关的数据和方法
 */

import ThemeManager from '@/utils/themeManager.js';

export default {
	data() {
		return {
			themeColors: ThemeManager.getColors(),
			currentThemeName: ThemeManager.getCurrentThemeName()
		};
	},
	
	onLoad() {
		// 设置主题监听器
		ThemeManager.addListener(this.onThemeChange);
		
		// 应用当前主题
		this.applyCurrentTheme();
	},
	
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	
	methods: {
		/**
		 * 主题变化回调
		 */
		onThemeChange(theme) {
			this.themeColors = theme.colors;
			this.currentThemeName = theme.name;
			
			// 如果页面有自定义的主题变化处理，调用它
			if (typeof this.onCustomThemeChange === 'function') {
				this.onCustomThemeChange(theme);
			}
		},
		
		/**
		 * 应用当前主题
		 */
		applyCurrentTheme() {
			const theme = ThemeManager.getCurrentTheme();
			this.themeColors = theme.colors;
			this.currentThemeName = theme.name;
		},
		
		/**
		 * 获取主题颜色
		 */
		getThemeColor(colorKey) {
			return ThemeManager.getColor(colorKey);
		},
		
		/**
		 * 检查是否为深色主题
		 */
		isDarkTheme() {
			return ThemeManager.isDarkTheme();
		}
	}
};
