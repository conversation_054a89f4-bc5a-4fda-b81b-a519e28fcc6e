<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">{{account.name}}</text>
				<text class="header-action" @click="showMoreActions">⋯</text>
			</view>
		</view>
		
		<!-- 账户信息 -->
		<view class="account-info">
			<view class="account-card">
				<view class="account-header">
					<view class="account-icon" :style="{backgroundColor: account.color}">
						<text class="icon-text">{{account.icon}}</text>
					</view>
					<view class="account-details">
						<text class="account-name">{{account.name}}</text>
						<text class="account-type">{{getAccountTypeName(account.type)}}</text>
					</view>
				</view>
				
				<view class="balance-section">
					<text class="balance-label">当前余额</text>
					<text class="balance-amount" :class="getBalanceClass(account)">
						¥{{(account.balance || 0).toFixed(2)}}
					</text>
				</view>
			</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stats-card">
				<view class="stat-item">
					<text class="stat-value">{{(monthlyStats.income || 0).toFixed(2)}}</text>
					<text class="stat-label">本月收入</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-value">{{(monthlyStats.expense || 0).toFixed(2)}}</text>
					<text class="stat-label">本月支出</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-value">{{monthlyStats.count}}</text>
					<text class="stat-label">本月笔数</text>
				</view>
			</view>
		</view>
		
		<!-- 筛选器 -->
		<view class="filter-section">
			<scroll-view class="filter-scroll" scroll-x>
				<view class="filter-list">
					<view 
						v-for="filter in filterOptions" 
						:key="filter.value"
						class="filter-item"
						:class="{active: currentFilter === filter.value}"
						@click="changeFilter(filter.value)"
					>
						<text>{{filter.label}}</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 交易记录 -->
		<view class="records-section">
			<view v-if="filteredRecords.length === 0" class="empty-state">
				<text class="empty-icon">📝</text>
				<text class="empty-text">暂无交易记录</text>
			</view>
			
			<view v-else class="records-list">
				<view 
					v-for="group in groupedRecords" 
					:key="group.date"
					class="record-group"
				>
					<view class="group-header">
						<text class="group-date">{{group.date}}</text>
						<view class="group-summary">
							<text class="group-income" v-if="group.income > 0">
								+¥{{(group.income || 0).toFixed(2)}}
							</text>
							<text class="group-expense" v-if="group.expense > 0">
								-¥{{(group.expense || 0).toFixed(2)}}
							</text>
						</view>
					</view>
					
					<view class="group-records">
						<view 
							v-for="record in group.records" 
							:key="record.id"
							class="record-item"
							@click="viewRecord(record)"
						>
							<view class="record-left">
								<view class="record-icon" :style="{backgroundColor: getCategoryColor(record)}">
									<text class="record-emoji">{{getCategoryIcon(record)}}</text>
								</view>
								<view class="record-info">
									<text class="record-category">{{getCategoryName(record)}}</text>
									<text class="record-note" v-if="record.note">{{record.note}}</text>
									<text class="record-time">{{formatTime(record.date)}}</text>
								</view>
							</view>
							
							<view class="record-right">
								<text class="record-amount" :class="record.type">
									{{record.type === 'expense' ? '-' : '+'}}¥{{(record.amount || 0).toFixed(2)}}
								</text>
								<text class="record-balance">余额: ¥{{calculateBalance(record)}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 更多操作弹窗 -->
		<view v-if="showActionsPopup" class="popup-overlay" @click="hideMoreActions">
			<view class="actions-popup" @click.stop>
				<view class="action-item" @click="editAccount">
					<text class="action-icon">✏️</text>
					<text class="action-text">编辑账户</text>
				</view>
				<view class="action-item" @click="adjustBalance">
					<text class="action-icon">⚖️</text>
					<text class="action-text">余额调整</text>
				</view>
				<view class="action-item" @click="transferMoney">
					<text class="action-icon">🔄</text>
					<text class="action-text">转账</text>
				</view>
				<view class="action-item" @click="exportRecords">
					<text class="action-icon">📤</text>
					<text class="action-text">导出明细</text>
				</view>
				<view class="action-item danger" @click="deleteAccount">
					<text class="action-icon">🗑️</text>
					<text class="action-text">删除账户</text>
				</view>
			</view>
		</view>
		
		<!-- 悬浮按钮 -->
		<view class="fab" @click="quickRecord">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			account: {},
			records: [],
			categories: [],
			currentFilter: 'all',
			showActionsPopup: false,
			filterOptions: [
				{ label: '全部', value: 'all' },
				{ label: '收入', value: 'income' },
				{ label: '支出', value: 'expense' },
				{ label: '转账', value: 'transfer' },
				{ label: '本月', value: 'month' },
				{ label: '本周', value: 'week' }
			]
		};
	},
	
	computed: {
		// 过滤后的记录
		filteredRecords() {
			let filtered = this.records.filter(record => 
				record.accountId === this.account.id || record.toAccountId === this.account.id
			);
			
			const now = new Date();
			const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
			const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
			
			switch (this.currentFilter) {
				case 'income':
					return filtered.filter(r => r.type === 'income');
				case 'expense':
					return filtered.filter(r => r.type === 'expense');
				case 'transfer':
					return filtered.filter(r => r.type === 'transfer');
				case 'month':
					return filtered.filter(r => new Date(r.date) >= startOfMonth);
				case 'week':
					return filtered.filter(r => new Date(r.date) >= startOfWeek);
				default:
					return filtered;
			}
		},
		
		// 按日期分组的记录
		groupedRecords() {
			const groups = {};
			
			this.filteredRecords.forEach(record => {
				const date = new Date(record.date);
				const dateKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
				
				if (!groups[dateKey]) {
					groups[dateKey] = {
						date: this.formatDate(date),
						records: [],
						income: 0,
						expense: 0
					};
				}
				
				groups[dateKey].records.push(record);
				
				// 计算当日收支
				if (record.type === 'income' || (record.type === 'transfer' && record.toAccountId === this.account.id)) {
					groups[dateKey].income += record.amount || 0;
				} else if (record.type === 'expense' || (record.type === 'transfer' && record.accountId === this.account.id)) {
					groups[dateKey].expense += record.amount || 0;
				}
			});
			
			// 转换为数组并按日期排序
			return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date));
		},
		
		// 本月统计
		monthlyStats() {
			if (!this.account || !this.account.id) {
				return { income: 0, expense: 0, count: 0 };
			}
			
			const now = new Date();
			const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
			
			const monthlyRecords = this.records.filter(record => {
				const recordDate = new Date(record.date);
				return recordDate >= startOfMonth && 
					   (record.accountId === this.account.id || record.toAccountId === this.account.id);
			});
			
			let income = 0;
			let expense = 0;
			
			monthlyRecords.forEach(record => {
				if (record.type === 'income' || (record.type === 'transfer' && record.toAccountId === this.account.id)) {
					income += record.amount || 0;
				} else if (record.type === 'expense' || (record.type === 'transfer' && record.accountId === this.account.id)) {
					expense += record.amount || 0;
				}
			});
			
			return {
				income,
				expense,
				count: monthlyRecords.length
			};
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.loadAccountDetail(options.id);
		} else if (options.accountId) {
			this.loadAccountDetail(options.accountId);
		}
	},
	
	methods: {
		loadAccountDetail(accountId) {
			const accounts = StorageManager.getAccounts();
			this.account = accounts.find(a => a.id === accountId) || {
				id: accountId,
				name: '未知账户',
				balance: 0,
				icon: '💰',
				color: '#4CAF50',
				type: 'cash'
			};
			this.records = StorageManager.getRecords();
			this.categories = StorageManager.getCategories();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		// 获取账户类型名称
		getAccountTypeName(type) {
			const typeMap = {
				'cash': '现金',
				'bank_card': '储蓄卡',
				'credit_card': '信用卡',
				'alipay': '支付宝',
				'wechat': '微信支付'
			};
			return typeMap[type] || type;
		},
		
		// 获取余额样式类
		getBalanceClass(account) {
			const balance = account.balance || 0;
			if (balance > 0) return 'positive';
			if (balance < 0) return 'negative';
			return 'zero';
		},
		
		// 切换筛选器
		changeFilter(filter) {
			this.currentFilter = filter;
		},
		
		// 获取分类信息
		getCategoryName(record) {
			if (record.type === 'transfer') {
				if (record.accountId === this.account.id) {
					return `转出到${this.getAccountName(record.toAccountId)}`;
				} else {
					return `从${this.getAccountName(record.accountId)}转入`;
				}
			}
			
			const category = this.categories.find(c => c.id === record.categoryId);
			return category ? category.name : '未分类';
		},
		
		getCategoryIcon(record) {
			if (record.type === 'transfer') {
				return '🔄';
			}
			
			const category = this.categories.find(c => c.id === record.categoryId);
			return category ? category.icon : '💰';
		},
		
		getCategoryColor(record) {
			if (record.type === 'transfer') {
				return '#FF9800';
			}
			
			const category = this.categories.find(c => c.id === record.categoryId);
			return category ? category.color : '#4CAF50';
		},
		
		// 获取账户名称
		getAccountName(accountId) {
			const accounts = StorageManager.getAccounts();
			const account = accounts.find(a => a.id === accountId);
			return account ? account.name : '未知账户';
		},
		
		// 格式化日期
		formatDate(date) {
			const today = new Date();
			const yesterday = new Date(today);
			yesterday.setDate(yesterday.getDate() - 1);
			
			if (date.toDateString() === today.toDateString()) {
				return '今天';
			} else if (date.toDateString() === yesterday.toDateString()) {
				return '昨天';
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		
		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp);
			return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
		},
		
		// 计算交易后余额（模拟）
		calculateBalance(record) {
			// 这里应该根据交易时间顺序计算实际余额
			// 简化处理，返回当前余额
			return (this.account.balance || 0).toFixed(2);
		},
		
		// 查看记录详情
		viewRecord(record) {
			uni.navigateTo({
				url: `/pages/add-record/add-record?id=${record.id}&mode=view`
			});
		},
		
		// 显示更多操作
		showMoreActions() {
			this.showActionsPopup = true;
		},
		
		// 隐藏更多操作
		hideMoreActions() {
			this.showActionsPopup = false;
		},
		
		// 编辑账户
		editAccount() {
			this.hideMoreActions();
			uni.navigateTo({
				url: `/pages/accounts/accounts?edit=${this.account.id}`
			});
		},
		
		// 余额调整
		adjustBalance() {
			this.hideMoreActions();
			uni.showModal({
				title: '余额调整',
				content: '此功能将跳转到余额调整页面',
				success: (res) => {
					if (res.confirm) {
						// 实现余额调整功能
					}
				}
			});
		},
		
		// 转账
		transferMoney() {
			this.hideMoreActions();
			uni.navigateTo({
				url: `/pages/transfer/transfer?fromAccount=${this.account.id}`
			});
		},
		
		// 导出记录
		exportRecords() {
			this.hideMoreActions();
			
			// 生成CSV格式的数据
			let csvContent = '日期,类型,分类,金额,备注\n';
			
			this.filteredRecords.forEach(record => {
				const date = new Date(record.date).toLocaleDateString();
				const type = record.type === 'expense' ? '支出' : record.type === 'income' ? '收入' : '转账';
				const category = this.getCategoryName(record);
				const amount = record.amount.toFixed(2);
				const note = record.note || '';
				
				csvContent += `${date},${type},${category},${amount},${note}\n`;
			});
			
			// 复制到剪贴板
			uni.setClipboardData({
				data: csvContent,
				success: () => {
					uni.showToast({
						title: '明细已复制到剪贴板',
						icon: 'success'
					});
				}
			});
		},
		
		// 删除账户
		deleteAccount() {
			this.hideMoreActions();
			
			uni.showModal({
				title: '删除账户',
				content: '删除账户将同时删除相关的交易记录，此操作不可恢复。确定要删除吗？',
				confirmColor: '#ff4757',
				success: (res) => {
					if (res.confirm) {
						// 实现删除账户功能
						this.performDeleteAccount();
					}
				}
			});
		},
		
		// 执行删除账户
		performDeleteAccount() {
			try {
				// 删除相关记录
				let records = StorageManager.getRecords();
				records = records.filter(r => r.accountId !== this.account.id && r.toAccountId !== this.account.id);
				StorageManager.saveRecords(records);
				
				// 删除账户
				let accounts = StorageManager.getAccounts();
				accounts = accounts.filter(a => a.id !== this.account.id);
				StorageManager.saveAccounts(accounts);
				
				uni.showToast({
					title: '账户已删除',
					icon: 'success'
				});
				
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				
			} catch (error) {
				console.error('删除账户失败:', error);
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				});
			}
		},
		
		// 快速记账
		quickRecord() {
			uni.navigateTo({
				url: `/pages/add-record/add-record?accountId=${this.account.id}`
			});
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 账户信息 */
.account-info {
	margin: -20rpx 40rpx 40rpx;
}

.account-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.account-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.account-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
}

.account-details {
	flex: 1;
}

.account-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.account-type {
	font-size: 26rpx;
	color: #666;
}

.balance-section {
	text-align: center;
	padding-top: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.balance-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 12rpx;
}

.balance-amount {
	font-size: 48rpx;
	font-weight: bold;
}

.balance-amount.positive {
	color: #4CAF50;
}

.balance-amount.negative {
	color: #F44336;
}

.balance-amount.zero {
	color: #666;
}

/* 统计信息 */
.stats-section {
	margin: 0 40rpx 40rpx;
}

.stats-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.stat-divider {
	width: 1rpx;
	background: #f0f0f0;
	margin: 0 30rpx;
}

/* 筛选器 */
.filter-section {
	margin: 0 40rpx 30rpx;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-list {
	display: flex;
	gap: 16rpx;
	padding: 0 0 20rpx;
}

.filter-item {
	padding: 16rpx 32rpx;
	background: white;
	border-radius: 50rpx;
	font-size: 26rpx;
	color: #666;
	white-space: nowrap;
	transition: all 0.3s;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.filter-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

/* 交易记录 */
.records-section {
	margin: 0 40rpx;
}

.empty-state {
	background: white;
	border-radius: 20rpx;
	padding: 100rpx 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
}

.records-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.record-group {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.group-header {
	background: #f8f9fa;
	padding: 24rpx 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.group-date {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.group-summary {
	display: flex;
	gap: 20rpx;
}

.group-income {
	font-size: 24rpx;
	color: #4CAF50;
}

.group-expense {
	font-size: 24rpx;
	color: #F44336;
}

.group-records {
	display: flex;
	flex-direction: column;
}

.record-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.record-item:last-child {
	border-bottom: none;
}

.record-item:active {
	background-color: #f8f9fa;
}

.record-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.record-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.record-emoji {
	font-size: 24rpx;
}

.record-info {
	flex: 1;
}

.record-category {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 6rpx;
}

.record-note {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 6rpx;
}

.record-time {
	font-size: 22rpx;
	color: #999;
}

.record-right {
	text-align: right;
}

.record-amount {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 6rpx;
}

.record-amount.expense {
	color: #F44336;
}

.record-amount.income {
	color: #4CAF50;
}

.record-amount.transfer {
	color: #FF9800;
}

.record-balance {
	font-size: 22rpx;
	color: #999;
}

/* 更多操作弹窗 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.actions-popup {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	width: 100%;
}

.action-item {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.action-item:last-child {
	border-bottom: none;
}

.action-item:active {
	background-color: #f8f9fa;
}

.action-item.danger {
	color: #ff4757;
}

.action-icon {
	font-size: 32rpx;
	margin-right: 24rpx;
}

.action-text {
	font-size: 32rpx;
	font-weight: 500;
}

/* 悬浮按钮 */
.fab {
	position: fixed;
	right: 40rpx;
	bottom: 40rpx;
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
	z-index: 100;
}

.fab-icon {
	font-size: 48rpx;
	color: white;
	font-weight: bold;
}
</style>
