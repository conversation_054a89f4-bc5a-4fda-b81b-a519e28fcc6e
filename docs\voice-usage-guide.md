# 语音记账使用指南

## 🎯 功能概述

碎米记账的语音识别功能可以帮助您快速记录收支信息，只需说出记账内容，系统会自动识别并解析出金额、分类和备注信息。

## 📱 支持的表达方式

### 💰 金额表达
系统支持多种金额表达方式：

#### ✅ 推荐表达
- `30元` - 标准表达
- `50块钱` - 口语化表达
- `100块` - 简化表达
- `花了25元` - 动作+金额
- `费用80块` - 用途+金额

#### ✅ 收入表达
- `工资收入5000` - 明确收入类型
- `收入1000元` - 简单收入
- `奖金500块` - 具体收入来源

### 🏷️ 分类识别

系统会根据关键词自动识别分类：

#### 餐饮类
- `买菜花了30块钱` → 餐饮，30元
- `午餐50元` → 餐饮，50元
- `咖啡15块` → 餐饮，15元
- `水果20元` → 餐饮，20元

#### 交通类
- `打车费用25块` → 交通，25元
- `加油200元` → 交通，200元
- `停车费10块` → 交通，10元
- `地铁5元` → 交通，5元

#### 购物类
- `买衣服150元` → 购物，150元
- `买书花了80元` → 购物，80元
- `购物300块` → 购物，300元

#### 娱乐类
- `电影票35元` → 娱乐，35元
- `游戏充值100块` → 娱乐，100元
- `健身卡500元` → 娱乐，500元

#### 生活类
- `房租2000块` → 生活，2000元
- `电费100元` → 生活，100元
- `理发30块钱` → 生活，30元
- `网费80元` → 生活，80元

#### 收入类
- `工资收入5000` → 工资，5000元
- `奖金收入1000` → 奖金，1000元
- `兼职收入500` → 其他收入，500元
- `红包收入200` → 奖金，200元

### 📅 日期识别

系统支持多种日期表达方式：

#### ✅ 绝对日期
- `2024年1月15日买书80元` → 2024-01-15
- `1月20日午餐50元` → 当年1月20日
- `2024-01-15买菜30元` → 2024-01-15
- `1/15买衣服100元` → 当年1月15日

#### ✅ 相对日期
- `昨天买菜30元` → 昨天的日期
- `今天午餐50元` → 今天的日期
- `明天电影票35元` → 明天的日期
- `前天打车25元` → 前天的日期
- `后天聚餐200元` → 后天的日期

#### ✅ 星期表达
- `上周一买书80元` → 上周一的日期
- `这周三理发30元` → 本周三的日期
- `下周五聚餐200元` → 下周五的日期
- `周六购物150元` → 本周六的日期
- `星期天电影35元` → 本周日的日期

#### ✅ 月份表达
- `上个月房租2000元` → 上月同日期
- `这个月电费100元` → 本月同日期
- `下个月保险500元` → 下月同日期

#### ⚠️ 注意事项
- 如果没有说明具体日期，系统会使用当前时间
- 日期识别支持中文和数字混合表达
- 系统会自动处理日期的合法性检查

## 🎤 使用技巧

### 1. 清晰发音
- 语速适中，发音清晰
- 避免方言，使用普通话
- 在安静环境中录音

### 2. 完整表达
- 包含金额和用途：`买菜花了30块钱`
- 明确收支类型：`工资收入5000`
- 避免过于简单：`30` → `午餐30元`

### 3. 常用句式
```
支出记录：
- [用途] + [金额] → "买菜30元"
- "花了" + [金额] + [用途] → "花了50块买书"
- [用途] + "费用" + [金额] → "交通费用25块"
- [日期] + [用途] + [金额] → "昨天买菜30元"

收入记录：
- [来源] + "收入" + [金额] → "工资收入5000"
- "收入" + [金额] + [来源] → "收入1000奖金"
- [日期] + [来源] + "收入" + [金额] → "今天工资收入5000"

带日期记录：
- [日期] + [记录内容] → "昨天午餐50元"
- [记录内容] + [日期] → "买书80元是前天的"
- [具体日期] + [记录内容] → "2024年1月15日买菜30元"
```

## 🔧 故障排除

### 问题1：无法识别金额
**症状**：提示"无法识别记账信息"

**解决方案**：
- 确保包含数字和金额单位（元/块/钱）
- 尝试：`午餐50元` 而不是 `午餐`
- 检查发音是否清晰

### 问题2：分类识别错误
**症状**：分类不准确

**解决方案**：
- 使用更明确的关键词
- 例如：`买菜30元` 而不是 `花了30`
- 可以手动编辑分类

### 问题3：录音失败
**症状**：无法开始录音

**解决方案**：
- 检查麦克风权限
- 确保使用HTTPS协议（H5环境）
- 尝试刷新页面重新授权

### 问题4：识别结果为空
**症状**：录音后没有识别结果

**解决方案**：
- 检查网络连接
- 确保录音时长足够（至少1秒）
- 避免环境噪音过大

## 📊 测试功能

### 语音解析测试页面
访问 `pages/test/parse-test` 可以：
- 测试不同的语音表达
- 查看解析结果
- 调试识别逻辑

### 测试示例
在测试页面中尝试以下表达：
```
支出测试：
- 买菜花了30块钱
- 午餐50元
- 打车费用25块
- 电影票35元
- 房租2000块

收入测试：
- 工资收入5000
- 奖金收入1000
- 兼职收入500
- 红包收入200

边界测试：
- 30元
- 花了50
- 收入1000
```

## 🌟 最佳实践

### 1. 标准化表达
建立个人的语音记账习惯：
- 固定的表达模式
- 常用的分类关键词
- 清晰的金额表达

### 2. 及时确认
- 录音后检查识别结果
- 必要时手动编辑
- 确认分类和金额正确

### 3. 环境优化
- 选择安静的环境
- 保持适当的录音距离
- 避免背景噪音

### 4. 渐进学习
- 从简单表达开始
- 逐步尝试复杂句式
- 总结个人使用经验

## 🔮 功能规划

### 即将推出
- 更多语言支持
- 自定义分类关键词
- 语音命令扩展
- 离线识别能力

### 长期规划
- 个性化语音模型
- 智能学习用户习惯
- 多轮对话支持
- 语音查询功能

## 💡 小贴士

1. **快速记账**：使用语音功能可以比手动输入快3-5倍
2. **准确性**：清晰的表达比快速的表达更重要
3. **习惯养成**：坚持使用会让识别更准确
4. **及时记录**：消费后立即记录，避免遗忘
5. **定期检查**：定期查看记录，确保准确性

## 📞 技术支持

如果遇到问题：
1. 查看控制台日志
2. 使用测试页面诊断
3. 检查网络和权限设置
4. 尝试不同的表达方式

记住：语音识别是一个学习过程，多练习会让体验更好！
