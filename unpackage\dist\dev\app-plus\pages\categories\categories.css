
.page[data-v-1d71fdc2] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 弹窗样式 */
.popup-overlay[data-v-1d71fdc2] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

/* 头部样式 */
.header[data-v-1d71fdc2] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-1d71fdc2] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-1d71fdc2], .header-action[data-v-1d71fdc2] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-1d71fdc2] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 类型切换 */
.type-tabs[data-v-1d71fdc2] {
	background: white;
	display: flex;
	margin: 0.625rem 1.25rem;
	border-radius: 0.625rem;
	overflow: hidden;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.tab-item[data-v-1d71fdc2] {
	flex: 1;
	padding: 0.9375rem;
	text-align: center;
	background: #f8f9fa;
	transition: all 0.3s;
}
.tab-item.active[data-v-1d71fdc2] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.tab-text[data-v-1d71fdc2] {
	font-size: 0.875rem;
	font-weight: 500;
}

/* 分类列表 */
.categories-section[data-v-1d71fdc2] {
	margin: 0 1.25rem;
}
.empty-state[data-v-1d71fdc2] {
	background: white;
	border-radius: 0.625rem;
	padding: 2.5rem 1.25rem;
	text-align: center;
}
.empty-icon[data-v-1d71fdc2] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-1d71fdc2] {
	font-size: 0.875rem;
	color: #999;
}
.categories-list[data-v-1d71fdc2] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
}
.category-item[data-v-1d71fdc2] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.category-item[data-v-1d71fdc2]:last-child {
	border-bottom: none;
}
.category-item[data-v-1d71fdc2]:active {
	background-color: #f8f9fa;
}
.category-left[data-v-1d71fdc2] {
	display: flex;
	align-items: center;
	flex: 1;
}
.category-icon[data-v-1d71fdc2] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.625rem;
}
.icon-text[data-v-1d71fdc2] {
	font-size: 0.75rem;
}
.category-info[data-v-1d71fdc2] {
	flex: 1;
}
.category-name[data-v-1d71fdc2] {
	font-size: 1rem;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.category-group[data-v-1d71fdc2] {
	font-size: 0.8125rem;
	color: #666;
}
.category-arrow[data-v-1d71fdc2] {
	font-size: 0.875rem;
	color: #ccc;
}

/* 弹窗内容样式 */
.popup-content[data-v-1d71fdc2] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	max-height: 80vh;
	overflow-y: auto;
}
.popup-header[data-v-1d71fdc2] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-1d71fdc2] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-1d71fdc2] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-section[data-v-1d71fdc2] {
	margin-bottom: 1.25rem;
}
.form-item[data-v-1d71fdc2] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-1d71fdc2] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-input[data-v-1d71fdc2] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
}

/* 图标选择器 */
.icon-selector[data-v-1d71fdc2] {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	gap: 0.5rem;
}
.icon-item[data-v-1d71fdc2] {
	width: 2.5rem;
	height: 2.5rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}
.icon-item.active[data-v-1d71fdc2] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	transform: scale(1.1);
}
.icon-item .icon-text[data-v-1d71fdc2] {
	font-size: 1rem;
}

/* 颜色选择器 */
.color-selector[data-v-1d71fdc2] {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 0.5rem;
}
.color-item[data-v-1d71fdc2] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	border: 0.125rem solid transparent;
	transition: all 0.3s;
}
.color-item.active[data-v-1d71fdc2] {
	border-color: #333;
	transform: scale(1.2);
}

/* 操作按钮 */
.popup-actions[data-v-1d71fdc2] {
	display: flex;
	gap: 0.625rem;
}
.delete-btn[data-v-1d71fdc2], .cancel-btn[data-v-1d71fdc2], .save-btn[data-v-1d71fdc2] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.delete-btn[data-v-1d71fdc2] {
	background: #ff4757;
	color: white;
}
.cancel-btn[data-v-1d71fdc2] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-1d71fdc2] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
