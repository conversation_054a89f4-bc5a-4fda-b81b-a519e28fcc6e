<template>
  <view class="test-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="iconfont icon-back">‹</text>
        </view>
        <view class="navbar-title">安全功能测试</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <view class="content">
      <!-- 状态显示 -->
      <view class="status-section">
        <view class="section-title">当前状态</view>
        <view class="status-card">
          <view class="status-item">
            <text class="status-label">PIN码状态：</text>
            <text class="status-value" :class="{ success: hasPinCode, error: !hasPinCode }">
              {{ hasPinCode ? '已设置' : '未设置' }}
            </text>
          </view>
          <view class="status-item">
            <text class="status-label">指纹解锁：</text>
            <text class="status-value" :class="{ success: fingerprintEnabled, error: !fingerprintEnabled }">
              {{ fingerprintEnabled ? '已启用' : '未启用' }}
            </text>
          </view>
          <view class="status-item">
            <text class="status-label">安全保护：</text>
            <text class="status-value" :class="{ success: securityEnabled, error: !securityEnabled }">
              {{ securityEnabled ? '已启用' : '未启用' }}
            </text>
          </view>
          <view class="status-item">
            <text class="status-label">需要解锁：</text>
            <text class="status-value" :class="{ warning: needsUnlock, success: !needsUnlock }">
              {{ needsUnlock ? '是' : '否' }}
            </text>
          </view>
        </view>
      </view>

      <!-- 测试功能 -->
      <view class="test-section">
        <view class="section-title">测试功能</view>
        
        <button class="test-btn" @click="testPinValidation">
          测试PIN码验证
        </button>
        
        <button class="test-btn" @click="testFingerprintSupport">
          测试指纹支持检测
        </button>
        
        <button class="test-btn" @click="testFingerprintEnrolled">
          测试指纹录入检测
        </button>
        
        <button class="test-btn" @click="testFingerprintAuth">
          测试指纹验证
        </button>
        
        <button class="test-btn" @click="simulateAutoLock">
          模拟自动锁定
        </button>
        
        <button class="test-btn" @click="resetLastUnlockTime">
          重置解锁时间
        </button>
        
        <button class="test-btn danger" @click="resetAllSecurity">
          重置所有安全设置
        </button>
      </view>

      <!-- 测试结果 -->
      <view class="result-section">
        <view class="section-title">测试结果</view>
        <view class="result-card">
          <scroll-view class="result-content" scroll-y>
            <view v-for="(log, index) in testLogs" :key="index" class="log-item">
              <text class="log-time">{{ log.time }}</text>
              <text class="log-message" :class="log.type">{{ log.message }}</text>
            </view>
            <view v-if="testLogs.length === 0" class="no-logs">
              暂无测试记录
            </view>
          </scroll-view>
          <button class="clear-btn" @click="clearLogs">清除日志</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SecurityManager from '@/utils/securityManager.js';

export default {
  name: 'SecurityTest',
  data() {
    return {
      hasPinCode: false,
      fingerprintEnabled: false,
      securityEnabled: false,
      needsUnlock: false,
      testLogs: []
    };
  },
  onLoad() {
    this.refreshStatus();
  },
  methods: {
    /**
     * 刷新状态
     */
    refreshStatus() {
      try {
        this.hasPinCode = SecurityManager.hasPinCode();
        this.fingerprintEnabled = SecurityManager.isFingerprintEnabled();
        this.securityEnabled = SecurityManager.isSecurityEnabled();
        this.needsUnlock = SecurityManager.needsUnlock();
        
        this.addLog('状态已刷新', 'info');
      } catch (error) {
        this.addLog('刷新状态失败: ' + error.message, 'error');
      }
    },

    /**
     * 测试PIN码验证
     */
    async testPinValidation() {
      try {
        if (!this.hasPinCode) {
          this.addLog('请先设置PIN码', 'warning');
          return;
        }

        const testPin = await this.promptForPin();
        if (!testPin) {
          this.addLog('取消PIN码测试', 'info');
          return;
        }

        const isValid = SecurityManager.verifyPin(testPin);
        this.addLog(`PIN码验证结果: ${isValid ? '正确' : '错误'}`, isValid ? 'success' : 'error');
        
        if (isValid) {
          SecurityManager.updateLastUnlockTime();
          this.refreshStatus();
        }
      } catch (error) {
        this.addLog('PIN码验证测试失败: ' + error.message, 'error');
      }
    },

    /**
     * 测试指纹支持检测
     */
    async testFingerprintSupport() {
      try {
        this.addLog('检测指纹支持中...', 'info');
        const supported = await SecurityManager.isFingerprintSupported();
        this.addLog(`设备指纹支持: ${supported ? '支持' : '不支持'}`, supported ? 'success' : 'warning');
      } catch (error) {
        this.addLog('指纹支持检测失败: ' + error.message, 'error');
      }
    },

    /**
     * 测试指纹录入检测
     */
    async testFingerprintEnrolled() {
      try {
        this.addLog('检测指纹录入中...', 'info');
        const enrolled = await SecurityManager.hasEnrolledFingerprint();
        this.addLog(`指纹录入状态: ${enrolled ? '已录入' : '未录入'}`, enrolled ? 'success' : 'warning');
      } catch (error) {
        this.addLog('指纹录入检测失败: ' + error.message, 'error');
      }
    },

    /**
     * 测试指纹验证
     */
    async testFingerprintAuth() {
      try {
        if (!this.fingerprintEnabled) {
          this.addLog('请先启用指纹解锁', 'warning');
          return;
        }

        this.addLog('开始指纹验证...', 'info');
        const success = await SecurityManager.verifyFingerprint();
        this.addLog(`指纹验证结果: ${success ? '成功' : '失败'}`, success ? 'success' : 'error');
        
        if (success) {
          SecurityManager.updateLastUnlockTime();
          this.refreshStatus();
        }
      } catch (error) {
        this.addLog('指纹验证测试失败: ' + error.message, 'error');
      }
    },

    /**
     * 模拟自动锁定
     */
    simulateAutoLock() {
      try {
        // 将最后解锁时间设置为6分钟前
        const sixMinutesAgo = Date.now() - (6 * 60 * 1000);
        uni.setStorageSync('app_last_unlock_time', sixMinutesAgo);
        
        this.refreshStatus();
        this.addLog('已模拟自动锁定（设置解锁时间为6分钟前）', 'success');
      } catch (error) {
        this.addLog('模拟自动锁定失败: ' + error.message, 'error');
      }
    },

    /**
     * 重置解锁时间
     */
    resetLastUnlockTime() {
      try {
        SecurityManager.updateLastUnlockTime();
        this.refreshStatus();
        this.addLog('解锁时间已重置', 'success');
      } catch (error) {
        this.addLog('重置解锁时间失败: ' + error.message, 'error');
      }
    },

    /**
     * 重置所有安全设置
     */
    resetAllSecurity() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置所有安全设置吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            try {
              SecurityManager.disableSecurity();
              this.refreshStatus();
              this.addLog('所有安全设置已重置', 'success');
            } catch (error) {
              this.addLog('重置安全设置失败: ' + error.message, 'error');
            }
          }
        }
      });
    },

    /**
     * 提示输入PIN码
     */
    promptForPin() {
      return new Promise((resolve) => {
        uni.showModal({
          title: '输入PIN码',
          content: '请输入6位数字PIN码进行测试',
          editable: true,
          placeholderText: '请输入PIN码',
          success: (res) => {
            if (res.confirm) {
              resolve(res.content);
            } else {
              resolve(null);
            }
          }
        });
      });
    },

    /**
     * 添加日志
     */
    addLog(message, type = 'info') {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      
      this.testLogs.unshift({
        time,
        message,
        type
      });

      // 限制日志数量
      if (this.testLogs.length > 50) {
        this.testLogs = this.testLogs.slice(0, 50);
      }
    },

    /**
     * 清除日志
     */
    clearLogs() {
      this.testLogs = [];
    },

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.custom-navbar {
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.navbar-left {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.icon-back {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  width: 60px;
}

.content {
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.status-section {
  margin-bottom: 30px;
}

.status-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
}

.status-value.success {
  color: #4CAF50;
}

.status-value.error {
  color: #FF5722;
}

.status-value.warning {
  color: #FF9800;
}

.test-section {
  margin-bottom: 30px;
}

.test-btn {
  width: 100%;
  height: 45px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn:active {
  transform: scale(0.98);
  background: #f5f5f5;
}

.test-btn.danger {
  background: #fff5f5;
  border-color: #ffcdd2;
  color: #d32f2f;
}

.result-section {
  margin-bottom: 30px;
}

.result-card {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-content {
  height: 300px;
  padding: 15px;
}

.log-item {
  display: flex;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  font-size: 12px;
  color: #999;
  width: 60px;
  flex-shrink: 0;
  margin-right: 10px;
}

.log-message {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.log-message.info {
  color: #333;
}

.log-message.success {
  color: #4CAF50;
}

.log-message.error {
  color: #FF5722;
}

.log-message.warning {
  color: #FF9800;
}

.no-logs {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 50px 0;
}

.clear-btn {
  width: 100%;
  height: 40px;
  background: #f5f5f5;
  border: none;
  border-top: 1px solid #e0e0e0;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.clear-btn:active {
  background: #e0e0e0;
}
</style>
