<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">{{isViewMode ? '记录详情' : (editingRecord ? '编辑记录' : '添加记录')}}</text>
				<view class="header-actions" v-if="!isViewMode">
					<text class="quick-btn" @click="voiceRecord">🎤</text>
					<text class="quick-btn" @click="photoRecord">📷</text>
				</view>
				<text class="header-action" @click="toggleEdit" v-if="isViewMode">编辑</text>
			</view>
		</view>
		
		<!-- 类型切换 -->
		<view class="type-tabs" v-if="!isViewMode">
			<view class="tab-item" :class="{active: currentType === 'expense'}" @click="switchType('expense')">
				<text class="tab-text">支出</text>
			</view>
			<view class="tab-item" :class="{active: currentType === 'income'}" @click="switchType('income')">
				<text class="tab-text">收入</text>
			</view>
		</view>
		
		<!-- 金额输入 -->
		<view class="amount-section">
			<view class="amount-input-container">
				<text class="currency">¥</text>
				<input 
					class="amount-input" 
					type="digit" 
					v-model="amount" 
					placeholder="0.00"
					:disabled="isViewMode"
					focus
				/>
			</view>
		</view>
		
		<!-- 分类选择 -->
		<view class="category-section">
			<view class="section-title">
				<text>选择分类</text>
			</view>
			
			<!-- 分组切换 -->
			<view class="group-tabs" v-if="!isViewMode">
				<view class="group-tab" :class="{active: currentGroup === 'daily'}" @click="switchGroup('daily')">
					<text class="group-text">日常</text>
				</view>
				<view class="group-tab" :class="{active: currentGroup === 'reimbursement'}" @click="switchGroup('reimbursement')">
					<text class="group-text">报销</text>
				</view>
			</view>
			
			<!-- 分类网格 -->
			<view class="categories-grid">
				<view 
					v-for="category in currentCategories" 
					:key="category.id" 
					class="category-item" 
					:class="{active: selectedCategory && selectedCategory.id === category.id}"
					@click="selectCategory(category)"
				>
					<text class="category-icon">{{category.icon}}</text>
					<text class="category-name">{{category.name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 备注输入 -->
		<view class="note-section">
			<view class="section-title">
				<text>备注</text>
			</view>
			<textarea 
				class="note-input" 
				v-model="note" 
				placeholder="添加备注信息（可选）"
				:disabled="isViewMode"
				maxlength="100"
			></textarea>
		</view>
		
		<!-- 标签选择 -->
		<view class="tags-section">
			<view class="section-title">
				<text>标签</text>
				<text class="add-tag-btn" @click="showTagSelector" v-if="!isViewMode">+</text>
			</view>
			
			<!-- 已选标签 -->
			<view v-if="selectedTags.length > 0" class="selected-tags">
				<view 
					v-for="tag in selectedTagsData" 
					:key="tag.id"
					class="tag-chip"
					:style="{backgroundColor: tag.color}"
				>
					<text class="tag-text">{{tag.name}}</text>
					<text class="tag-remove" @click="removeTag(tag.id)" v-if="!isViewMode">×</text>
				</view>
			</view>
			
			<!-- 无标签提示 -->
			<view v-else class="no-tags">
				<text class="no-tags-text">暂无标签，点击右上角添加</text>
			</view>
		</view>
		
		<!-- 日期时间 -->
		<view class="datetime-section">
			<view class="datetime-item">
				<text class="datetime-label">日期</text>
				<picker mode="date" :value="dateStr" @change="onDateChange" :disabled="isViewMode">
					<view class="datetime-value">{{dateStr}}</view>
				</picker>
			</view>
			<view class="datetime-item">
				<text class="datetime-label">时间</text>
				<picker mode="time" :value="timeStr" @change="onTimeChange" :disabled="isViewMode">
					<view class="datetime-value">{{timeStr}}</view>
				</picker>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-buttons" v-if="!isViewMode">
			<button class="save-btn" @click="save">保存</button>
		</view>
		
		<!-- 查看模式操作 -->
		<view class="view-actions" v-if="isViewMode">
			<button class="edit-btn" @click="toggleEdit">编辑</button>
			<button class="delete-btn" @click="deleteRecord">删除</button>
		</view>
		
		<!-- 标签选择弹窗 -->
		<view v-if="showTagPopup" class="popup-overlay" @click="closeTagPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">选择标签</text>
					<text class="popup-close" @click="closeTagPopup">×</text>
				</view>
				
				<view class="tags-grid">
					<view 
						v-for="tag in availableTags" 
						:key="tag.id"
						class="tag-option"
						:class="{selected: selectedTags.includes(tag.id)}"
						@click="toggleTag(tag.id)"
					>
						<view class="tag-icon" :style="{backgroundColor: tag.color}">
							<text class="icon-text">{{tag.icon}}</text>
						</view>
						<text class="tag-name">{{tag.name}}</text>
					</view>
				</view>
				
				<view v-if="availableTags.length === 0" class="no-tags-hint">
					<text class="hint-text">暂无标签，去设置中添加</text>
					<button class="go-settings-btn" @click="goToTagSettings">去添加</button>
				</view>
				
				<view class="popup-actions">
					<button class="confirm-btn" @click="confirmTagSelection">确定</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createRecord } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';
import SyncManager from '@/utils/sync.js';

export default {
	data() {
		return {
			// 基本信息
			amount: '',
			note: '',
			currentType: 'expense',
			currentGroup: 'daily',
			selectedCategory: null,
			
			// 标签相关
			selectedTags: [],
			availableTags: [],
			showTagPopup: false,
			
			// 日期时间
			dateStr: '',
			timeStr: '',
			
			// 模式控制
			isViewMode: false,
			editingRecord: null,
			recordId: null
		};
	},
	computed: {
		// 当前可选分类
		currentCategories() {
			return this.getDefaultCategories().filter(cat => 
				cat.type === this.currentType && cat.groupId === this.currentGroup
			);
		},
		
		// 已选标签数据
		selectedTagsData() {
			return this.availableTags.filter(tag => this.selectedTags.includes(tag.id));
		}
	},
	onLoad(options) {
		this.initDateTime();
		this.loadTags();
		
		// 处理快速类型选择
		if (options.type) {
			this.currentType = options.type;
		}
		
		// 处理记录查看/编辑
		if (options.id) {
			this.recordId = options.id;
			this.isViewMode = options.mode === 'view';
			this.loadRecord(options.id);
		}
	},
	methods: {
		// 初始化日期时间
		initDateTime() {
			const now = new Date();
			this.dateStr = this.formatDate(now);
			this.timeStr = this.formatTime(now);
		},
		
		// 加载记录数据
		loadRecord(id) {
			const records = StorageManager.getRecords();
			const record = records.find(r => r.id === id);
			
			if (record) {
				this.editingRecord = record;
				this.amount = record.amount.toString();
				this.note = record.note || '';
				this.currentType = record.type;
				
				// 找到对应分类
				const category = this.getDefaultCategories().find(c => c.id === record.categoryId);
				if (category) {
					this.selectedCategory = category;
					this.currentGroup = category.groupId;
				}
				
				// 加载标签
				this.selectedTags = record.tags || [];
				
				// 设置日期时间
				const date = new Date(record.date);
				this.dateStr = this.formatDate(date);
				this.timeStr = this.formatTime(date);
			}
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		// 语音记账
		voiceRecord() {
			uni.navigateTo({ url: '/pages/voice-record/voice-record' });
		},
		// 拍照记账
		photoRecord() {
			uni.navigateTo({ url: '/pages/photo-record/photo-record' });
		},
		
		// 切换编辑模式
		toggleEdit() {
			this.isViewMode = !this.isViewMode;
		},
		
		// 切换类型
		switchType(type) {
			if (this.isViewMode) return;
			this.currentType = type;
			this.selectedCategory = null;
		},
		
		// 切换分组
		switchGroup(group) {
			if (this.isViewMode) return;
			this.currentGroup = group;
			this.selectedCategory = null;
		},
		
		// 选择分类
		selectCategory(category) {
			if (this.isViewMode) return;
			this.selectedCategory = category;
		},
		
		// 加载标签
		loadTags() {
			this.availableTags = StorageManager.getTags();
		},
		
		// 显示标签选择器
		showTagSelector() {
			if (this.isViewMode) return;
			this.showTagPopup = true;
		},
		
		// 关闭标签选择器
		closeTagPopup() {
			this.showTagPopup = false;
		},
		
		// 切换标签选择
		toggleTag(tagId) {
			const index = this.selectedTags.indexOf(tagId);
			if (index > -1) {
				this.selectedTags.splice(index, 1);
			} else {
				this.selectedTags.push(tagId);
			}
		},
		
		// 确认标签选择
		confirmTagSelection() {
			this.closeTagPopup();
		},
		
		// 移除标签
		removeTag(tagId) {
			if (this.isViewMode) return;
			const index = this.selectedTags.indexOf(tagId);
			if (index > -1) {
				this.selectedTags.splice(index, 1);
			}
		},
		
		// 跳转到标签设置
		goToTagSettings() {
			uni.navigateTo({
				url: '/pages/tags/tags'
			});
		},
		
		// 更新标签使用次数
		updateTagUsage(oldTags, newTags) {
			const tags = StorageManager.getTags();
			
			// 减少旧标签的使用次数
			oldTags.forEach(tagId => {
				const tag = tags.find(t => t.id === tagId);
				if (tag && tag.usageCount > 0) {
					tag.usageCount--;
				}
			});
			
			// 增加新标签的使用次数
			newTags.forEach(tagId => {
				const tag = tags.find(t => t.id === tagId);
				if (tag) {
					tag.usageCount++;
				}
			});
			
			StorageManager.saveTags(tags);
		},
		
		// 日期变化
		onDateChange(e) {
			this.dateStr = e.detail.value;
		},
		
		// 时间变化
		onTimeChange(e) {
			this.timeStr = e.detail.value;
		},
		
		// 保存记录
		save() {
			if (!this.amount || this.amount <= 0) {
				return uni.showToast({ title: '请输入正确的金额', icon: 'none' });
			}
			
			if (!this.selectedCategory) {
				return uni.showToast({ title: '请选择分类', icon: 'none' });
			}
			
			// 构建日期时间
			const datetime = new Date(`${this.dateStr} ${this.timeStr}`);
			
			const records = StorageManager.getRecords();
			
			if (this.editingRecord) {
				// 编辑模式
				const index = records.findIndex(r => r.id === this.editingRecord.id);
				if (index !== -1) {
					// 更新标签使用次数
					this.updateTagUsage(this.editingRecord.tags || [], this.selectedTags);
					
					records[index] = {
						...this.editingRecord,
						amount: parseFloat(this.amount),
						categoryId: this.selectedCategory.id,
						type: this.currentType,
						note: this.note,
						tags: [...this.selectedTags],
						date: datetime.getTime(),
						updatedAt: Date.now()
					};
				}
			} else {
				// 新增模式
				const record = createRecord({
					amount: parseFloat(this.amount),
					categoryId: this.selectedCategory.id,
					type: this.currentType,
					note: this.note,
					tags: [...this.selectedTags],
					date: datetime.getTime()
				});
				records.push(record);
				
				// 更新标签使用次数
				this.updateTagUsage([], this.selectedTags);
			}
			
			StorageManager.saveRecords(records);
			
			// 同步数据
			if (typeof SyncManager !== 'undefined' && SyncManager.syncAfterRecord) {
				SyncManager.syncAfterRecord();
			}
			
			uni.showToast({ 
				title: this.editingRecord ? '修改成功' : '保存成功', 
				icon: 'success' 
			});
			
			setTimeout(() => {
				uni.navigateBack();
			}, 1000);
		},
		
		// 删除记录
		deleteRecord() {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条记录吗？',
				success: (res) => {
					if (res.confirm) {
						const records = StorageManager.getRecords();
						const filteredRecords = records.filter(r => r.id !== this.editingRecord.id);
						StorageManager.saveRecords(filteredRecords);
						
						uni.showToast({ title: '删除成功', icon: 'success' });
						setTimeout(() => {
							uni.navigateBack();
						}, 1000);
					}
				}
			});
		},
		
		// 格式化日期
		formatDate(date) {
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		
		// 格式化时间
		formatTime(date) {
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${hours}:${minutes}`;
		},
		
		// 获取默认分类
		getDefaultCategories() {
			return [
				// 支出分类
				{ id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily' },
				{ id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily' },
				{ id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily' },
				{ id: 'entertainment', name: '娱乐', icon: '🎮', type: 'expense', groupId: 'daily' },
				{ id: 'medical', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily' },
				{ id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily' },
				{ id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily' },
				{ id: 'utilities', name: '水电', icon: '💡', type: 'expense', groupId: 'daily' },
				
				// 收入分类
				{ id: 'salary', name: '工资', icon: '💰', type: 'income', groupId: 'daily' },
				{ id: 'bonus', name: '奖金', icon: '🎁', type: 'income', groupId: 'daily' },
				{ id: 'investment', name: '投资', icon: '📈', type: 'income', groupId: 'daily' },
				{ id: 'other_income', name: '其他收入', icon: '💵', type: 'income', groupId: 'daily' },
				
				// 报销分类
				{ id: 'business_meal', name: '商务餐饮', icon: '🍽️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'business_transport', name: '差旅交通', icon: '✈️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'accommodation', name: '住宿费', icon: '🏨', type: 'expense', groupId: 'reimbursement' },
				{ id: 'office_supplies', name: '办公用品', icon: '📎', type: 'expense', groupId: 'reimbursement' },
				{ id: 'communication', name: '通讯费', icon: '📱', type: 'expense', groupId: 'reimbursement' },
				{ id: 'training', name: '培训费', icon: '🎓', type: 'expense', groupId: 'reimbursement' },
				{ id: 'entertainment_business', name: '商务招待', icon: '🍷', type: 'expense', groupId: 'reimbursement' },
				{ id: 'other_reimbursement', name: '其他报销', icon: '📋', type: 'expense', groupId: 'reimbursement' }
			];
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

.header-actions {
	display: flex;
	gap: 20rpx;
}

.quick-btn {
	font-size: 32rpx;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	background: rgba(255, 255, 255, 0.2);
	transition: all 0.2s;
}

.quick-btn:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.3);
}

.header-action {
	font-size: 28rpx;
}

/* 类型切换 */
.type-tabs {
	background: white;
	margin: 20rpx 40rpx;
	border-radius: 20rpx;
	display: flex;
	padding: 8rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 16rpx;
	transition: all 0.3s;
}

.tab-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 金额输入 */
.amount-section {
	background: white;
	margin: 0 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
}

.amount-input-container {
	display: flex;
	align-items: center;
	justify-content: center;
}

.currency {
	font-size: 48rpx;
	color: #333;
	font-weight: bold;
	margin-right: 10rpx;
}

.amount-input {
	font-size: 60rpx;
	color: #333;
	font-weight: bold;
	text-align: center;
	border: none;
	outline: none;
	min-width: 200rpx;
}

/* 分类区域 */
.category-section {
	background: white;
	margin: 0 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

/* 分组切换 */
.group-tabs {
	display: flex;
	margin-bottom: 30rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 6rpx;
}

.group-tab {
	flex: 1;
	text-align: center;
	padding: 16rpx;
	border-radius: 12rpx;
	transition: all 0.3s;
}

.group-tab.active {
	background: white;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.group-text {
	font-size: 26rpx;
	color: #666;
}

.group-tab.active .group-text {
	color: #333;
	font-weight: 500;
}

/* 分类网格 */
.categories-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	transition: all 0.3s;
}

.category-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.category-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.category-name {
	font-size: 24rpx;
	text-align: center;
}

.category-item.active .category-name {
	color: white;
}

/* 备注区域 */
.note-section {
	background: white;
	margin: 0 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
}

.note-input {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	outline: none;
	font-size: 28rpx;
	color: #333;
	resize: none;
}

/* 标签区域 */
.tags-section {
	background: white;
	margin: 0 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
}

.section-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.add-tag-btn {
	font-size: 32rpx;
	color: #667eea;
	font-weight: bold;
}

.selected-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 20rpx;
}

.tag-chip {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	color: white;
	font-size: 24rpx;
}

.tag-text {
	margin-right: 8rpx;
}

.tag-remove {
	font-size: 28rpx;
	font-weight: bold;
	opacity: 0.8;
}

.no-tags {
	margin-top: 20rpx;
	text-align: center;
	padding: 40rpx;
}

.no-tags-text {
	font-size: 26rpx;
	color: #999;
}

/* 标签选择弹窗 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	max-height: 70vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.tags-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.tag-option {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	transition: all 0.3s;
}

.tag-option.selected {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.tag-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx;
}

.tag-icon .icon-text {
	font-size: 20rpx;
}

.tag-name {
	font-size: 24rpx;
	text-align: center;
}

.tag-option.selected .tag-name {
	color: white;
}

.no-tags-hint {
	text-align: center;
	padding: 60rpx 40rpx;
}

.hint-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin-bottom: 30rpx;
}

.go-settings-btn {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 28rpx;
}

.popup-actions {
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}

/* 日期时间 */
.datetime-section {
	background: white;
	margin: 0 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
}

.datetime-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.datetime-item:last-child {
	border-bottom: none;
}

.datetime-label {
	font-size: 28rpx;
	color: #333;
}

.datetime-value {
	font-size: 28rpx;
	color: #667eea;
}

/* 操作按钮 */
.action-buttons {
	padding: 0 40rpx 40rpx;
}

.save-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.save-btn:active {
	transform: scale(0.98);
}

/* 查看模式操作 */
.view-actions {
	display: flex;
	gap: 20rpx;
	padding: 0 40rpx 40rpx;
}

.edit-btn {
	flex: 1;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.delete-btn {
	flex: 1;
	height: 100rpx;
	background: #ff4757;
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.edit-btn:active,
.delete-btn:active {
	transform: scale(0.98);
}
</style>
