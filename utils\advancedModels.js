/**
 * 进阶功能数据模型
 * 包含多人记账、投资追踪、借债管理、报销管理、周期记账等功能的数据模型
 */

/**
 * 创建家庭账本
 * @param {Object} options - 家庭账本参数
 * @returns {Object} 家庭账本对象
 */
export function createFamilyBook({
  name = '',
  description = '',
  ownerId = '',
  icon = '👨‍👩‍👧‍👦',
  color = '#2196F3'
} = {}) {
  const now = Date.now();
  return {
    id: `family_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    description,
    ownerId,
    icon,
    color,
    members: [],
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建家庭成员
 * @param {Object} options - 成员参数
 * @returns {Object} 成员对象
 */
export function createFamilyMember({
  name = '',
  avatar = '',
  role = 'member', // owner, admin, member, viewer
  permissions = ['view'],
  familyBookId = ''
} = {}) {
  const now = Date.now();
  return {
    id: `member_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    avatar,
    role,
    permissions, // ['view', 'add', 'edit', 'delete', 'manage']
    familyBookId,
    joinedAt: now,
    lastActiveAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建分摊记录
 * @param {Object} options - 分摊参数
 * @returns {Object} 分摊对象
 */
export function createSplitRecord({
  recordId = '',
  totalAmount = 0,
  payerId = '',
  participants = [], // [{memberId, amount, paid}]
  splitType = 'equal', // equal, custom, percentage
  note = ''
} = {}) {
  const now = Date.now();
  return {
    id: `split_${now}_${Math.random().toString(36).slice(2, 8)}`,
    recordId,
    totalAmount: Number(totalAmount),
    payerId,
    participants,
    splitType,
    note,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建投资记录
 * @param {Object} options - 投资参数
 * @returns {Object} 投资对象
 */
export function createInvestment({
  name = '',
  type = 'stock', // stock, fund, bond, crypto, other
  symbol = '',
  quantity = 0,
  buyPrice = 0,
  currentPrice = 0,
  buyDate = Date.now(),
  accountId = '',
  note = ''
} = {}) {
  const now = Date.now();
  return {
    id: `investment_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    type,
    symbol,
    quantity: Number(quantity),
    buyPrice: Number(buyPrice),
    currentPrice: Number(currentPrice),
    buyDate,
    accountId,
    note,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建借债记录
 * @param {Object} options - 借债参数
 * @returns {Object} 借债对象
 */
export function createDebt({
  type = 'lent', // lent(借出), borrowed(借入)
  personName = '',
  amount = 0,
  paidAmount = 0,
  date = '',
  note = '',
  status = 'pending', // pending(未还清), completed(已还清)
  contactPhone = '',
  dueDate = null,
  interestRate = 0
} = {}) {
  const now = Date.now();
  return {
    id: `debt_${now}_${Math.random().toString(36).slice(2, 8)}`,
    type,
    personName,
    amount: Number(amount),
    paidAmount: Number(paidAmount),
    date: date || new Date().toISOString().split('T')[0],
    note,
    status,
    contactPhone,
    dueDate,
    interestRate: Number(interestRate),
    repayments: [], // 还款记录
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建还款记录
 * @param {Object} options - 还款参数
 * @returns {Object} 还款对象
 */
export function createRepayment({
  debtId = '',
  amount = 0,
  repayDate = Date.now(),
  note = ''
} = {}) {
  const now = Date.now();
  return {
    id: `repayment_${now}_${Math.random().toString(36).slice(2, 8)}`,
    debtId,
    amount: Number(amount),
    repayDate,
    note,
    createdAt: now,
    synced: false
  };
}

/**
 * 创建报销记录
 * @param {Object} options - 报销参数
 * @returns {Object} 报销对象
 */
export function createReimbursement({
  title = '',
  categoryId = '',
  amount = 0,
  company = '',
  date = '',
  description = '',
  status = 'pending', // pending, approved, rejected
  attachments = [],
  recordId = '',
  submitDate = null,
  approveDate = null,
  payDate = null
} = {}) {
  const now = Date.now();
  return {
    id: `reimburse_${now}_${Math.random().toString(36).slice(2, 8)}`,
    title,
    categoryId,
    amount: Number(amount),
    company,
    date,
    description,
    status,
    attachments,
    recordId,
    submitDate,
    approveDate,
    payDate,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建周期记账模板
 * @param {Object} options - 模板参数
 * @returns {Object} 模板对象
 */
export function createRecurringTemplate({
  name = '',
  type = 'expense',
  categoryId = '',
  accountId = '',
  amount = 0,
  note = '',
  frequency = 'monthly', // daily, weekly, monthly, yearly
  startDate = Date.now(),
  endDate = null,
  enabled = true,
  tags = []
} = {}) {
  const now = Date.now();
  return {
    id: `template_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    type,
    categoryId,
    accountId,
    amount: Number(amount),
    note,
    frequency,
    startDate,
    endDate,
    enabled,
    tags,
    lastExecuted: null,
    nextExecution: startDate,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建OCR识别结果
 * @param {Object} options - OCR参数
 * @returns {Object} OCR结果对象
 */
export function createOCRResult({
  imageUrl = '',
  merchant = '',
  amount = 0,
  date = Date.now(),
  items = [],
  confidence = 0,
  rawText = ''
} = {}) {
  const now = Date.now();
  return {
    id: `ocr_${now}_${Math.random().toString(36).slice(2, 8)}`,
    imageUrl,
    merchant,
    amount: Number(amount),
    date,
    items,
    confidence: Number(confidence),
    rawText,
    createdAt: now
  };
}

/**
 * 验证投资记录
 * @param {Object} investment - 投资对象
 * @returns {Object} 验证结果
 */
export function validateInvestment(investment) {
  if (!investment.name || investment.name.trim() === '') {
    return { valid: false, message: '投资名称不能为空' };
  }

  if (!investment.type) {
    return { valid: false, message: '投资类型不能为空' };
  }

  if (investment.quantity <= 0) {
    return { valid: false, message: '持有数量必须大于0' };
  }

  if (investment.buyPrice <= 0) {
    return { valid: false, message: '买入价格必须大于0' };
  }

  return { valid: true };
}

/**
 * 验证借债记录
 * @param {Object} debt - 借债对象
 * @returns {Object} 验证结果
 */
export function validateDebt(debt) {
  const errors = [];
  
  if (!debt.personName || debt.personName.trim() === '') {
    errors.push('对方姓名不能为空');
  }

  if (!debt.amount || debt.amount <= 0) {
    errors.push('借债金额必须大于0');
  }
  
  if (!debt.date) {
    errors.push('请选择日期');
  }

  if (debt.interestRate && debt.interestRate < 0) {
    errors.push('利率不能为负数');
  }

  if (debt.dueDate && debt.date && new Date(debt.dueDate) < new Date(debt.date)) {
    errors.push('到期日期不能早于借款日期');
  }
  
  if (debt.paidAmount < 0 || debt.paidAmount > debt.amount) {
    errors.push('已还金额不能为负数或超过借款金额');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证家庭账本
 * @param {Object} familyBook - 家庭账本对象
 * @returns {Object} 验证结果
 */
export function validateFamilyBook(familyBook) {
  if (!familyBook.name || familyBook.name.trim() === '') {
    return { valid: false, message: '账本名称不能为空' };
  }

  if (!familyBook.ownerId) {
    return { valid: false, message: '账本所有者不能为空' };
  }

  return { valid: true };
}

/**
 * 验证报销记录
 * @param {Object} reimbursement - 报销对象
 * @returns {Object} 验证结果
 */
export function validateReimbursement(reimbursement) {
  const errors = [];
  
  if (!reimbursement.title || reimbursement.title.trim() === '') {
    errors.push('报销标题不能为空');
  }
  
  if (!reimbursement.categoryId) {
    errors.push('请选择报销分类');
  }
  
  if (!reimbursement.amount || reimbursement.amount <= 0) {
    errors.push('报销金额必须大于0');
  }
  
  if (!reimbursement.date) {
    errors.push('请选择报销日期');
  }
  
  if (reimbursement.title && reimbursement.title.length > 50) {
    errors.push('报销标题不能超过50个字符');
  }
  
  if (reimbursement.description && reimbursement.description.length > 200) {
    errors.push('报销描述不能超过200个字符');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 计算投资收益
 * @param {Object} investment - 投资对象
 * @returns {Object} 收益信息
 */
export function calculateInvestmentProfit(investment) {
  const totalCost = investment.quantity * investment.buyPrice;
  const currentValue = investment.quantity * investment.currentPrice;
  const profit = currentValue - totalCost;
  const profitRate = totalCost > 0 ? (profit / totalCost) * 100 : 0;

  return {
    totalCost,
    currentValue,
    profit,
    profitRate: Number(profitRate.toFixed(2))
  };
}

/**
 * 计算分摊金额
 * @param {number} totalAmount - 总金额
 * @param {Array} participants - 参与者列表
 * @param {string} splitType - 分摊类型
 * @returns {Array} 分摊结果
 */
export function calculateSplit(totalAmount, participants, splitType = 'equal') {
  const result = [];
  
  if (splitType === 'equal') {
    const perPersonAmount = totalAmount / participants.length;
    participants.forEach(participant => {
      result.push({
        ...participant,
        amount: Number(perPersonAmount.toFixed(2))
      });
    });
  } else if (splitType === 'custom') {
    // 自定义分摊，使用传入的金额
    result.push(...participants);
  } else if (splitType === 'percentage') {
    // 按百分比分摊
    participants.forEach(participant => {
      const amount = (totalAmount * participant.percentage) / 100;
      result.push({
        ...participant,
        amount: Number(amount.toFixed(2))
      });
    });
  }

  return result;
}
