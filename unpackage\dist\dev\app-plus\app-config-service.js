
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#F8F8F8","navigationBar":{"backgroundColor":"#F8F8F8","titleText":"碎米记账","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"碎米记账","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.75","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#999","selectedColor":"#4caf50","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","iconPath":"/static/tabbar/home.png","selectedIconPath":"/static/tabbar/home-selected.png","text":"首页"},{"pagePath":"pages/report/report","iconPath":"/static/tabbar/report.png","selectedIconPath":"/static/tabbar/report-selected.png","text":"统计"},{"pagePath":"pages/settings/settings","iconPath":"/static/tabbar/setting.png","selectedIconPath":"/static/tabbar/setting-selected.png","text":"设置"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"titleText":"首页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/add-record/add-record","meta":{"navigationBar":{"titleText":"记一笔","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/report/report","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"titleText":"统计报表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/categories/categories","meta":{"navigationBar":{"titleText":"分类管理","type":"default"},"isNVue":false}},{"path":"pages/settings/settings","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"titleText":"设置","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/settings/theme","meta":{"navigationBar":{"titleText":"主题设置","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/settings/theme-test","meta":{"navigationBar":{"titleText":"主题测试","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/records/records","meta":{"navigationBar":{"titleText":"全部记录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/search/search","meta":{"navigationBar":{"titleText":"搜索记录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/accounts/accounts","meta":{"navigationBar":{"titleText":"账户管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/transfer/transfer","meta":{"navigationBar":{"titleText":"转账","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/budget/budget","meta":{"navigationBar":{"titleText":"预算管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/tags/tags","meta":{"navigationBar":{"titleText":"标签管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/savings/savings","meta":{"navigationBar":{"titleText":"储蓄目标","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/reminders/reminders","meta":{"navigationBar":{"titleText":"账单提醒","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/voice-record/voice-record","meta":{"navigationBar":{"titleText":"语音记账","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/photo-record/photo-record","meta":{"navigationBar":{"titleText":"拍照记账","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/account-detail/account-detail","meta":{"navigationBar":{"titleText":"账户明细","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/family/family","meta":{"navigationBar":{"titleText":"家庭账本","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/investment/investment","meta":{"navigationBar":{"titleText":"投资追踪","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/debt/debt","meta":{"navigationBar":{"titleText":"借债管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/reimbursement/reimbursement","meta":{"navigationBar":{"titleText":"报销管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/recurring/recurring","meta":{"navigationBar":{"titleText":"周期记账","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/security/security","meta":{"navigationBar":{"titleText":"安全设置","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/security/pin-setup","meta":{"navigationBar":{"titleText":"设置PIN码","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/security/fingerprint-setup","meta":{"navigationBar":{"titleText":"指纹解锁","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/security/unlock","meta":{"navigationBar":{"titleText":"解锁","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/security/test","meta":{"navigationBar":{"titleText":"安全功能测试","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/help/help","meta":{"navigationBar":{"titleText":"使用帮助","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/help/quick-guide","meta":{"navigationBar":{"titleText":"快速操作指南","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/test/voice-test","meta":{"navigationBar":{"titleText":"语音识别测试","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/test/parse-test","meta":{"navigationBar":{"titleText":"语音解析测试","style":"custom","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  