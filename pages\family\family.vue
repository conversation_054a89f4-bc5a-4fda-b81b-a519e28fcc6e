<template>
  <view class="family-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-content">
        <text class="title">家庭账本</text>
        <view class="add-btn" @click="showCreateDialog = true">
          <text class="add-icon">+</text>
        </view>
      </view>
    </view>

    <!-- 当前账本信息 -->
    <view class="current-book" v-if="currentFamilyBook">
      <view class="book-header">
        <view class="book-info">
          <text class="book-icon">{{currentFamilyBook.icon}}</text>
          <view class="book-details">
            <text class="book-name">{{currentFamilyBook.name}}</text>
            <text class="book-desc">{{currentFamilyBook.description}}</text>
          </view>
        </view>
        <view class="book-actions">
          <text class="action-btn" @click="showMemberDialog = true">成员</text>
          <text class="action-btn" @click="editBook(currentFamilyBook)">编辑</text>
        </view>
      </view>
      
      <!-- 成员列表 -->
      <view class="members-section">
        <text class="section-title">成员 ({{currentMembers.length}})</text>
        <scroll-view class="members-scroll" scroll-x="true">
          <view class="member-item" v-for="member in currentMembers" :key="member.id">
            <view class="member-avatar">
              <text class="avatar-text">{{member.name.charAt(0)}}</text>
            </view>
            <text class="member-name">{{member.name}}</text>
            <text class="member-role">{{getRoleName(member.role)}}</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 账本列表 -->
    <view class="books-section">
      <text class="section-title">我的账本</text>
      <view class="book-list">
        <view class="book-card" 
              v-for="book in familyBooks" 
              :key="book.id"
              @click="switchBook(book)"
              :class="{ active: currentFamilyBook && book.id === currentFamilyBook.id }">
          <view class="card-header">
            <text class="card-icon">{{book.icon}}</text>
            <view class="card-info">
              <text class="card-name">{{book.name}}</text>
              <text class="card-desc">{{book.description}}</text>
            </view>
          </view>
          <view class="card-footer">
            <text class="member-count">{{getMemberCount(book.id)}}人</text>
            <text class="card-date">{{formatDate(book.updatedAt)}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 创建账本对话框 -->
    <view class="modal" v-if="showCreateDialog" @click="closeCreateDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{editingBook ? '编辑账本' : '创建账本'}}</text>
          <text class="modal-close" @click="closeCreateDialog">×</text>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">账本名称</text>
            <input class="form-input" v-model="bookForm.name" placeholder="请输入账本名称" />
          </view>
          <view class="form-group">
            <text class="form-label">描述</text>
            <textarea class="form-textarea" v-model="bookForm.description" placeholder="请输入账本描述"></textarea>
          </view>
          <view class="form-group">
            <text class="form-label">图标</text>
            <scroll-view class="icon-scroll" scroll-x="true">
              <view class="icon-item" 
                    v-for="icon in bookIcons" 
                    :key="icon"
                    @click="bookForm.icon = icon"
                    :class="{ selected: bookForm.icon === icon }">
                <text class="icon-text">{{icon}}</text>
              </view>
            </scroll-view>
          </view>
        </view>
        <view class="modal-footer">
          <view class="btn btn-cancel" @click="closeCreateDialog">取消</view>
          <view class="btn btn-primary" @click="saveBook">{{editingBook ? '保存' : '创建'}}</view>
        </view>
      </view>
    </view>

    <!-- 成员管理对话框 -->
    <view class="modal" v-if="showMemberDialog" @click="closeMemberDialog">
      <view class="modal-content member-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">成员管理</text>
          <text class="modal-close" @click="closeMemberDialog">×</text>
        </view>
        <view class="modal-body">
          <view class="member-list">
            <view class="member-row" v-for="member in currentMembers" :key="member.id">
              <view class="member-info">
                <view class="member-avatar">
                  <text class="avatar-text">{{member.name.charAt(0)}}</text>
                </view>
                <view class="member-details">
                  <text class="member-name">{{member.name}}</text>
                  <text class="member-role">{{getRoleName(member.role)}}</text>
                </view>
              </view>
              <view class="member-actions" v-if="canManageMembers">
                <text class="action-text" @click="editMember(member)">编辑</text>
                <text class="action-text danger" @click="removeMember(member)" v-if="member.role !== 'owner'">移除</text>
              </view>
            </view>
          </view>
          <view class="add-member-section" v-if="canManageMembers">
            <view class="form-group">
              <text class="form-label">添加成员</text>
              <input class="form-input" v-model="memberForm.name" placeholder="请输入成员姓名" />
            </view>
            <view class="form-group">
              <text class="form-label">角色</text>
              <view class="role-options">
                <view class="role-option" 
                      v-for="role in memberRoles" 
                      :key="role.value"
                      @click="memberForm.role = role.value"
                      :class="{ selected: memberForm.role === role.value }">
                  <text class="role-text">{{role.label}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="modal-footer" v-if="canManageMembers">
          <view class="btn btn-cancel" @click="closeMemberDialog">关闭</view>
          <view class="btn btn-primary" @click="addMember">添加成员</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import { createFamilyBook, createFamilyMember, validateFamilyBook } from '@/utils/advancedModels.js';

export default {
  data() {
    return {
      familyBooks: [],
      familyMembers: [],
      currentUser: null,
      currentFamilyBook: null,
      showCreateDialog: false,
      showMemberDialog: false,
      editingBook: null,
      bookForm: {
        name: '',
        description: '',
        icon: '👨‍👩‍👧‍👦'
      },
      memberForm: {
        name: '',
        role: 'member'
      },
      bookIcons: ['👨‍👩‍👧‍👦', '👪', '🏠', '💰', '📊', '💼', '🎯', '📋', '💎', '🌟'],
      memberRoles: [
        { value: 'admin', label: '管理员' },
        { value: 'member', label: '成员' },
        { value: 'viewer', label: '查看者' }
      ]
    };
  },
  
  computed: {
    currentMembers() {
      if (!this.currentFamilyBook) return [];
      return this.familyMembers.filter(member => 
        member.familyBookId === this.currentFamilyBook.id && !member.deleted
      );
    },
    
    canManageMembers() {
      if (!this.currentFamilyBook || !this.currentUser) return false;
      const currentMember = this.currentMembers.find(member => member.id === this.currentUser.id);
      return !currentMember || ['owner', 'admin'].includes(currentMember.role);
    }
  },
  
  onLoad() {
    this.loadData();
  },
  
  methods: {
    loadData() {
      this.familyBooks = StorageManager.getFamilyBooks().filter(book => !book.deleted);
      this.familyMembers = StorageManager.getFamilyMembers().filter(member => !member.deleted);
      this.currentUser = StorageManager.getCurrentUser();
      
      // 设置当前账本
      if (this.currentUser.currentFamilyBookId) {
        this.currentFamilyBook = this.familyBooks.find(book => 
          book.id === this.currentUser.currentFamilyBookId
        );
      }
      
      // 如果没有当前账本但有账本，选择第一个
      if (!this.currentFamilyBook && this.familyBooks.length > 0) {
        this.switchBook(this.familyBooks[0]);
      }
    },
    
    switchBook(book) {
      this.currentFamilyBook = book;
      this.currentUser.currentFamilyBookId = book.id;
      StorageManager.saveCurrentUser(this.currentUser);
      
      uni.showToast({
        title: `已切换到 ${book.name}`,
        icon: 'success'
      });
    },
    
    showCreateDialog() {
      this.editingBook = null;
      this.bookForm = {
        name: '',
        description: '',
        icon: '👨‍👩‍👧‍👦'
      };
      this.showCreateDialog = true;
    },
    
    editBook(book) {
      this.editingBook = book;
      this.bookForm = {
        name: book.name,
        description: book.description,
        icon: book.icon
      };
      this.showCreateDialog = true;
    },
    
    closeCreateDialog() {
      this.showCreateDialog = false;
      this.editingBook = null;
    },
    
    saveBook() {
      if (!this.bookForm.name.trim()) {
        uni.showToast({
          title: '请输入账本名称',
          icon: 'none'
        });
        return;
      }
      
      if (this.editingBook) {
        // 编辑账本
        const index = this.familyBooks.findIndex(book => book.id === this.editingBook.id);
        if (index !== -1) {
          this.familyBooks[index] = {
            ...this.familyBooks[index],
            name: this.bookForm.name,
            description: this.bookForm.description,
            icon: this.bookForm.icon,
            updatedAt: Date.now()
          };
        }
      } else {
        // 创建新账本
        const newBook = createFamilyBook({
          name: this.bookForm.name,
          description: this.bookForm.description,
          icon: this.bookForm.icon,
          ownerId: this.currentUser.id
        });
        
        this.familyBooks.unshift(newBook);
        
        // 创建所有者成员记录
        const ownerMember = createFamilyMember({
          name: this.currentUser.name,
          role: 'owner',
          permissions: ['view', 'add', 'edit', 'delete', 'manage'],
          familyBookId: newBook.id
        });
        
        this.familyMembers.push(ownerMember);
        
        // 设置为当前账本
        this.switchBook(newBook);
      }
      
      StorageManager.saveFamilyBooks(this.familyBooks);
      StorageManager.saveFamilyMembers(this.familyMembers);
      
      this.closeCreateDialog();
      
      uni.showToast({
        title: this.editingBook ? '账本已更新' : '账本已创建',
        icon: 'success'
      });
    },
    
    closeMemberDialog() {
      this.showMemberDialog = false;
      this.memberForm = {
        name: '',
        role: 'member'
      };
    },
    
    addMember() {
      if (!this.memberForm.name.trim()) {
        uni.showToast({
          title: '请输入成员姓名',
          icon: 'none'
        });
        return;
      }
      
      const newMember = createFamilyMember({
        name: this.memberForm.name,
        role: this.memberForm.role,
        permissions: this.getPermissionsByRole(this.memberForm.role),
        familyBookId: this.currentFamilyBook.id
      });
      
      this.familyMembers.push(newMember);
      StorageManager.saveFamilyMembers(this.familyMembers);
      
      this.memberForm = {
        name: '',
        role: 'member'
      };
      
      uni.showToast({
        title: '成员已添加',
        icon: 'success'
      });
    },
    
    removeMember(member) {
      uni.showModal({
        title: '确认删除',
        content: `确定要移除成员 ${member.name} 吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.familyMembers.findIndex(m => m.id === member.id);
            if (index !== -1) {
              this.familyMembers[index].deleted = true;
              StorageManager.saveFamilyMembers(this.familyMembers);
              
              uni.showToast({
                title: '成员已移除',
                icon: 'success'
              });
            }
          }
        }
      });
    },
    
    getMemberCount(bookId) {
      return this.familyMembers.filter(member => 
        member.familyBookId === bookId && !member.deleted
      ).length;
    },
    
    getRoleName(role) {
      const roleMap = {
        owner: '所有者',
        admin: '管理员',
        member: '成员',
        viewer: '查看者'
      };
      return roleMap[role] || '成员';
    },
    
    getPermissionsByRole(role) {
      const permissionMap = {
        owner: ['view', 'add', 'edit', 'delete', 'manage'],
        admin: ['view', 'add', 'edit', 'delete'],
        member: ['view', 'add', 'edit'],
        viewer: ['view']
      };
      return permissionMap[role] || ['view'];
    },
    
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  }
};
</script>

<style scoped>
.family-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  padding: 44px 20px 20px;
  background: rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: white;
}

.add-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.current-book {
  margin: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.book-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.book-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.book-icon {
  font-size: 40px;
  margin-right: 16px;
}

.book-details {
  display: flex;
  flex-direction: column;
}

.book-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.book-desc {
  font-size: 14px;
  color: #666;
}

.book-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 8px 16px;
  background: #f0f0f0;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
}

.members-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.members-scroll {
  white-space: nowrap;
}

.member-item {
  display: inline-block;
  text-align: center;
  margin-right: 16px;
  vertical-align: top;
}

.member-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.avatar-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.member-name {
  display: block;
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
}

.member-role {
  display: block;
  font-size: 10px;
  color: #999;
}

.books-section {
  margin: 20px;
}

.book-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.book-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.book-card.active {
  border: 2px solid #667eea;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.card-icon {
  font-size: 24px;
  margin-right: 12px;
}

.card-info {
  flex: 1;
}

.card-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #666;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-count {
  font-size: 12px;
  color: #999;
}

.card-date {
  font-size: 12px;
  color: #999;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 80%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}

.member-modal {
  max-height: 70%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
}

.icon-scroll {
  white-space: nowrap;
}

.icon-item {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-item.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.icon-text {
  font-size: 24px;
}

.role-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.role-option {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}

.role-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
}

.btn-cancel {
  background: #f0f0f0;
  color: #666;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.member-list {
  margin-bottom: 20px;
}

.member-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.member-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-details {
  margin-left: 12px;
}

.member-actions {
  display: flex;
  gap: 12px;
}

.action-text {
  font-size: 14px;
  color: #667eea;
}

.action-text.danger {
  color: #ff4757;
}

.add-member-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}
</style>
