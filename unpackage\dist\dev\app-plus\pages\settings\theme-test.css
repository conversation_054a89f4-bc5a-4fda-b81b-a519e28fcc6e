
.page[data-v-246d7331] {
	min-height: 100vh;
	padding-bottom: 1.875rem;
}
.header[data-v-246d7331] {
	padding: 1.875rem 1.25rem 1.25rem;
	text-align: center;
}
.header-title[data-v-246d7331] {
	color: #ffffff;
	font-size: 1.125rem;
	font-weight: bold;
}
.theme-info[data-v-246d7331] {
	margin: 0.9375rem;
	padding: 0.9375rem;
	border-radius: 0.625rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.info-title[data-v-246d7331] {
	font-size: 0.875rem;
	font-weight: bold;
}
.info-value[data-v-246d7331] {
	font-size: 0.8125rem;
}
.color-section[data-v-246d7331], .component-section[data-v-246d7331], .quick-switch[data-v-246d7331] {
	margin: 0.9375rem;
}
.section-title[data-v-246d7331] {
	font-size: 1rem;
	font-weight: bold;
	margin-bottom: 0.9375rem;
}
.color-grid[data-v-246d7331] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.color-item[data-v-246d7331] {
	padding: 0.625rem;
	border-radius: 0.5rem;
	text-align: center;
}
.color-preview[data-v-246d7331] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.375rem;
	margin: 0 auto 0.46875rem;
}
.color-name[data-v-246d7331] {
	font-size: 0.75rem;
	font-weight: bold;
	display: block;
	margin-bottom: 0.25rem;
}
.color-value[data-v-246d7331] {
	font-size: 0.625rem;
	word-break: break-all;
}
.test-group[data-v-246d7331] {
	margin-bottom: 1.25rem;
}
.group-title[data-v-246d7331] {
	font-size: 0.875rem;
	font-weight: bold;
	margin-bottom: 0.625rem;
	display: block;
}
.button-group[data-v-246d7331] {
	display: flex;
	gap: 0.625rem;
}
.btn[data-v-246d7331] {
	padding: 0.75rem 1.25rem;
	border-radius: 0.5rem;
	text-align: center;
	flex: 1;
}
.btn.secondary[data-v-246d7331] {
	border: 0.0625rem solid;
}
.btn-text[data-v-246d7331] {
	font-size: 0.875rem;
	font-weight: bold;
}
.card[data-v-246d7331] {
	padding: 0.9375rem;
	border-radius: 0.625rem;
	border: 0.0625rem solid;
}
.card-title[data-v-246d7331] {
	font-size: 0.875rem;
	font-weight: bold;
	margin-bottom: 0.46875rem;
	display: block;
}
.card-content[data-v-246d7331] {
	font-size: 0.8125rem;
	line-height: 1.5;
}
.status-group[data-v-246d7331] {
	display: flex;
	gap: 0.9375rem;
	flex-wrap: wrap;
}
.status-item[data-v-246d7331] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.status-dot[data-v-246d7331] {
	width: 0.625rem;
	height: 0.625rem;
	border-radius: 50%;
}
.status-text[data-v-246d7331] {
	font-size: 0.8125rem;
}
.income-expense-group[data-v-246d7331] {
	display: flex;
	gap: 1.25rem;
}
.amount-item[data-v-246d7331] {
	text-align: center;
}
.amount-label[data-v-246d7331] {
	font-size: 0.75rem;
	margin-bottom: 0.3125rem;
	display: block;
}
.amount-value[data-v-246d7331] {
	font-size: 1rem;
	font-weight: bold;
}
.theme-buttons[data-v-246d7331] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.theme-btn[data-v-246d7331] {
	padding: 0.625rem;
	border-radius: 0.5rem;
	border: 0.0625rem solid;
	text-align: center;
	transition: all 0.3s ease;
}
.theme-btn.active[data-v-246d7331] {
	transform: scale(1.02);
}
.theme-preview-mini[data-v-246d7331] {
	width: 100%;
	height: 1.25rem;
	border-radius: 0.25rem;
	margin-bottom: 0.46875rem;
}
.theme-btn-text[data-v-246d7331] {
	font-size: 0.75rem;
	font-weight: bold;
}
