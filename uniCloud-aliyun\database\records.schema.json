{"bsonType": "object", "required": ["date", "type", "category_id", "amount"], "properties": {"date": {"bsonType": "date", "description": "记账日期"}, "type": {"bsonType": "string", "enum": ["expense", "income", "reimburse"], "description": "类型: 支出/收入/报销"}, "category_id": {"bsonType": "string", "description": "分类ID"}, "category_name": {"bsonType": "string", "description": "分类名称"}, "amount": {"bsonType": "double", "description": "金额"}, "remark": {"bsonType": "string", "description": "备注", "maxLength": 200}, "created_at": {"bsonType": "date", "description": "创建时间", "default": {"$func": "now"}}, "updated_at": {"bsonType": "date", "description": "更新时间", "default": {"$func": "now"}}, "is_deleted": {"bsonType": "bool", "description": "软删除标记", "default": false}}, "uniCloud": {"indexes": {"idx_date": {"fields": {"date": 1}}, "idx_category": {"fields": {"category_id": 1}}}}}