<template>
  <view class="security-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="iconfont icon-back">‹</text>
        </view>
        <view class="navbar-title">安全设置</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <view class="content">
      <!-- 安全状态卡片 -->
      <view class="security-status-card">
        <view class="status-header">
          <text class="status-icon">{{ securityEnabled ? '🔒' : '🔓' }}</text>
          <view class="status-info">
            <text class="status-title">
              {{ securityEnabled ? '安全保护已启用' : '安全保护已关闭' }}
            </text>
            <text class="status-subtitle">
              {{ securityEnabled ? '您的财务数据受到保护' : '建议启用安全保护' }}
            </text>
          </view>
        </view>
        
        <view v-if="securityEnabled" class="status-details">
          <view class="detail-item">
            <text class="detail-label">PIN码：</text>
            <text class="detail-value success">已设置</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">指纹解锁：</text>
            <text class="detail-value" :class="{ success: fingerprintEnabled, muted: !fingerprintEnabled }">
              {{ fingerprintEnabled ? '已启用' : '未启用' }}
            </text>
          </view>
          <view class="detail-item">
            <text class="detail-label">自动锁定：</text>
            <text class="detail-value muted">5分钟后</text>
          </view>
        </view>
      </view>

      <!-- 安全设置选项 -->
      <view class="settings-section">
        <view class="section-title">安全设置</view>
        
        <!-- PIN码设置 -->
        <view class="setting-item" @click="managePinCode">
          <view class="setting-left">
            <text class="setting-icon">🔢</text>
            <view class="setting-info">
              <text class="setting-title">PIN码设置</text>
              <text class="setting-subtitle">
                {{ hasPinCode ? '修改或禁用PIN码' : '设置6位数字PIN码' }}
              </text>
            </view>
          </view>
          <view class="setting-right">
            <text class="setting-status" :class="{ success: hasPinCode }">
              {{ hasPinCode ? '已设置' : '未设置' }}
            </text>
            <text class="iconfont arrow">›</text>
          </view>
        </view>

        <!-- 指纹解锁设置 -->
        <view class="setting-item" @click="manageFingerprint">
          <view class="setting-left">
            <text class="setting-icon">👆</text>
            <view class="setting-info">
              <text class="setting-title">指纹解锁</text>
              <text class="setting-subtitle">
                {{ fingerprintEnabled ? '管理指纹解锁设置' : '启用指纹快速解锁' }}
              </text>
            </view>
          </view>
          <view class="setting-right">
            <text class="setting-status" :class="{ success: fingerprintEnabled, muted: !hasPinCode }">
              {{ !hasPinCode ? '需要PIN码' : (fingerprintEnabled ? '已启用' : '未启用') }}
            </text>
            <text class="iconfont arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 高级设置 -->
      <view class="settings-section">
        <view class="section-title">高级设置</view>
        
        <!-- 自动锁定时间 -->
        <view class="setting-item" @click="showAutoLockOptions">
          <view class="setting-left">
            <text class="setting-icon">⏰</text>
            <view class="setting-info">
              <text class="setting-title">自动锁定</text>
              <text class="setting-subtitle">应用后台运行时自动锁定</text>
            </view>
          </view>
          <view class="setting-right">
            <text class="setting-status muted">5分钟</text>
            <text class="iconfont arrow">›</text>
          </view>
        </view>

        <!-- 重置安全设置 -->
        <view v-if="securityEnabled" class="setting-item danger" @click="resetSecurity">
          <view class="setting-left">
            <text class="setting-icon">⚠️</text>
            <view class="setting-info">
              <text class="setting-title">重置安全设置</text>
              <text class="setting-subtitle">清除所有安全设置</text>
            </view>
          </view>
          <view class="setting-right">
            <text class="iconfont arrow">›</text>
          </view>
        </view>

        <!-- 功能测试 -->
        <view class="setting-item" @click="goToTest">
          <view class="setting-left">
            <text class="setting-icon">🧪</text>
            <view class="setting-info">
              <text class="setting-title">功能测试</text>
              <text class="setting-subtitle">测试安全功能是否正常工作</text>
            </view>
          </view>
          <view class="setting-right">
            <text class="iconfont arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 安全提示 -->
      <view class="security-tips">
        <view class="tips-title">安全提示</view>
        <view class="tip-item">
          <text class="tip-icon">💡</text>
          <text class="tip-text">PIN码是您的主要安全保护，请妥善保管</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">🔒</text>
          <text class="tip-text">指纹解锁需要先设置PIN码作为备用方式</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">⚡</text>
          <text class="tip-text">应用会在后台运行5分钟后自动锁定</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SecurityManager from '@/utils/securityManager.js';

export default {
  name: 'SecurityPage',
  data() {
    return {
      hasPinCode: false,
      fingerprintEnabled: false,
      securityEnabled: false
    };
  },
  onShow() {
    this.loadSecurityStatus();
  },
  methods: {
    /**
     * 加载安全状态
     */
    loadSecurityStatus() {
      try {
        this.hasPinCode = SecurityManager.hasPinCode();
        this.fingerprintEnabled = SecurityManager.isFingerprintEnabled();
        this.securityEnabled = SecurityManager.isSecurityEnabled();
      } catch (error) {
        console.error('加载安全状态失败:', error);
      }
    },

    /**
     * 管理PIN码
     */
    managePinCode() {
      if (this.hasPinCode) {
        // 已设置PIN码，显示管理选项
        uni.showActionSheet({
          itemList: ['修改PIN码', '禁用PIN码'],
          success: (res) => {
            if (res.tapIndex === 0) {
              this.changePinCode();
            } else if (res.tapIndex === 1) {
              this.disablePinCode();
            }
          }
        });
      } else {
        // 未设置PIN码，跳转到设置页面
        uni.navigateTo({
          url: '/pages/security/pin-setup'
        });
      }
    },

    /**
     * 修改PIN码
     */
    changePinCode() {
      uni.navigateTo({
        url: '/pages/security/pin-setup?mode=change'
      });
    },

    /**
     * 禁用PIN码
     */
    disablePinCode() {
      uni.showModal({
        title: '禁用PIN码',
        content: '禁用PIN码将同时禁用指纹解锁，确定要继续吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              SecurityManager.disableSecurity();
              this.loadSecurityStatus();
              
              uni.showToast({
                title: '安全保护已禁用',
                icon: 'success'
              });
            } catch (error) {
              console.error('禁用PIN码失败:', error);
              uni.showToast({
                title: '操作失败',
                icon: 'error'
              });
            }
          }
        }
      });
    },

    /**
     * 管理指纹解锁
     */
    manageFingerprint() {
      if (!this.hasPinCode) {
        uni.showModal({
          title: '需要PIN码',
          content: '启用指纹解锁需要先设置PIN码，是否现在设置？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/security/pin-setup'
              });
            }
          }
        });
        return;
      }

      uni.navigateTo({
        url: '/pages/security/fingerprint-setup'
      });
    },

    /**
     * 显示自动锁定选项
     */
    showAutoLockOptions() {
      uni.showActionSheet({
        itemList: ['立即', '1分钟', '5分钟', '15分钟', '30分钟', '从不'],
        success: (res) => {
          const options = ['立即', '1分钟', '5分钟', '15分钟', '30分钟', '从不'];
          uni.showToast({
            title: `已设置为${options[res.tapIndex]}`,
            icon: 'success'
          });
          // 这里可以实现实际的设置逻辑
        }
      });
    },

    /**
     * 重置安全设置
     */
    resetSecurity() {
      uni.showModal({
        title: '重置安全设置',
        content: '此操作将清除所有安全设置，包括PIN码和指纹解锁。确定要继续吗？',
        confirmText: '重置',
        confirmColor: '#FF5722',
        success: (res) => {
          if (res.confirm) {
            uni.showModal({
              title: '最后确认',
              content: '重置后将无法恢复，请确认您的选择。',
              confirmText: '确认重置',
              confirmColor: '#FF5722',
              success: (res2) => {
                if (res2.confirm) {
                  try {
                    SecurityManager.disableSecurity();
                    this.loadSecurityStatus();
                    
                    uni.showToast({
                      title: '安全设置已重置',
                      icon: 'success'
                    });
                  } catch (error) {
                    console.error('重置失败:', error);
                    uni.showToast({
                      title: '重置失败',
                      icon: 'error'
                    });
                  }
                }
              }
            });
          }
        }
      });
    },

    /**
     * 跳转到功能测试页面
     */
    goToTest() {
      uni.navigateTo({
        url: '/pages/security/test'
      });
    },

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
.security-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.custom-navbar {
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.navbar-left {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.icon-back {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  width: 60px;
}

.content {
  padding: 20px;
}

.security-status-card {
  background: #fff;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-icon {
  font-size: 32px;
  margin-right: 15px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.status-subtitle {
  font-size: 14px;
  color: #666;
}

.status-details {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
}

.detail-value.success {
  color: #4CAF50;
}

.detail-value.muted {
  color: #999;
}

.settings-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-left: 5px;
}

.setting-item {
  background: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.setting-item:active {
  transform: scale(0.98);
}

.setting-item.danger {
  border-left: 3px solid #FF5722;
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 24px;
  margin-right: 15px;
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 3px;
}

.setting-subtitle {
  font-size: 13px;
  color: #666;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-status {
  font-size: 13px;
  color: #999;
  margin-right: 8px;
}

.setting-status.success {
  color: #4CAF50;
}

.setting-status.muted {
  color: #ccc;
}

.arrow {
  font-size: 16px;
  color: #ccc;
  font-weight: bold;
}

.security-tips {
  background: #fff;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 16px;
  margin-right: 10px;
  margin-top: 2px;
}

.tip-text {
  flex: 1;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
</style>
