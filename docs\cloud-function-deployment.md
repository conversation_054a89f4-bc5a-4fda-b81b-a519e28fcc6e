# 云函数部署指南

## 📋 概述

为了在所有平台（H5、小程序、App）都能使用真实的硅基流动语音识别API，我们创建了云函数来处理语音识别请求。

## 🚀 部署步骤

### 1. 确保uniCloud环境已配置

确保您的项目已经关联了uniCloud服务空间：

1. 在HBuilderX中右键点击项目根目录
2. 选择"创建uniCloud云开发环境"
3. 选择阿里云或腾讯云
4. 创建或关联服务空间

### 2. 上传云函数

1. 在HBuilderX中右键点击 `uniCloud-aliyun/cloudfunctions/speechRecognition`
2. 选择"上传并运行"
3. 等待上传完成

### 3. 验证部署

在HBuilderX控制台中查看上传结果，确保没有错误信息。

## 🔧 云函数配置

### API密钥配置

云函数中已经配置了硅基流动API密钥：
```javascript
const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
```

如果需要更换API密钥，请修改 `uniCloud-aliyun/cloudfunctions/speechRecognition/index.js` 文件。

### 支持的音频格式

云函数支持以下音频格式：
- **H5环境**: webm格式
- **小程序环境**: mp3格式  
- **App环境**: mp3格式

## 📱 客户端调用

### 自动平台检测

客户端代码会自动检测当前平台并调用云函数：

```javascript
// H5环境
const result = await uniCloud.callFunction({
    name: 'speechRecognition',
    data: {
        audioData: base64Data,
        audioFormat: 'webm'
    }
});

// 小程序/App环境
const result = await uniCloud.callFunction({
    name: 'speechRecognition',
    data: {
        audioData: base64Data,
        audioFormat: 'mp3'
    }
});
```

### 错误处理

云函数返回统一的响应格式：

```javascript
// 成功响应
{
    code: 200,
    message: '识别成功',
    data: {
        text: '买菜花了30块钱',
        duration: 2.5,
        language: 'zh'
    }
}

// 错误响应
{
    code: 500,
    message: '语音识别失败',
    data: {
        error: '详细错误信息',
        stack: '错误堆栈'
    }
}
```

## 🔍 调试指南

### 1. 查看云函数日志

在uniCloud控制台中查看云函数执行日志：
1. 登录uniCloud控制台
2. 选择对应的服务空间
3. 进入"云函数"页面
4. 点击"speechRecognition"查看日志

### 2. 本地调试

在客户端代码中添加调试信息：

```javascript
console.log('准备调用云函数');
const result = await uniCloud.callFunction({
    name: 'speechRecognition',
    data: {
        audioData: base64Data,
        audioFormat: 'mp3'
    }
});
console.log('云函数调用结果:', result);
```

### 3. 常见问题

#### 问题1: 云函数调用失败
**症状**: `uniCloud.callFunction is not a function`

**解决方案**:
- 确保项目已关联uniCloud服务空间
- 检查main.js中是否正确初始化uniCloud

#### 问题2: 音频数据过大
**症状**: 云函数超时或内存不足

**解决方案**:
- 限制录音时长（建议不超过60秒）
- 检查音频编码设置
- 考虑音频压缩

#### 问题3: API调用失败
**症状**: 硅基流动API返回错误

**解决方案**:
- 检查API密钥是否正确
- 确认账户余额充足
- 检查网络连接

## 📊 性能优化

### 1. 音频格式优化

```javascript
// 小程序录音配置优化
const options = {
    duration: 60000,        // 最大60秒
    sampleRate: 16000,      // 16kHz采样率
    numberOfChannels: 1,    // 单声道
    encodeBitRate: 48000,   // 48kbps码率
    format: 'mp3'          // MP3格式
};
```

### 2. 错误重试机制

```javascript
async function callSpeechRecognition(audioData, retries = 3) {
    for (let i = 0; i < retries; i++) {
        try {
            const result = await uniCloud.callFunction({
                name: 'speechRecognition',
                data: { audioData, audioFormat: 'mp3' }
            });
            return result;
        } catch (error) {
            if (i === retries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}
```

## 🔒 安全考虑

### 1. API密钥保护

- API密钥存储在云函数中，客户端无法访问
- 定期更换API密钥
- 监控API使用量

### 2. 访问控制

```javascript
// 在云函数中添加访问控制
exports.main = async (event, context) => {
    // 验证用户身份
    const { uid } = context;
    if (!uid) {
        return { code: 401, message: '未授权访问' };
    }
    
    // 限制调用频率
    // ... 实现频率限制逻辑
    
    // 处理语音识别
    // ...
};
```

## 📈 监控和统计

### 1. 使用统计

在云函数中添加使用统计：

```javascript
// 记录调用次数
const db = uniCloud.database();
await db.collection('speech_usage').add({
    uid: context.uid,
    timestamp: Date.now(),
    audioSize: audioBuffer.length,
    duration: result.duration
});
```

### 2. 错误监控

```javascript
// 记录错误信息
if (error) {
    await db.collection('speech_errors').add({
        uid: context.uid,
        error: error.message,
        timestamp: Date.now(),
        audioSize: audioBuffer.length
    });
}
```

## 🔄 版本更新

### 更新云函数

1. 修改 `uniCloud-aliyun/cloudfunctions/speechRecognition/index.js`
2. 右键点击云函数目录
3. 选择"上传并运行"
4. 等待更新完成

### 版本管理

建议在云函数中添加版本信息：

```javascript
const FUNCTION_VERSION = '1.0.0';

exports.main = async (event, context) => {
    console.log(`语音识别云函数 v${FUNCTION_VERSION} 被调用`);
    // ...
};
```

## 💡 最佳实践

1. **音频预处理**: 在上传前进行音频格式检查和大小限制
2. **缓存机制**: 对相同音频内容进行缓存，避免重复识别
3. **降级策略**: 在云函数不可用时提供本地模拟数据
4. **用户反馈**: 提供识别结果确认和修正功能
5. **成本控制**: 监控API调用量，设置使用限额

现在您的语音识别功能将在所有平台都使用真实的硅基流动API，而不是模拟数据！
