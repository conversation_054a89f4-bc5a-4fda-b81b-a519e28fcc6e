<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">全部记录</text>
				<text class="header-action" @click="showFilter">筛选</text>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar" v-if="showFilterBar">
			<view class="filter-item">
				<picker mode="selector" :value="typeIndex" :range="typeOptions" @change="onTypeChange">
					<view class="filter-text">{{typeOptions[typeIndex]}}</view>
				</picker>
			</view>
			<view class="filter-item">
				<picker mode="date" :value="startDate" @change="onStartDateChange">
					<view class="filter-text">{{startDate || '开始日期'}}</view>
				</picker>
			</view>
			<view class="filter-item">
				<picker mode="date" :value="endDate" @change="onEndDateChange">
					<view class="filter-text">{{endDate || '结束日期'}}</view>
				</picker>
			</view>
			<view class="filter-reset" @click="resetFilter">重置</view>
		</view>
		
		<!-- 统计信息 -->
		<view class="summary-card">
			<view class="summary-item">
				<text class="summary-label">总支出</text>
				<text class="summary-value expense">¥{{totalExpense.toFixed(2)}}</text>
			</view>
			<view class="summary-divider"></view>
			<view class="summary-item">
				<text class="summary-label">总收入</text>
				<text class="summary-value income">¥{{totalIncome.toFixed(2)}}</text>
			</view>
		</view>
		
		<!-- 记录列表 -->
		<view class="records-container">
			<view v-if="filteredRecords.length === 0" class="empty">
				<text class="empty-text">暂无记录</text>
			</view>
			
			<view v-else>
				<view v-for="(group, date) in groupedRecords" :key="date" class="date-group">
					<view class="date-header">
						<text class="date-text">{{formatDate(date)}}</text>
						<text class="date-amount">
							支出 ¥{{getDayExpense(group).toFixed(2)}} 
							收入 ¥{{getDayIncome(group).toFixed(2)}}
						</text>
					</view>
					
					<view class="records-list">
						<view v-for="record in group" :key="record.id" class="record-item" @click="viewRecord(record)">
							<view class="record-left">
								<view class="record-category">
									<text class="category-icon">{{getCategoryIcon(record.categoryId)}}</text>
									<text class="category-name">{{getCategoryName(record.categoryId)}}</text>
								</view>
								<text class="record-note" v-if="record.note">{{record.note}}</text>
								<text class="record-time">{{formatTime(record.date)}}</text>
							</view>
							<view class="record-right">
								<text class="record-amount" :class="record.type === 'expense' ? 'expense' : 'income'">
									{{record.type === 'expense' ? '-' : '+'}}¥{{record.amount.toFixed(2)}}
								</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			records: [],
			categories: [],
			showFilterBar: false,
			typeIndex: 0,
			typeOptions: ['全部', '支出', '收入'],
			startDate: '',
			endDate: ''
		};
	},
	computed: {
		filteredRecords() {
			let filtered = [...this.records];
			
			// 按类型筛选
			if (this.typeIndex === 1) {
				filtered = filtered.filter(r => r.type === 'expense');
			} else if (this.typeIndex === 2) {
				filtered = filtered.filter(r => r.type === 'income');
			}
			
			// 按日期筛选
			if (this.startDate) {
				const startTime = new Date(this.startDate).getTime();
				filtered = filtered.filter(r => r.date >= startTime);
			}
			if (this.endDate) {
				const endTime = new Date(this.endDate).getTime() + 24 * 60 * 60 * 1000 - 1;
				filtered = filtered.filter(r => r.date <= endTime);
			}
			
			return filtered.sort((a, b) => b.date - a.date);
		},
		groupedRecords() {
			const groups = {};
			this.filteredRecords.forEach(record => {
				const date = new Date(record.date);
				const dateKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
				
				if (!groups[dateKey]) {
					groups[dateKey] = [];
				}
				groups[dateKey].push(record);
			});
			return groups;
		},
		totalExpense() {
			return this.filteredRecords
				.filter(r => r.type === 'expense')
				.reduce((sum, r) => sum + r.amount, 0);
		},
		totalIncome() {
			return this.filteredRecords
				.filter(r => r.type === 'income')
				.reduce((sum, r) => sum + r.amount, 0);
		}
	},
	onLoad() {
		this.loadData();
	},
	methods: {
		loadData() {
			this.records = StorageManager.getRecords();
			this.categories = this.getDefaultCategories();
		},
		goBack() {
			uni.navigateBack();
		},
		showFilter() {
			this.showFilterBar = !this.showFilterBar;
		},
		onTypeChange(e) {
			this.typeIndex = e.detail.value;
		},
		onStartDateChange(e) {
			this.startDate = e.detail.value;
		},
		onEndDateChange(e) {
			this.endDate = e.detail.value;
		},
		resetFilter() {
			this.typeIndex = 0;
			this.startDate = '';
			this.endDate = '';
		},
		viewRecord(record) {
			uni.navigateTo({
				url: `/pages/add-record/add-record?id=${record.id}&mode=view`
			});
		},
		formatDate(dateStr) {
			const date = new Date(dateStr);
			const today = new Date();
			const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
			
			if (dateStr === this.getDateString(today)) {
				return '今天';
			} else if (dateStr === this.getDateString(yesterday)) {
				return '昨天';
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		formatTime(timestamp) {
			const date = new Date(timestamp);
			return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
		},
		getDateString(date) {
			return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
		},
		getDayExpense(records) {
			return records.filter(r => r.type === 'expense').reduce((sum, r) => sum + r.amount, 0);
		},
		getDayIncome(records) {
			return records.filter(r => r.type === 'income').reduce((sum, r) => sum + r.amount, 0);
		},
		getCategoryName(categoryId) {
			const category = this.categories.find(c => c.id === categoryId);
			return category ? category.name : '未知分类';
		},
		getCategoryIcon(categoryId) {
			const category = this.categories.find(c => c.id === categoryId);
			return category ? category.icon : '💰';
		},
		getDefaultCategories() {
			return [
				// 支出分类
				{ id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily' },
				{ id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily' },
				{ id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily' },
				{ id: 'entertainment', name: '娱乐', icon: '🎮', type: 'expense', groupId: 'daily' },
				{ id: 'medical', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily' },
				{ id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily' },
				{ id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily' },
				{ id: 'utilities', name: '水电', icon: '💡', type: 'expense', groupId: 'daily' },
				
				// 收入分类
				{ id: 'salary', name: '工资', icon: '💰', type: 'income', groupId: 'daily' },
				{ id: 'bonus', name: '奖金', icon: '🎁', type: 'income', groupId: 'daily' },
				{ id: 'investment', name: '投资', icon: '📈', type: 'income', groupId: 'daily' },
				{ id: 'other_income', name: '其他收入', icon: '💵', type: 'income', groupId: 'daily' },
				
				// 报销分类
				{ id: 'business_meal', name: '商务餐饮', icon: '🍽️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'business_transport', name: '差旅交通', icon: '✈️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'accommodation', name: '住宿费', icon: '🏨', type: 'expense', groupId: 'reimbursement' },
				{ id: 'office_supplies', name: '办公用品', icon: '📎', type: 'expense', groupId: 'reimbursement' },
				{ id: 'communication', name: '通讯费', icon: '📱', type: 'expense', groupId: 'reimbursement' },
				{ id: 'training', name: '培训费', icon: '🎓', type: 'expense', groupId: 'reimbursement' },
				{ id: 'entertainment_business', name: '商务招待', icon: '🍷', type: 'expense', groupId: 'reimbursement' },
				{ id: 'other_reimbursement', name: '其他报销', icon: '📋', type: 'expense', groupId: 'reimbursement' }
			];
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

.header-action {
	font-size: 28rpx;
}

/* 筛选栏 */
.filter-bar {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid #eee;
}

.filter-item {
	margin-right: 40rpx;
	padding: 10rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.filter-text {
	font-size: 26rpx;
	color: #333;
}

.filter-reset {
	margin-left: auto;
	color: #667eea;
	font-size: 26rpx;
}

/* 统计卡片 */
.summary-card {
	background: white;
	margin: 20rpx 40rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.summary-item {
	flex: 1;
	text-align: center;
}

.summary-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.summary-value {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
}

.summary-value.expense {
	color: #ff4757;
}

.summary-value.income {
	color: #2ed573;
}

.summary-divider {
	width: 2rpx;
	background-color: #eee;
	margin: 0 30rpx;
}

/* 记录容器 */
.records-container {
	margin: 0 40rpx 40rpx;
}

.empty {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}

/* 日期分组 */
.date-group {
	margin-bottom: 30rpx;
}

.date-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
}

.date-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.date-amount {
	font-size: 24rpx;
	color: #666;
}

/* 记录列表 */
.records-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.record-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.record-item:last-child {
	border-bottom: none;
}

.record-item:active {
	background-color: #f8f9fa;
}

.record-left {
	flex: 1;
}

.record-category {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.category-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.category-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.record-note {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.record-time {
	font-size: 24rpx;
	color: #999;
}

.record-right {
	text-align: right;
}

.record-amount {
	font-size: 32rpx;
	font-weight: bold;
}

.record-amount.expense {
	color: #ff4757;
}

.record-amount.income {
	color: #2ed573;
}
</style>
