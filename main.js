import App from './App'
import StorageManager from '@/utils/storageManager.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
// 首次启动初始化默认分类
(async ()=>{
  if(!StorageManager.getCategories().length){
    try{
      const res=await uniCloud.callFunction({name:'initCategories'});
      if(res.result.code===0){
        const listRes=await uniCloud.database().collection('categories').get();
        StorageManager.saveCategories(listRes.data||[]);
      }
    }catch(e){
      console.error('init categories fail',e);
    }
  }
})();

const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif