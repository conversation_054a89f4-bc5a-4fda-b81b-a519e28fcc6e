
.page[data-v-8acca752] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-8acca752] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-8acca752] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-8acca752], .header-action[data-v-8acca752] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-8acca752] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 统计卡片 */
.stats-section[data-v-8acca752] {
	margin: 0.625rem 1.25rem;
}
.stats-card[data-v-8acca752] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	display: flex;
	justify-content: space-around;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.stats-item[data-v-8acca752] {
	text-align: center;
}
.stats-number[data-v-8acca752] {
	font-size: 1.5rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.stats-label[data-v-8acca752] {
	font-size: 0.75rem;
	color: #666;
}

/* 标签列表 */
.tags-section[data-v-8acca752] {
	margin: 0 1.25rem;
}
.empty-state[data-v-8acca752] {
	background: white;
	border-radius: 0.625rem;
	padding: 2.5rem 1.25rem;
	text-align: center;
}
.empty-icon[data-v-8acca752] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-8acca752] {
	font-size: 0.875rem;
	color: #999;
}
.tags-list[data-v-8acca752] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
}
.tag-item[data-v-8acca752] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.tag-item[data-v-8acca752]:last-child {
	border-bottom: none;
}
.tag-item[data-v-8acca752]:active {
	background-color: #f8f9fa;
}
.tag-left[data-v-8acca752] {
	display: flex;
	align-items: center;
	flex: 1;
}
.tag-icon[data-v-8acca752] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.625rem;
}
.icon-text[data-v-8acca752] {
	font-size: 0.75rem;
}
.tag-info[data-v-8acca752] {
	flex: 1;
}
.tag-name[data-v-8acca752] {
	font-size: 1rem;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.tag-usage[data-v-8acca752] {
	font-size: 0.8125rem;
	color: #666;
}
.tag-arrow[data-v-8acca752] {
	font-size: 0.875rem;
	color: #ccc;
}

/* 弹窗样式 */
.popup-overlay[data-v-8acca752] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.popup-content[data-v-8acca752] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	max-height: 80vh;
	overflow-y: auto;
}
.popup-header[data-v-8acca752] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-8acca752] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-8acca752] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-section[data-v-8acca752] {
	margin-bottom: 1.25rem;
}
.form-item[data-v-8acca752] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-8acca752] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-input[data-v-8acca752] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
}

/* 图标选择器 */
.icon-selector[data-v-8acca752] {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	gap: 0.5rem;
}
.icon-item[data-v-8acca752] {
	width: 2.5rem;
	height: 2.5rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}
.icon-item.active[data-v-8acca752] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	transform: scale(1.1);
}
.icon-item .icon-text[data-v-8acca752] {
	font-size: 1rem;
}

/* 颜色选择器 */
.color-selector[data-v-8acca752] {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 0.5rem;
}
.color-item[data-v-8acca752] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	border: 0.125rem solid transparent;
	transition: all 0.3s;
}
.color-item.active[data-v-8acca752] {
	border-color: #333;
	transform: scale(1.2);
}

/* 操作按钮 */
.popup-actions[data-v-8acca752] {
	display: flex;
	gap: 0.625rem;
}
.delete-btn[data-v-8acca752], .cancel-btn[data-v-8acca752], .save-btn[data-v-8acca752] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.delete-btn[data-v-8acca752] {
	background: #ff4757;
	color: white;
}
.cancel-btn[data-v-8acca752] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-8acca752] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
