
.pin-setup[data-v-49162e7e] {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.custom-navbar[data-v-49162e7e] {
  padding-top: var(--status-bar-height);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.navbar-content[data-v-49162e7e] {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.navbar-left[data-v-49162e7e] {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.icon-back[data-v-49162e7e] {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}
.navbar-title[data-v-49162e7e] {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}
.navbar-right[data-v-49162e7e] {
  width: 60px;
}
.content[data-v-49162e7e] {
  padding: 40px 30px;
}
.step-indicator[data-v-49162e7e] {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60px;
}
.step[data-v-49162e7e] {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.step-number[data-v-49162e7e] {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}
.step.active .step-number[data-v-49162e7e] {
  background: #fff;
  color: #667eea;
}
.step-text[data-v-49162e7e] {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}
.step.active .step-text[data-v-49162e7e] {
  color: #fff;
}
.step-line[data-v-49162e7e] {
  width: 60px;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 20px;
  margin-bottom: 20px;
}
.step-line.active[data-v-49162e7e] {
  background: #fff;
}
.pin-input-section[data-v-49162e7e] {
  text-align: center;
}
.pin-title[data-v-49162e7e] {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}
.pin-subtitle[data-v-49162e7e] {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
}
.pin-display[data-v-49162e7e] {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 50px;
}
.pin-dot[data-v-49162e7e] {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
}
.pin-dot.filled[data-v-49162e7e] {
  border-color: #fff;
  background: #fff;
  color: #667eea;
}
.number-keyboard[data-v-49162e7e] {
  max-width: 300px;
  margin: 0 auto;
}
.keyboard-row[data-v-49162e7e] {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}
.keyboard-key[data-v-49162e7e] {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}
.keyboard-key[data-v-49162e7e]:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.key-number[data-v-49162e7e] {
  background: rgba(255, 255, 255, 0.2);
}
.key-action[data-v-49162e7e] {
  background: rgba(255, 255, 255, 0.1);
}
.error-message[data-v-49162e7e] {
  position: fixed;
  bottom: 100px;
  left: 30px;
  right: 30px;
  background: rgba(255, 59, 48, 0.9);
  color: #fff;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  font-size: 14px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
