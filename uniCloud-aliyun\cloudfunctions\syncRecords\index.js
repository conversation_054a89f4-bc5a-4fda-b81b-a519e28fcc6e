"use strict";
const db = uniCloud.database();
const _ = db.command;

// 帮助函数：根据 id 创建映射
function toMap(arr) {
  const map = {};
  arr.forEach(item => {
    map[item._id] = item;
  });
  return map;
}

exports.main = async (event, context) => {
  const { localRecords = [] } = event;
  const ids = localRecords.map(r => r._id);
  // 1. 拉取云端对应记录
  let cloudRecords = [];
  if (ids.length) {
    cloudRecords = (await db.collection("records").where({ _id: _.in(ids) }).get()).data;
  }

  const cloudMap = toMap(cloudRecords);
  const localMap = toMap(localRecords);

  const toUpload = [];
  const toDownload = [];

  // 比对
  localRecords.forEach(lr => {
    const cr = cloudMap[lr._id];
    if (!cr || lr.updated_at > cr.updated_at) {
      toUpload.push(lr);
    } else if (cr.updated_at > lr.updated_at) {
      toDownload.push(cr);
    }
  });

  // 查找云端新增
  cloudRecords.forEach(cr => {
    if (!localMap[cr._id]) {
      toDownload.push(cr);
    }
  });

  // 上传差异
  for (const rec of toUpload) {
    if (cloudMap[rec._id]) {
      await db.collection("records").doc(rec._id).update(rec);
    } else {
      await db.collection("records").add(rec);
    }
  }

  return {
    code: 0,
    uploaded: toUpload.length,
    downloaded: toDownload,
    message: `上传 ${toUpload.length} 条, 下载 ${toDownload.length} 条`
  };
};
