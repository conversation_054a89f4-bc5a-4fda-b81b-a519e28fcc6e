<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">借债管理</text>
				<text class="add-btn" @click="showAddDialog">+</text>
			</view>
		</view>

		<!-- 统计卡片 -->
		<view class="stats-cards">
			<view class="stat-card">
				<text class="stat-label">借出总额</text>
				<text class="stat-value lent">¥{{totalLent.toFixed(2)}}</text>
			</view>
			<view class="stat-card">
				<text class="stat-label">借入总额</text>
				<text class="stat-value borrowed">¥{{totalBorrowed.toFixed(2)}}</text>
			</view>
			<view class="stat-card">
				<text class="stat-label">净借出</text>
				<text class="stat-value" :class="netAmount >= 0 ? 'lent' : 'borrowed'">
					¥{{Math.abs(netAmount).toFixed(2)}}
				</text>
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'all'}"
				@click="setFilter('all')"
			>
				<text>全部</text>
			</view>
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'lent'}"
				@click="setFilter('lent')"
			>
				<text>借出</text>
			</view>
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'borrowed'}"
				@click="setFilter('borrowed')"
			>
				<text>借入</text>
			</view>
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'pending'}"
				@click="setFilter('pending')"
			>
				<text>未还清</text>
			</view>
		</view>

		<!-- 借债列表 -->
		<view class="debt-list">
			<view 
				v-for="debt in filteredDebts" 
				:key="debt.id"
				class="debt-item"
				@click="showDebtDetail(debt)"
			>
				<view class="debt-header">
					<view class="debt-person">
						<text class="person-name">{{debt.personName}}</text>
						<text class="debt-type" :class="debt.type">
							{{debt.type === 'lent' ? '借出' : '借入'}}
						</text>
					</view>
					<view class="debt-amount">
						<text class="amount" :class="debt.type">
							{{debt.type === 'lent' ? '+' : '-'}}¥{{debt.amount.toFixed(2)}}
						</text>
					</view>
				</view>
				
				<view class="debt-progress">
					<view class="progress-info">
						<text class="progress-text">
							已还 ¥{{debt.paidAmount.toFixed(2)}} / ¥{{debt.amount.toFixed(2)}}
						</text>
						<text class="progress-percent">{{((debt.paidAmount / debt.amount) * 100).toFixed(0)}}%</text>
					</view>
					<view class="progress-bar">
						<view 
							class="progress-fill" 
							:style="{width: (debt.paidAmount / debt.amount * 100) + '%'}"
						></view>
					</view>
				</view>
				
				<view class="debt-info">
					<text class="debt-date">{{formatDate(debt.date)}}</text>
					<text class="debt-status" :class="debt.status">
						{{debt.status === 'pending' ? '未还清' : '已还清'}}
					</text>
				</view>
				
				<text v-if="debt.note" class="debt-note">{{debt.note}}</text>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="filteredDebts.length === 0" class="empty-state">
			<text class="empty-icon">💰</text>
			<text class="empty-text">暂无借债记录</text>
			<text class="empty-desc">点击右上角 + 号添加借债记录</text>
		</view>

		<!-- 添加借债弹窗 -->
		<view v-if="showDialog" class="popup-overlay" @click="closeDialog">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingDebt ? '编辑借债' : '添加借债'}}</text>
					<text class="popup-close" @click="closeDialog">×</text>
				</view>
				
				<view class="form-group">
					<text class="form-label">类型</text>
					<view class="type-tabs">
						<view 
							class="type-tab" 
							:class="{active: formData.type === 'lent'}"
							@click="formData.type = 'lent'"
						>
							<text>借出</text>
						</view>
						<view 
							class="type-tab" 
							:class="{active: formData.type === 'borrowed'}"
							@click="formData.type = 'borrowed'"
						>
							<text>借入</text>
						</view>
					</view>
				</view>
				
				<view class="form-group">
					<text class="form-label">对方姓名</text>
					<input 
						class="form-input" 
						v-model="formData.personName" 
						placeholder="请输入对方姓名"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">金额</text>
					<input 
						class="form-input" 
						type="digit"
						v-model="formData.amount" 
						placeholder="请输入金额"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">日期</text>
					<picker mode="date" :value="formData.date" @change="onDateChange">
						<view class="picker-input">
							<text>{{formData.date || '选择日期'}}</text>
						</view>
					</picker>
				</view>
				
				<view class="form-group">
					<text class="form-label">备注</text>
					<textarea 
						class="form-textarea" 
						v-model="formData.note" 
						placeholder="请输入备注（可选）"
						maxlength="100"
					></textarea>
				</view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeDialog">取消</button>
					<button class="confirm-btn" @click="saveDebt">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import { createDebt, validateDebt } from '@/utils/advancedModels.js';

export default {
	data() {
		return {
			debts: [],
			currentFilter: 'all',
			showDialog: false,
			editingDebt: null,
			formData: {
				type: 'lent',
				personName: '',
				amount: '',
				date: '',
				note: ''
			}
		};
	},
	
	computed: {
		// 筛选后的借债列表
		filteredDebts() {
			let filtered = this.debts;
			
			switch(this.currentFilter) {
				case 'lent':
					filtered = this.debts.filter(debt => debt.type === 'lent');
					break;
				case 'borrowed':
					filtered = this.debts.filter(debt => debt.type === 'borrowed');
					break;
				case 'pending':
					filtered = this.debts.filter(debt => debt.status === 'pending');
					break;
			}
			
			return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
		},
		
		// 借出总额
		totalLent() {
			return this.debts
				.filter(debt => debt.type === 'lent')
				.reduce((sum, debt) => sum + debt.amount, 0);
		},
		
		// 借入总额
		totalBorrowed() {
			return this.debts
				.filter(debt => debt.type === 'borrowed')
				.reduce((sum, debt) => sum + debt.amount, 0);
		},
		
		// 净借出金额
		netAmount() {
			return this.totalLent - this.totalBorrowed;
		}
	},
	
	onLoad() {
		this.loadDebts();
		this.initDate();
	},
	
	methods: {
		// 加载借债数据
		async loadDebts() {
			try {
				this.debts = await StorageManager.getDebts();
			} catch (error) {
				console.error('加载借债数据失败:', error);
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				});
			}
		},
		
		// 初始化日期
		initDate() {
			const today = new Date();
			this.formData.date = this.formatDateForPicker(today);
		},
		
		// 设置筛选条件
		setFilter(filter) {
			this.currentFilter = filter;
		},
		
		// 显示添加对话框
		showAddDialog() {
			this.editingDebt = null;
			this.resetForm();
			this.showDialog = true;
		},
		
		// 显示借债详情
		showDebtDetail(debt) {
			// 这里可以跳转到详情页面或显示详情弹窗
			uni.showModal({
				title: '借债详情',
				content: `对方：${debt.personName}\n金额：¥${debt.amount.toFixed(2)}\n已还：¥${debt.paidAmount.toFixed(2)}\n日期：${this.formatDate(debt.date)}\n备注：${debt.note || '无'}`,
				showCancel: false
			});
		},
		
		// 关闭对话框
		closeDialog() {
			this.showDialog = false;
			this.editingDebt = null;
			this.resetForm();
		},
		
		// 重置表单
		resetForm() {
			this.formData = {
				type: 'lent',
				personName: '',
				amount: '',
				date: this.formatDateForPicker(new Date()),
				note: ''
			};
		},
		
		// 日期变化
		onDateChange(e) {
			this.formData.date = e.detail.value;
		},
		
		// 保存借债
		async saveDebt() {
			try {
				// 验证表单
				if (!this.formData.personName.trim()) {
					uni.showToast({
						title: '请输入对方姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formData.amount || parseFloat(this.formData.amount) <= 0) {
					uni.showToast({
						title: '请输入有效金额',
						icon: 'none'
					});
					return;
				}
				
				// 创建借债记录
				const debtData = {
					...this.formData,
					amount: parseFloat(this.formData.amount),
					paidAmount: 0,
					status: 'pending'
				};
				
				const debt = createDebt(debtData);
				const validation = validateDebt(debt);
				
				if (!validation.isValid) {
					uni.showToast({
						title: validation.errors[0],
						icon: 'none'
					});
					return;
				}
				
				// 保存到存储
				await StorageManager.addDebt(debt);
				
				// 重新加载数据
				await this.loadDebts();
				
				// 关闭对话框
				this.closeDialog();
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
			} catch (error) {
				console.error('保存借债失败:', error);
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			}
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 格式化日期显示
		formatDate(dateStr) {
			const date = new Date(dateStr);
			const now = new Date();
			const diffTime = now - date;
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
			
			if (diffDays === 0) {
				return '今天';
			} else if (diffDays === 1) {
				return '昨天';
			} else if (diffDays < 7) {
				return `${diffDays}天前`;
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		
		// 格式化日期为picker格式
		formatDateForPicker(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		}
	}
};
</script>

<style scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	padding: 44rpx 32rpx 32rpx;
}

.header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.back-btn, .add-btn {
	color: white;
	font-size: 36rpx;
	font-weight: bold;
	width: 60rpx;
	text-align: center;
}

.header-title {
	color: white;
	font-size: 36rpx;
	font-weight: bold;
}

.stats-cards {
	display: flex;
	padding: 32rpx;
	gap: 16rpx;
}

.stat-card {
	flex: 1;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.stat-value {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
}

.stat-value.lent {
	color: #10b981;
}

.stat-value.borrowed {
	color: #ef4444;
}

.filter-tabs {
	display: flex;
	margin: 0 32rpx 32rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 12rpx;
	padding: 8rpx;
}

.filter-tab {
	flex: 1;
	text-align: center;
	padding: 16rpx;
	border-radius: 8rpx;
	transition: all 0.3s;
}

.filter-tab.active {
	background: rgba(255, 255, 255, 0.2);
}

.filter-tab text {
	color: white;
	font-size: 28rpx;
}

.debt-list {
	padding: 0 32rpx;
}

.debt-item {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.debt-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.debt-person {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.person-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.debt-type {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	color: white;
}

.debt-type.lent {
	background: #10b981;
}

.debt-type.borrowed {
	background: #ef4444;
}

.debt-amount .amount {
	font-size: 32rpx;
	font-weight: bold;
}

.debt-amount .amount.lent {
	color: #10b981;
}

.debt-amount .amount.borrowed {
	color: #ef4444;
}

.debt-progress {
	margin-bottom: 16rpx;
}

.progress-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.progress-text {
	font-size: 24rpx;
	color: #666;
}

.progress-percent {
	font-size: 24rpx;
	color: #10b981;
	font-weight: bold;
}

.progress-bar {
	height: 8rpx;
	background: #f3f4f6;
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #10b981, #34d399);
	transition: width 0.3s;
}

.debt-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.debt-date {
	font-size: 24rpx;
	color: #666;
}

.debt-status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.debt-status.pending {
	background: #fef3c7;
	color: #d97706;
}

.debt-status.completed {
	background: #d1fae5;
	color: #065f46;
}

.debt-note {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.empty-state {
	text-align: center;
	padding: 120rpx 32rpx;
}

.empty-icon {
	font-size: 120rpx;
	display: block;
	margin-bottom: 24rpx;
}

.empty-text {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
	display: block;
	margin-bottom: 12rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.7);
	display: block;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.popup-content {
	background: white;
	border-radius: 16rpx;
	width: 640rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 1rpx solid #f3f4f6;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #666;
	line-height: 1;
}

.form-group {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f3f4f6;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
}

.type-tabs {
	display: flex;
	gap: 16rpx;
}

.type-tab {
	flex: 1;
	text-align: center;
	padding: 16rpx;
	border: 2rpx solid #e5e7eb;
	border-radius: 8rpx;
	transition: all 0.3s;
}

.type-tab.active {
	border-color: #667eea;
	background: #667eea;
}

.type-tab text {
	font-size: 28rpx;
	color: #666;
}

.type-tab.active text {
	color: white;
}

.form-input, .form-textarea {
	width: 100%;
	padding: 16rpx;
	border: 2rpx solid #e5e7eb;
	border-radius: 8rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.picker-input {
	padding: 16rpx;
	border: 2rpx solid #e5e7eb;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
}

.popup-actions {
	display: flex;
	gap: 16rpx;
	padding: 32rpx;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	padding: 24rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
}

.cancel-btn {
	background: #f3f4f6;
	color: #666;
}

.confirm-btn {
	background: #667eea;
	color: white;
}
</style>
