<template>
  <view class="unlock-page">
    <view class="content">
      <!-- 应用图标和名称 -->
      <view class="app-info">
        <view class="app-icon">💰</view>
        <view class="app-name">碎米记账</view>
        <view class="app-subtitle">请验证身份以继续使用</view>
      </view>

      <!-- 指纹解锁区域 -->
      <view v-if="showFingerprintUnlock" class="fingerprint-section">
        <view class="fingerprint-icon" @click="tryFingerprintUnlock">
          <text class="icon">👆</text>
        </view>
        <view class="fingerprint-text">轻触指纹解锁</view>
        <view class="switch-to-pin" @click="switchToPinUnlock">
          <text>使用PIN码解锁</text>
        </view>
      </view>

      <!-- PIN码解锁区域 -->
      <view v-if="showPinUnlock" class="pin-section">
        <view class="pin-title">输入PIN码解锁</view>
        
        <!-- PIN码显示 -->
        <view class="pin-display">
          <view 
            v-for="(item, index) in 6" 
            :key="index"
            class="pin-dot"
            :class="{ 
              filled: index < currentPin.length,
              error: hasError && index < currentPin.length
            }"
          >
            <text v-if="index < currentPin.length">●</text>
          </view>
        </view>

        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-text">
          {{ errorMessage }}
        </view>

        <!-- 数字键盘 -->
        <view class="number-keyboard">
          <view class="keyboard-row" v-for="(row, rowIndex) in keyboard" :key="rowIndex">
            <view 
              v-for="(key, keyIndex) in row" 
              :key="keyIndex"
              class="keyboard-key"
              :class="{ 
                'key-number': typeof key === 'number',
                'key-action': typeof key === 'string'
              }"
              @click="handleKeyPress(key)"
            >
              <text v-if="key === 'delete'" class="iconfont">⌫</text>
              <text v-else-if="key === 'fingerprint'" class="iconfont">👆</text>
              <text v-else>{{ key }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 忘记PIN码 -->
      <view class="forgot-pin" @click="showForgotPinDialog">
        <text>忘记PIN码？</text>
      </view>
    </view>
  </view>
</template>

<script>
import SecurityManager from '@/utils/securityManager.js';

export default {
  name: 'UnlockPage',
  data() {
    return {
      currentPin: '',
      errorMessage: '',
      hasError: false,
      failedAttempts: 0,
      maxAttempts: 5,
      showFingerprintUnlock: false,
      showPinUnlock: true,
      keyboard: [
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9],
        ['fingerprint', 0, 'delete']
      ]
    };
  },
  async onLoad() {
    await this.initUnlockMethod();
  },
  methods: {
    /**
     * 初始化解锁方式
     */
    async initUnlockMethod() {
      try {
        const fingerprintEnabled = SecurityManager.isFingerprintEnabled();
        
        if (fingerprintEnabled) {
          this.showFingerprintUnlock = true;
          this.showPinUnlock = false;
          
          // 自动尝试指纹解锁
          setTimeout(() => {
            this.tryFingerprintUnlock();
          }, 500);
        } else {
          this.showFingerprintUnlock = false;
          this.showPinUnlock = true;
        }
        
        // 更新键盘布局
        this.updateKeyboard();
      } catch (error) {
        console.error('初始化解锁方式失败:', error);
        this.showPinUnlock = true;
      }
    },

    /**
     * 更新键盘布局
     */
    updateKeyboard() {
      if (this.showFingerprintUnlock && SecurityManager.isFingerprintEnabled()) {
        this.keyboard = [
          [1, 2, 3],
          [4, 5, 6],
          [7, 8, 9],
          ['fingerprint', 0, 'delete']
        ];
      } else {
        this.keyboard = [
          [1, 2, 3],
          [4, 5, 6],
          [7, 8, 9],
          ['', 0, 'delete']
        ];
      }
    },

    /**
     * 尝试指纹解锁
     */
    async tryFingerprintUnlock() {
      try {
        const success = await SecurityManager.verifyFingerprint();
        
        if (success) {
          this.unlockSuccess();
        } else {
          this.showError('指纹验证失败');
        }
      } catch (error) {
        console.error('指纹解锁失败:', error);
        this.showError('指纹解锁失败，请使用PIN码');
        this.switchToPinUnlock();
      }
    },

    /**
     * 切换到PIN码解锁
     */
    switchToPinUnlock() {
      this.showFingerprintUnlock = false;
      this.showPinUnlock = true;
      this.updateKeyboard();
    },

    /**
     * 处理按键点击
     */
    handleKeyPress(key) {
      if (key === '') return;
      
      if (key === 'delete') {
        this.currentPin = this.currentPin.slice(0, -1);
        this.clearError();
      } else if (key === 'fingerprint') {
        if (SecurityManager.isFingerprintEnabled()) {
          this.tryFingerprintUnlock();
        }
      } else if (typeof key === 'number') {
        if (this.currentPin.length < 6) {
          this.currentPin += key.toString();
          this.clearError();
          
          // 当输入满6位时自动验证
          if (this.currentPin.length === 6) {
            setTimeout(() => {
              this.verifyPin();
            }, 200);
          }
        }
      }
    },

    /**
     * 验证PIN码
     */
    async verifyPin() {
      try {
        const success = SecurityManager.verifyPin(this.currentPin);
        
        if (success) {
          this.unlockSuccess();
        } else {
          this.failedAttempts++;
          
          if (this.failedAttempts >= this.maxAttempts) {
            this.showError(`PIN码错误次数过多，请稍后再试`);
            // 可以在这里添加锁定逻辑
          } else {
            this.showError(`PIN码错误，还可尝试 ${this.maxAttempts - this.failedAttempts} 次`);
          }
          
          this.currentPin = '';
          this.hasError = true;
          
          setTimeout(() => {
            this.hasError = false;
          }, 1000);
        }
      } catch (error) {
        console.error('PIN码验证失败:', error);
        this.showError('验证失败，请重试');
        this.currentPin = '';
      }
    },

    /**
     * 解锁成功
     */
    unlockSuccess() {
      // 更新最后解锁时间
      SecurityManager.updateLastUnlockTime();
      
      uni.showToast({
        title: '解锁成功',
        icon: 'success',
        duration: 1000
      });
      
      // 返回到调用页面或首页
      setTimeout(() => {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          uni.navigateBack();
        } else {
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }
      }, 1000);
    },

    /**
     * 显示错误信息
     */
    showError(message) {
      this.errorMessage = message;
      setTimeout(() => {
        this.clearError();
      }, 3000);
    },

    /**
     * 清除错误信息
     */
    clearError() {
      this.errorMessage = '';
      this.hasError = false;
    },

    /**
     * 显示忘记PIN码对话框
     */
    showForgotPinDialog() {
      uni.showModal({
        title: '忘记PIN码',
        content: '忘记PIN码需要清除所有数据并重新设置。确定要继续吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.resetSecurity();
          }
        }
      });
    },

    /**
     * 重置安全设置
     */
    resetSecurity() {
      uni.showModal({
        title: '最后确认',
        content: '此操作将清除所有安全设置和应用数据，无法恢复！',
        confirmText: '清除',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            try {
              // 清除安全设置
              SecurityManager.disableSecurity();
              
              // 可以选择是否清除所有应用数据
              // StorageManager.clearAllData();
              
              uni.showToast({
                title: '安全设置已重置',
                icon: 'success'
              });
              
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/index/index'
                });
              }, 1500);
            } catch (error) {
              console.error('重置失败:', error);
              uni.showToast({
                title: '重置失败',
                icon: 'error'
              });
            }
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.unlock-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  width: 100%;
  max-width: 400px;
  padding: 40px 30px;
  text-align: center;
}

.app-info {
  margin-bottom: 60px;
}

.app-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.app-name {
  font-size: 28px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}

.app-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.fingerprint-section {
  margin-bottom: 40px;
}

.fingerprint-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fingerprint-icon:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.fingerprint-icon .icon {
  font-size: 40px;
}

.fingerprint-text {
  font-size: 18px;
  color: #fff;
  margin-bottom: 20px;
}

.switch-to-pin {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  text-decoration: underline;
}

.pin-section {
  margin-bottom: 40px;
}

.pin-title {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 30px;
}

.pin-display {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.pin-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  transition: all 0.3s ease;
}

.pin-dot.filled {
  border-color: #fff;
  background: #fff;
  color: #667eea;
}

.pin-dot.error {
  border-color: #FF5722;
  background: #FF5722;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-text {
  font-size: 14px;
  color: #FF5722;
  margin-bottom: 20px;
  min-height: 20px;
}

.number-keyboard {
  max-width: 300px;
  margin: 0 auto;
}

.keyboard-row {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.keyboard-key {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.keyboard-key:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.key-number {
  background: rgba(255, 255, 255, 0.2);
}

.key-action {
  background: rgba(255, 255, 255, 0.1);
  font-size: 20px;
}

.forgot-pin {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  text-decoration: underline;
}
</style>
