
.page[data-v-1c040d21] {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 1.25rem;
}
.header[data-v-1c040d21] {
	text-align: center;
	margin-bottom: 1.875rem;
}
.title[data-v-1c040d21] {
	font-size: 1.5rem;
	font-weight: bold;
	color: #333;
}
.test-section[data-v-1c040d21] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.test-item[data-v-1c040d21] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
	padding-bottom: 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
}
.test-item[data-v-1c040d21]:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}
.test-label[data-v-1c040d21] {
	font-size: 1rem;
	color: #333;
	font-weight: 500;
}
.test-result[data-v-1c040d21] {
	padding: 0.5rem 1rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	font-weight: bold;
}
.test-result.success[data-v-1c040d21] {
	background: #d4edda;
	color: #155724;
}
.test-result.error[data-v-1c040d21] {
	background: #f8d7da;
	color: #721c24;
}
.test-btn[data-v-1c040d21] {
	padding: 0.625rem 1.25rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	font-weight: bold;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
}
.test-btn[data-v-1c040d21]:disabled {
	background: #ccc;
	color: #999;
}
.error-details[data-v-1c040d21] {
	background: #f8d7da;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 1.25rem;
}
.error-title[data-v-1c040d21] {
	font-size: 0.875rem;
	color: #721c24;
	font-weight: bold;
	display: block;
	margin-bottom: 0.625rem;
}
.error-item[data-v-1c040d21] {
	font-size: 0.8125rem;
	color: #721c24;
	display: block;
	margin-bottom: 0.3125rem;
}
.results-section[data-v-1c040d21] {
	margin-top: 1.875rem;
	padding-top: 1.25rem;
	border-top: 0.0625rem solid #f0f0f0;
}
.results-title[data-v-1c040d21] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 1.25rem;
}
.result-item[data-v-1c040d21] {
	background: #f8f9fa;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
}
.result-header[data-v-1c040d21] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.625rem;
}
.result-name[data-v-1c040d21] {
	font-size: 0.9375rem;
	font-weight: bold;
	color: #333;
}
.result-status[data-v-1c040d21] {
	font-size: 1rem;
	font-weight: bold;
}
.result-status.success[data-v-1c040d21] {
	color: #28a745;
}
.result-status.error[data-v-1c040d21] {
	color: #dc3545;
}
.result-message[data-v-1c040d21] {
	font-size: 0.875rem;
	color: #333;
	display: block;
	margin-bottom: 0.3125rem;
}
.result-details[data-v-1c040d21] {
	font-size: 0.75rem;
	color: #666;
	display: block;
}
