# 平台兼容性说明

## 概述

碎米记账的语音识别功能已针对不同uni-app平台进行了适配，确保在各种环境下都能正常工作。

## 支持的平台

### 1. H5 (浏览器)
- **状态**: ✅ 完全支持
- **技术**: 使用浏览器原生 MediaRecorder API
- **要求**: 
  - Chrome 47+, Firefox 29+, Safari 14+, Edge 79+
  - HTTPS 协议（麦克风权限要求）
  - 用户授权麦克风权限

### 2. 微信小程序
- **状态**: ✅ 支持（模拟模式）
- **技术**: 使用 uni.getRecorderManager()
- **说明**: 
  - 当前使用模拟识别结果
  - 实际项目中需要将录音文件上传到服务器进行识别
  - 需要在小程序管理后台配置录音权限

### 3. App (iOS/Android)
- **状态**: ✅ 支持（模拟模式）
- **技术**: 使用 uni.getRecorderManager()
- **说明**: 
  - 当前使用模拟识别结果
  - 需要在 manifest.json 中配置录音权限
  - 实际项目中需要将录音文件上传到服务器进行识别

## 功能对比

| 功能 | H5 | 微信小程序 | App |
|------|----|-----------|----|
| 录音 | ✅ | ✅ | ✅ |
| 实时识别 | ✅ | ❌* | ❌* |
| 离线识别 | ❌ | ❌ | ❌ |
| 权限管理 | ✅ | ✅ | ✅ |

*注：小程序和App环境需要额外的服务器端处理

## 使用方法

### H5 环境
1. 确保使用 HTTPS 协议
2. 允许浏览器麦克风权限
3. 直接使用语音记账功能

### 微信小程序
1. 在小程序管理后台配置录音权限
2. 用户首次使用时会请求录音权限
3. 当前返回模拟识别结果

### App 环境
1. 在 manifest.json 中配置录音权限
2. 用户首次使用时会请求录音权限
3. 当前返回模拟识别结果

## 权限配置

### manifest.json 配置
```json
{
  "app-plus": {
    "modules": {
      "Audio": {}
    },
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
          "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>"
        ]
      },
      "ios": {
        "privacyDescription": {
          "NSMicrophoneUsageDescription": "此应用需要使用麦克风进行语音记账"
        }
      }
    }
  },
  "mp-weixin": {
    "permission": {
      "scope.record": {
        "desc": "您的麦克风将用于语音记账功能"
      }
    }
  }
}
```

## 错误处理

### 常见错误及解决方案

#### 1. "Cannot read property 'mediaDevices' of undefined"
- **原因**: 在非H5环境中调用了浏览器API
- **解决**: 已通过兼容性服务自动处理

#### 2. "当前环境不支持录音功能"
- **原因**: 平台不支持或权限未配置
- **解决**: 检查平台支持和权限配置

#### 3. "请允许访问麦克风权限"
- **原因**: 用户拒绝了麦克风权限
- **解决**: 引导用户在设置中开启权限

## 开发建议

### 1. 渐进式增强
```javascript
// 检查兼容性
const compatibility = speechRecognitionCompatService.checkCompatibility();
if (compatibility.supported) {
    // 显示语音按钮
    this.showVoiceButton = true;
} else {
    // 隐藏语音功能，提供替代方案
    this.showVoiceButton = false;
}
```

### 2. 用户友好的错误提示
```javascript
try {
    await speechRecognitionCompatService.startRecording();
} catch (error) {
    let message = '录音失败';
    if (error.message.includes('权限')) {
        message = '请允许访问麦克风权限';
    } else if (error.message.includes('不支持')) {
        message = '当前环境不支持录音功能';
    }
    
    uni.showToast({
        title: message,
        icon: 'none'
    });
}
```

### 3. 平台特定处理
```javascript
// 根据平台提供不同的用户指导
switch (compatibility.platform) {
    case 'H5':
        this.tips = '请确保使用HTTPS协议并允许麦克风权限';
        break;
    case 'MP-WEIXIN':
        this.tips = '首次使用需要授权录音权限';
        break;
    case 'APP-PLUS':
        this.tips = '请在设置中允许应用使用麦克风';
        break;
}
```

## 未来规划

### 1. 完整的小程序/App支持
- 实现录音文件上传
- 服务器端语音识别
- 离线识别能力

### 2. 性能优化
- 音频压缩
- 流式识别
- 缓存机制

### 3. 功能增强
- 多语言支持
- 自定义唤醒词
- 语音命令扩展

## 测试建议

1. **多平台测试**: 在H5、微信小程序、App中分别测试
2. **权限测试**: 测试权限拒绝和重新授权的情况
3. **网络测试**: 测试网络异常时的处理
4. **兼容性测试**: 在不同版本的浏览器/微信中测试

## 总结

通过兼容性服务的封装，语音识别功能现在可以在所有主要的uni-app平台上运行。H5环境提供完整的实时识别功能，而小程序和App环境目前使用模拟数据，为后续的完整实现奠定了基础。

开发者可以根据实际需求选择合适的实现方案，并通过渐进式增强的方式为用户提供最佳体验。
