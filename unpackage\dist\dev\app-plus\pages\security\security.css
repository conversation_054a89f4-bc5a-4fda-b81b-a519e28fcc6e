
.security-page[data-v-841ba230] {
  min-height: 100vh;
  background: #f5f5f5;
}
.custom-navbar[data-v-841ba230] {
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.navbar-content[data-v-841ba230] {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.navbar-left[data-v-841ba230] {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.icon-back[data-v-841ba230] {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}
.navbar-title[data-v-841ba230] {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}
.navbar-right[data-v-841ba230] {
  width: 60px;
}
.content[data-v-841ba230] {
  padding: 20px;
}
.security-status-card[data-v-841ba230] {
  background: #fff;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.status-header[data-v-841ba230] {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.status-icon[data-v-841ba230] {
  font-size: 32px;
  margin-right: 15px;
}
.status-info[data-v-841ba230] {
  flex: 1;
}
.status-title[data-v-841ba230] {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 5px;
}
.status-subtitle[data-v-841ba230] {
  font-size: 14px;
  color: #666;
}
.status-details[data-v-841ba230] {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}
.detail-item[data-v-841ba230] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.detail-item[data-v-841ba230]:last-child {
  margin-bottom: 0;
}
.detail-label[data-v-841ba230] {
  font-size: 14px;
  color: #666;
}
.detail-value[data-v-841ba230] {
  font-size: 14px;
  font-weight: 500;
}
.detail-value.success[data-v-841ba230] {
  color: #4CAF50;
}
.detail-value.muted[data-v-841ba230] {
  color: #999;
}
.settings-section[data-v-841ba230] {
  margin-bottom: 30px;
}
.section-title[data-v-841ba230] {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-left: 5px;
}
.setting-item[data-v-841ba230] {
  background: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}
.setting-item[data-v-841ba230]:active {
  transform: scale(0.98);
}
.setting-item.danger[data-v-841ba230] {
  border-left: 3px solid #FF5722;
}
.setting-left[data-v-841ba230] {
  display: flex;
  align-items: center;
  flex: 1;
}
.setting-icon[data-v-841ba230] {
  font-size: 24px;
  margin-right: 15px;
}
.setting-info[data-v-841ba230] {
  flex: 1;
}
.setting-title[data-v-841ba230] {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 3px;
}
.setting-subtitle[data-v-841ba230] {
  font-size: 13px;
  color: #666;
}
.setting-right[data-v-841ba230] {
  display: flex;
  align-items: center;
}
.setting-status[data-v-841ba230] {
  font-size: 13px;
  color: #999;
  margin-right: 8px;
}
.setting-status.success[data-v-841ba230] {
  color: #4CAF50;
}
.setting-status.muted[data-v-841ba230] {
  color: #ccc;
}
.arrow[data-v-841ba230] {
  font-size: 16px;
  color: #ccc;
  font-weight: bold;
}
.security-tips[data-v-841ba230] {
  background: #fff;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.tips-title[data-v-841ba230] {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}
.tip-item[data-v-841ba230] {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}
.tip-item[data-v-841ba230]:last-child {
  margin-bottom: 0;
}
.tip-icon[data-v-841ba230] {
  font-size: 16px;
  margin-right: 10px;
  margin-top: 2px;
}
.tip-text[data-v-841ba230] {
  flex: 1;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
