<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">账户管理</text>
				<text class="header-action" @click="addAccount">+</text>
			</view>
		</view>
		
		<!-- 总资产 -->
		<view class="total-assets">
			<text class="total-label">总资产</text>
			<text class="total-amount">¥{{totalAssets.toFixed(2)}}</text>
		</view>
		
		<!-- 账户列表 -->
		<view class="accounts-section">
			<view class="section-title">
				<text>我的账户</text>
			</view>
			
			<view class="accounts-list">
				<view 
					v-for="account in accounts" 
					:key="account.id" 
					class="account-item"
					@click="viewAccount(account)"
				>
					<view class="account-left">
						<view class="account-icon" :style="{backgroundColor: account.color}">
							<text class="icon-text">{{account.icon}}</text>
						</view>
						<view class="account-info">
							<text class="account-name">{{account.name}}</text>
							<text class="account-type">{{getAccountTypeName(account.type)}}</text>
						</view>
					</view>
					<view class="account-right">
						<text class="account-balance" :class="getBalanceClass(account)">
							¥{{account.balance.toFixed(2)}}
						</text>
						<text class="account-arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 快速操作 -->
		<view class="quick-actions">
			<view class="action-item" @click="addAccount">
				<text class="action-icon">➕</text>
				<text class="action-text">添加账户</text>
			</view>
			<view class="action-item" @click="transferMoney">
				<text class="action-icon">🔄</text>
				<text class="action-text">转账</text>
			</view>
			<view class="action-item" @click="adjustBalance">
				<text class="action-icon">⚖️</text>
				<text class="action-text">余额调整</text>
			</view>
		</view>
		
		<!-- 添加/编辑账户弹窗 -->
		<view v-if="showPopup" class="popup-overlay" @click="closePopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingAccount ? '编辑账户' : '添加账户'}}</text>
					<text class="popup-close" @click="closePopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">账户名称</text>
						<input 
							class="form-input" 
							v-model="accountForm.name" 
							placeholder="请输入账户名称"
							maxlength="20"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">账户类型</text>
						<view class="type-selector">
							<view 
								v-for="type in accountTypes" 
								:key="type.value"
								class="type-item"
								:class="{active: accountForm.type === type.value}"
								@click="selectAccountType(type.value)"
							>
								<text class="type-icon">{{type.icon}}</text>
								<text class="type-name">{{type.name}}</text>
							</view>
						</view>
					</view>
					
					<view class="form-item">
						<text class="form-label">初始余额</text>
						<input 
							class="form-input" 
							v-model="accountForm.balance" 
							type="digit"
							placeholder="0.00"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">图标颜色</text>
						<view class="color-selector">
							<view 
								v-for="color in accountColors" 
								:key="color"
								class="color-item"
								:class="{active: accountForm.color === color}"
								:style="{backgroundColor: color}"
								@click="selectColor(color)"
							></view>
						</view>
					</view>
				</view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closePopup">取消</button>
					<button class="save-btn" @click="saveAccount">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createAccount, validateAccount } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			accounts: [],
			showPopup: false,
			editingAccount: null,
			accountForm: {
				name: '',
				type: 'cash',
				balance: '',
				icon: '💰',
				color: '#4CAF50'
			},
			accountTypes: [
				{ value: 'cash', name: '现金', icon: '💵' },
				{ value: 'bank_card', name: '储蓄卡', icon: '💳' },
				{ value: 'credit_card', name: '信用卡', icon: '💳' },
				{ value: 'alipay', name: '支付宝', icon: '📱' },
				{ value: 'wechat', name: '微信支付', icon: '📱' },
				{ value: 'investment', name: '投资账户', icon: '📈' },
				{ value: 'loan', name: '贷款账户', icon: '🏦' },
				{ value: 'other', name: '其他', icon: '💰' }
			],
			accountColors: [
				'#4CAF50', '#2196F3', '#FF9800', '#F44336', 
				'#9C27B0', '#607D8B', '#795548', '#FF5722',
				'#3F51B5', '#009688', '#CDDC39', '#FFC107'
			]
		};
	},
	computed: {
		totalAssets() {
			return this.accounts.reduce((total, account) => {
				// 负债账户（如信用卡、贷款）余额为负数时不计入总资产
				if (account.type === 'credit_card' || account.type === 'loan') {
					return total - Math.abs(account.balance);
				}
				return total + account.balance;
			}, 0);
		}
	},
	onLoad() {
		this.loadAccounts();
	},
	methods: {
		loadAccounts() {
			this.accounts = StorageManager.getAccounts();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		addAccount() {
			this.editingAccount = null;
			this.resetForm();
			this.showPopup = true;
		},
		
		viewAccount(account) {
			uni.navigateTo({
				url: `/pages/account-detail/account-detail?id=${account.id}`
			});
		},
		
		transferMoney() {
			uni.navigateTo({
				url: '/pages/transfer/transfer'
			});
		},
		
		adjustBalance() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		
		resetForm() {
			this.accountForm = {
				name: '',
				type: 'cash',
				balance: '',
				icon: '💰',
				color: '#4CAF50'
			};
		},
		
		selectAccountType(type) {
			this.accountForm.type = type;
			const typeInfo = this.accountTypes.find(t => t.value === type);
			if (typeInfo) {
				this.accountForm.icon = typeInfo.icon;
			}
		},
		
		selectColor(color) {
			this.accountForm.color = color;
		},
		
		closePopup() {
			this.$refs.accountPopup.close();
		},
		
		saveAccount() {
			// 验证表单
			const accountData = {
				...this.accountForm,
				balance: parseFloat(this.accountForm.balance) || 0
			};
			
			const validation = validateAccount(accountData);
			if (!validation.valid) {
				uni.showToast({
					title: validation.errors[0],
					icon: 'none'
				});
				return;
			}
			
			if (this.editingAccount) {
				// 编辑账户
				const index = this.accounts.findIndex(a => a.id === this.editingAccount.id);
				if (index !== -1) {
					this.accounts[index] = {
						...this.editingAccount,
						...accountData,
						updatedAt: Date.now()
					};
				}
			} else {
				// 添加新账户
				const newAccount = createAccount(accountData);
				this.accounts.push(newAccount);
			}
			
			StorageManager.saveAccounts(this.accounts);
			this.closePopup();
			
			uni.showToast({
				title: this.editingAccount ? '账户已更新' : '账户已添加',
				icon: 'success'
			});
		},
		
		closePopup() {
			this.showPopup = false;
		},
		
		getAccountTypeName(type) {
			const typeInfo = this.accountTypes.find(t => t.value === type);
			return typeInfo ? typeInfo.name : '未知类型';
		},
		
		getBalanceClass(account) {
			if (account.type === 'credit_card' || account.type === 'loan') {
				return account.balance > 0 ? 'debt' : 'positive';
			}
			return account.balance >= 0 ? 'positive' : 'negative';
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 总资产 */
.total-assets {
	background: white;
	margin: -20rpx 40rpx 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.total-label {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.total-amount {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}

/* 账户列表 */
.accounts-section {
	margin: 0 40rpx 40rpx;
}

.section-title {
	padding: 20rpx 0;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.accounts-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.account-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.account-item:last-child {
	border-bottom: none;
}

.account-item:active {
	background-color: #f8f9fa;
}

.account-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.account-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
}

.account-info {
	flex: 1;
}

.account-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.account-type {
	font-size: 26rpx;
	color: #666;
}

.account-right {
	display: flex;
	align-items: center;
}

.account-balance {
	font-size: 32rpx;
	font-weight: bold;
	margin-right: 16rpx;
}

.account-balance.positive {
	color: #4CAF50;
}

.account-balance.negative {
	color: #F44336;
}

.account-balance.debt {
	color: #FF9800;
}

.account-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 快速操作 */
.quick-actions {
	margin: 0 40rpx;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.action-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.action-item:active {
	transform: translateY(2rpx);
}

.action-icon {
	font-size: 40rpx;
	display: block;
	margin-bottom: 12rpx;
}

.action-text {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

/* 弹窗样式 */
.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

.type-selector {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16rpx;
}

.type-item {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s;
}

.type-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.type-icon {
	font-size: 28rpx;
	display: block;
	margin-bottom: 8rpx;
}

.type-name {
	font-size: 22rpx;
}

.color-selector {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.color-item {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	border: 4rpx solid transparent;
	transition: all 0.3s;
}

.color-item.active {
	border-color: #333;
	transform: scale(1.1);
}

.popup-actions {
	display: flex;
	gap: 20rpx;
}

.cancel-btn, .save-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
	border: none;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
}
</style>
