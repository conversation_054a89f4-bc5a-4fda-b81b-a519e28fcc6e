<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">报销管理</text>
				<text class="add-btn" @click="showAddDialog">+</text>
			</view>
		</view>

		<!-- 统计卡片 -->
		<view class="stats-cards">
			<view class="stat-card">
				<text class="stat-label">待报销</text>
				<text class="stat-value pending">¥{{pendingAmount.toFixed(2)}}</text>
			</view>
			<view class="stat-card">
				<text class="stat-label">已报销</text>
				<text class="stat-value approved">¥{{approvedAmount.toFixed(2)}}</text>
			</view>
			<view class="stat-card">
				<text class="stat-label">本月总计</text>
				<text class="stat-value total">¥{{monthlyTotal.toFixed(2)}}</text>
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'all'}"
				@click="setFilter('all')"
			>
				<text>全部</text>
			</view>
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'pending'}"
				@click="setFilter('pending')"
			>
				<text>待报销</text>
			</view>
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'approved'}"
				@click="setFilter('approved')"
			>
				<text>已报销</text>
			</view>
			<view 
				class="filter-tab" 
				:class="{active: currentFilter === 'rejected'}"
				@click="setFilter('rejected')"
			>
				<text>已拒绝</text>
			</view>
		</view>

		<!-- 报销列表 -->
		<view class="reimbursement-list">
			<view 
				v-for="item in filteredReimbursements" 
				:key="item.id"
				class="reimbursement-item"
				@click="showItemDetail(item)"
			>
				<view class="item-header">
					<view class="item-info">
						<text class="item-title">{{item.title}}</text>
						<text class="item-category">{{getCategoryName(item.categoryId)}}</text>
					</view>
					<view class="item-amount">
						<text class="amount">¥{{item.amount.toFixed(2)}}</text>
						<text class="status" :class="item.status">{{getStatusText(item.status)}}</text>
					</view>
				</view>
				
				<view class="item-details">
					<text class="item-date">{{formatDate(item.date)}}</text>
					<text class="item-company">{{item.company || '默认公司'}}</text>
				</view>
				
				<text v-if="item.description" class="item-description">{{item.description}}</text>
				
				<view v-if="item.attachments && item.attachments.length > 0" class="attachments">
					<text class="attachment-label">附件:</text>
					<view class="attachment-list">
						<text 
							v-for="(attachment, index) in item.attachments" 
							:key="index"
							class="attachment-item"
						>
							📎 {{attachment.name}}
						</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="filteredReimbursements.length === 0" class="empty-state">
			<text class="empty-icon">🧾</text>
			<text class="empty-text">暂无报销记录</text>
			<text class="empty-desc">点击右上角 + 号添加报销申请</text>
		</view>

		<!-- 添加报销弹窗 -->
		<view v-if="showDialog" class="popup-overlay" @click="closeDialog">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingItem ? '编辑报销' : '添加报销'}}</text>
					<text class="popup-close" @click="closeDialog">×</text>
				</view>
				
				<view class="form-group">
					<text class="form-label">报销标题</text>
					<input 
						class="form-input" 
						v-model="formData.title" 
						placeholder="请输入报销标题"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">报销分类</text>
					<picker :range="categoryOptions" range-key="name" @change="onCategoryChange">
						<view class="picker-input">
							<text>{{selectedCategoryName || '选择分类'}}</text>
						</view>
					</picker>
				</view>
				
				<view class="form-group">
					<text class="form-label">金额</text>
					<input 
						class="form-input" 
						type="digit"
						v-model="formData.amount" 
						placeholder="请输入金额"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">公司</text>
					<input 
						class="form-input" 
						v-model="formData.company" 
						placeholder="请输入公司名称"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">日期</text>
					<picker mode="date" :value="formData.date" @change="onDateChange">
						<view class="picker-input">
							<text>{{formData.date || '选择日期'}}</text>
						</view>
					</picker>
				</view>
				
				<view class="form-group">
					<text class="form-label">描述</text>
					<textarea 
						class="form-textarea" 
						v-model="formData.description" 
						placeholder="请输入报销描述（可选）"
						maxlength="200"
					></textarea>
				</view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeDialog">取消</button>
					<button class="confirm-btn" @click="saveReimbursement">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import { createReimbursement, validateReimbursement } from '@/utils/advancedModels.js';

export default {
	data() {
		return {
			reimbursements: [],
			currentFilter: 'all',
			showDialog: false,
			editingItem: null,
			formData: {
				title: '',
				categoryId: '',
				amount: '',
				company: '',
				date: '',
				description: ''
			},
			categoryOptions: [
				{ id: 'business_meal', name: '商务餐饮' },
				{ id: 'travel', name: '差旅交通' },
				{ id: 'accommodation', name: '住宿费' },
				{ id: 'office_supplies', name: '办公用品' },
				{ id: 'communication', name: '通讯费' },
				{ id: 'training', name: '培训费' },
				{ id: 'entertainment', name: '商务招待' },
				{ id: 'other', name: '其他报销' }
			]
		};
	},
	
	computed: {
		// 筛选后的报销列表
		filteredReimbursements() {
			let filtered = this.reimbursements;
			
			if (this.currentFilter !== 'all') {
				filtered = this.reimbursements.filter(item => item.status === this.currentFilter);
			}
			
			return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
		},
		
		// 待报销金额
		pendingAmount() {
			return this.reimbursements
				.filter(item => item.status === 'pending')
				.reduce((sum, item) => sum + item.amount, 0);
		},
		
		// 已报销金额
		approvedAmount() {
			return this.reimbursements
				.filter(item => item.status === 'approved')
				.reduce((sum, item) => sum + item.amount, 0);
		},
		
		// 本月总计
		monthlyTotal() {
			const currentMonth = new Date().getMonth();
			const currentYear = new Date().getFullYear();
			
			return this.reimbursements
				.filter(item => {
					const itemDate = new Date(item.date);
					return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
				})
				.reduce((sum, item) => sum + item.amount, 0);
		},
		
		// 选中的分类名称
		selectedCategoryName() {
			const category = this.categoryOptions.find(cat => cat.id === this.formData.categoryId);
			return category ? category.name : '';
		}
	},
	
	onLoad() {
		this.loadReimbursements();
		this.initDate();
	},
	
	methods: {
		// 加载报销数据
		async loadReimbursements() {
			try {
				this.reimbursements = await StorageManager.getReimbursements();
			} catch (error) {
				console.error('加载报销数据失败:', error);
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				});
			}
		},
		
		// 初始化日期
		initDate() {
			const today = new Date();
			this.formData.date = this.formatDateForPicker(today);
		},
		
		// 设置筛选条件
		setFilter(filter) {
			this.currentFilter = filter;
		},
		
		// 显示添加对话框
		showAddDialog() {
			this.editingItem = null;
			this.resetForm();
			this.showDialog = true;
		},
		
		// 显示报销详情
		showItemDetail(item) {
			const statusText = this.getStatusText(item.status);
			const categoryName = this.getCategoryName(item.categoryId);
			
			uni.showModal({
				title: '报销详情',
				content: `标题：${item.title}\n分类：${categoryName}\n金额：¥${item.amount.toFixed(2)}\n公司：${item.company || '默认公司'}\n状态：${statusText}\n日期：${this.formatDate(item.date)}\n描述：${item.description || '无'}`,
				showCancel: false
			});
		},
		
		// 关闭对话框
		closeDialog() {
			this.showDialog = false;
			this.editingItem = null;
			this.resetForm();
		},
		
		// 重置表单
		resetForm() {
			this.formData = {
				title: '',
				categoryId: '',
				amount: '',
				company: '',
				date: this.formatDateForPicker(new Date()),
				description: ''
			};
		},
		
		// 分类变化
		onCategoryChange(e) {
			const index = e.detail.value;
			this.formData.categoryId = this.categoryOptions[index].id;
		},
		
		// 日期变化
		onDateChange(e) {
			this.formData.date = e.detail.value;
		},
		
		// 保存报销
		async saveReimbursement() {
			try {
				// 验证表单
				if (!this.formData.title.trim()) {
					uni.showToast({
						title: '请输入报销标题',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formData.categoryId) {
					uni.showToast({
						title: '请选择报销分类',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formData.amount || parseFloat(this.formData.amount) <= 0) {
					uni.showToast({
						title: '请输入有效金额',
						icon: 'none'
					});
					return;
				}
				
				// 创建报销记录
				const reimbursementData = {
					...this.formData,
					amount: parseFloat(this.formData.amount),
					status: 'pending',
					attachments: []
				};
				
				const reimbursement = createReimbursement(reimbursementData);
				const validation = validateReimbursement(reimbursement);
				
				if (!validation.isValid) {
					uni.showToast({
						title: validation.errors[0],
						icon: 'none'
					});
					return;
				}
				
				// 保存到存储
				await StorageManager.addReimbursement(reimbursement);
				
				// 重新加载数据
				await this.loadReimbursements();
				
				// 关闭对话框
				this.closeDialog();
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
			} catch (error) {
				console.error('保存报销失败:', error);
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			}
		},
		
		// 获取分类名称
		getCategoryName(categoryId) {
			const category = this.categoryOptions.find(cat => cat.id === categoryId);
			return category ? category.name : '未知分类';
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				pending: '待报销',
				approved: '已报销',
				rejected: '已拒绝'
			};
			return statusMap[status] || '未知状态';
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 格式化日期显示
		formatDate(dateStr) {
			const date = new Date(dateStr);
			const now = new Date();
			const diffTime = now - date;
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
			
			if (diffDays === 0) {
				return '今天';
			} else if (diffDays === 1) {
				return '昨天';
			} else if (diffDays < 7) {
				return `${diffDays}天前`;
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		
		// 格式化日期为picker格式
		formatDateForPicker(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		}
	}
};
</script>

<style scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	padding: 44rpx 32rpx 32rpx;
}

.header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.back-btn, .add-btn {
	color: white;
	font-size: 36rpx;
	font-weight: bold;
	width: 60rpx;
	text-align: center;
}

.header-title {
	color: white;
	font-size: 36rpx;
	font-weight: bold;
}

.stats-cards {
	display: flex;
	padding: 32rpx;
	gap: 16rpx;
}

.stat-card {
	flex: 1;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.stat-value {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
}

.stat-value.pending {
	color: #f59e0b;
}

.stat-value.approved {
	color: #10b981;
}

.stat-value.total {
	color: #6366f1;
}

.filter-tabs {
	display: flex;
	margin: 0 32rpx 32rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 12rpx;
	padding: 8rpx;
}

.filter-tab {
	flex: 1;
	text-align: center;
	padding: 16rpx;
	border-radius: 8rpx;
	transition: all 0.3s;
}

.filter-tab.active {
	background: rgba(255, 255, 255, 0.2);
}

.filter-tab text {
	color: white;
	font-size: 28rpx;
}

.reimbursement-list {
	padding: 0 32rpx;
}

.reimbursement-item {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.item-category {
	font-size: 24rpx;
	color: #666;
	background: #f3f4f6;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.item-amount {
	text-align: right;
}

.amount {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	color: white;
}

.status.pending {
	background: #f59e0b;
}

.status.approved {
	background: #10b981;
}

.status.rejected {
	background: #ef4444;
}

.item-details {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.item-date, .item-company {
	font-size: 24rpx;
	color: #666;
}

.item-description {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 12rpx;
}

.attachments {
	border-top: 1rpx solid #f3f4f6;
	padding-top: 12rpx;
}

.attachment-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.attachment-list {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.attachment-item {
	font-size: 22rpx;
	color: #6366f1;
	background: #f0f9ff;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.empty-state {
	text-align: center;
	padding: 120rpx 32rpx;
}

.empty-icon {
	font-size: 120rpx;
	display: block;
	margin-bottom: 24rpx;
}

.empty-text {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
	display: block;
	margin-bottom: 12rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.7);
	display: block;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.popup-content {
	background: white;
	border-radius: 16rpx;
	width: 640rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 1rpx solid #f3f4f6;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #666;
	line-height: 1;
}

.form-group {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f3f4f6;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
}

.form-input, .form-textarea {
	width: 100%;
	padding: 16rpx;
	border: 2rpx solid #e5e7eb;
	border-radius: 8rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.picker-input {
	padding: 16rpx;
	border: 2rpx solid #e5e7eb;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
}

.popup-actions {
	display: flex;
	gap: 16rpx;
	padding: 32rpx;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	padding: 24rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
}

.cancel-btn {
	background: #f3f4f6;
	color: #666;
}

.confirm-btn {
	background: #667eea;
	color: white;
}
</style>
