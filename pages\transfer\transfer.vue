<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">转账</text>
			</view>
		</view>
		
		<!-- 转账表单 -->
		<view class="transfer-form">
			<!-- 转出账户 -->
			<view class="form-section">
				<view class="section-title">
					<text>转出账户</text>
				</view>
				<view class="account-selector" @click="selectFromAccount">
					<view v-if="fromAccount" class="selected-account">
						<view class="account-icon" :style="{backgroundColor: fromAccount.color}">
							<text class="icon-text">{{fromAccount.icon}}</text>
						</view>
						<view class="account-info">
							<text class="account-name">{{fromAccount.name}}</text>
							<text class="account-balance">余额: ¥{{fromAccount.balance.toFixed(2)}}</text>
						</view>
					</view>
					<view v-else class="placeholder">
						<text class="placeholder-text">请选择转出账户</text>
					</view>
					<text class="selector-arrow">></text>
				</view>
			</view>
			
			<!-- 转入账户 -->
			<view class="form-section">
				<view class="section-title">
					<text>转入账户</text>
				</view>
				<view class="account-selector" @click="selectToAccount">
					<view v-if="toAccount" class="selected-account">
						<view class="account-icon" :style="{backgroundColor: toAccount.color}">
							<text class="icon-text">{{toAccount.icon}}</text>
						</view>
						<view class="account-info">
							<text class="account-name">{{toAccount.name}}</text>
							<text class="account-balance">余额: ¥{{toAccount.balance.toFixed(2)}}</text>
						</view>
					</view>
					<view v-else class="placeholder">
						<text class="placeholder-text">请选择转入账户</text>
					</view>
					<text class="selector-arrow">></text>
				</view>
			</view>
			
			<!-- 转账金额 -->
			<view class="form-section">
				<view class="section-title">
					<text>转账金额</text>
				</view>
				<view class="amount-input-container">
					<text class="currency">¥</text>
					<input 
						class="amount-input" 
						type="digit" 
						v-model="amount" 
						placeholder="0.00"
						focus
					/>
				</view>
			</view>
			
			<!-- 备注 -->
			<view class="form-section">
				<view class="section-title">
					<text>备注</text>
				</view>
				<textarea 
					class="note-input" 
					v-model="note" 
					placeholder="添加转账备注（可选）"
					maxlength="100"
				></textarea>
			</view>
			
			<!-- 日期时间 -->
			<view class="form-section">
				<view class="datetime-row">
					<view class="datetime-item">
						<text class="datetime-label">日期</text>
						<picker mode="date" :value="dateStr" @change="onDateChange">
							<view class="datetime-value">{{dateStr}}</view>
						</picker>
					</view>
					<view class="datetime-item">
						<text class="datetime-label">时间</text>
						<picker mode="time" :value="timeStr" @change="onTimeChange">
							<view class="datetime-value">{{timeStr}}</view>
						</picker>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-buttons">
			<button class="transfer-btn" @click="confirmTransfer">确认转账</button>
		</view>
		
		<!-- 账户选择弹窗 -->
		<view v-if="showAccountPopup" class="popup-overlay" @click="closeAccountPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">选择账户</text>
					<text class="popup-close" @click="closeAccountPopup">×</text>
				</view>
				<view class="accounts-list">
					<view 
						v-for="account in availableAccounts" 
						:key="account.id"
						class="account-item"
						@click="selectAccount(account)"
					>
						<view class="account-icon" :style="{backgroundColor: account.color}">
							<text class="icon-text">{{account.icon}}</text>
						</view>
						<view class="account-info">
							<text class="account-name">{{account.name}}</text>
							<text class="account-balance">余额: ¥{{account.balance.toFixed(2)}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createRecord, validateRecord } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			accounts: [],
			showAccountPopup: false,
			fromAccount: null,
			toAccount: null,
			amount: '',
			note: '',
			dateStr: '',
			timeStr: '',
			selectingType: '' // 'from' 或 'to'
		};
	},
	computed: {
		availableAccounts() {
			if (this.selectingType === 'from') {
				return this.accounts;
			} else if (this.selectingType === 'to') {
				return this.accounts.filter(account => 
					!this.fromAccount || account.id !== this.fromAccount.id
				);
			}
			return this.accounts;
		}
	},
	onLoad() {
		this.loadAccounts();
		this.initDateTime();
	},
	methods: {
		loadAccounts() {
			this.accounts = StorageManager.getAccounts();
		},
		
		initDateTime() {
			const now = new Date();
			this.dateStr = this.formatDate(now);
			this.timeStr = this.formatTime(now);
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		selectFromAccount() {
			this.selectingType = 'from';
			this.showAccountPopup = true;
		},
		
		selectToAccount() {
			this.selectingType = 'to';
			this.showAccountPopup = true;
		},
		
		selectAccount(account) {
			if (this.selectingType === 'from') {
				this.fromAccount = account;
			} else if (this.selectingType === 'to') {
				this.toAccount = account;
			}
			this.closeAccountPopup();
		},
		
		closeAccountPopup() {
			this.showAccountPopup = false;
		},
		
		onDateChange(e) {
			this.dateStr = e.detail.value;
		},
		
		onTimeChange(e) {
			this.timeStr = e.detail.value;
		},
		
		confirmTransfer() {
			// 验证表单
			if (!this.fromAccount) {
				uni.showToast({
					title: '请选择转出账户',
					icon: 'none'
				});
				return;
			}
			
			if (!this.toAccount) {
				uni.showToast({
					title: '请选择转入账户',
					icon: 'none'
				});
				return;
			}
			
			if (this.fromAccount.id === this.toAccount.id) {
				uni.showToast({
					title: '转出和转入账户不能相同',
					icon: 'none'
				});
				return;
			}
			
			const transferAmount = parseFloat(this.amount);
			if (!transferAmount || transferAmount <= 0) {
				uni.showToast({
					title: '请输入正确的转账金额',
					icon: 'none'
				});
				return;
			}
			
			if (transferAmount > this.fromAccount.balance) {
				uni.showToast({
					title: '转账金额不能超过账户余额',
					icon: 'none'
				});
				return;
			}
			
			// 确认转账
			uni.showModal({
				title: '确认转账',
				content: `从 ${this.fromAccount.name} 转账 ¥${transferAmount.toFixed(2)} 到 ${this.toAccount.name}？`,
				success: (res) => {
					if (res.confirm) {
						this.executeTransfer(transferAmount);
					}
				}
			});
		},
		
		executeTransfer(transferAmount) {
			try {
				// 创建转账记录
				const transferDate = new Date(`${this.dateStr} ${this.timeStr}`).getTime();
				const transferRecord = createRecord({
					type: 'transfer',
					accountId: this.fromAccount.id,
					toAccountId: this.toAccount.id,
					amount: transferAmount,
					note: this.note || `从${this.fromAccount.name}转账到${this.toAccount.name}`,
					date: transferDate
				});
				
				// 更新账户余额
				const fromIndex = this.accounts.findIndex(a => a.id === this.fromAccount.id);
				const toIndex = this.accounts.findIndex(a => a.id === this.toAccount.id);
				
				if (fromIndex !== -1) {
					this.accounts[fromIndex].balance -= transferAmount;
					this.accounts[fromIndex].updatedAt = Date.now();
				}
				
				if (toIndex !== -1) {
					this.accounts[toIndex].balance += transferAmount;
					this.accounts[toIndex].updatedAt = Date.now();
				}
				
				// 保存数据
				const records = StorageManager.getRecords();
				records.push(transferRecord);
				StorageManager.saveRecords(records);
				StorageManager.saveAccounts(this.accounts);
				
				uni.showToast({
					title: '转账成功',
					icon: 'success'
				});
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				
			} catch (error) {
				console.error('转账失败:', error);
				uni.showToast({
					title: '转账失败',
					icon: 'error'
				});
			}
		},
		
		formatDate(date) {
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		
		formatTime(date) {
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${hours}:${minutes}`;
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 表单样式 */
.transfer-form {
	padding: 40rpx;
}

.form-section {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

/* 账户选择器 */
.account-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	transition: background-color 0.2s;
}

.account-selector:active {
	background-color: #e9ecef;
}

.selected-account {
	display: flex;
	align-items: center;
	flex: 1;
}

.account-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.icon-text {
	font-size: 24rpx;
}

.account-info {
	flex: 1;
}

.account-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.account-balance {
	font-size: 24rpx;
	color: #666;
}

.placeholder {
	flex: 1;
}

.placeholder-text {
	font-size: 28rpx;
	color: #999;
}

.selector-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 金额输入 */
.amount-input-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
}

.currency {
	font-size: 48rpx;
	color: #333;
	font-weight: bold;
	margin-right: 10rpx;
}

.amount-input {
	font-size: 60rpx;
	color: #333;
	font-weight: bold;
	text-align: center;
	border: none;
	outline: none;
	min-width: 200rpx;
	background: transparent;
}

/* 备注输入 */
.note-input {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	outline: none;
	font-size: 28rpx;
	color: #333;
	resize: none;
}

/* 日期时间 */
.datetime-row {
	display: flex;
	gap: 20rpx;
}

.datetime-item {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.datetime-label {
	font-size: 28rpx;
	color: #333;
}

.datetime-value {
	font-size: 28rpx;
	color: #667eea;
}

/* 操作按钮 */
.action-buttons {
	padding: 0 40rpx 40rpx;
}

.transfer-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.transfer-btn:active {
	transform: scale(0.98);
}

/* 弹窗样式 */
.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	max-height: 60vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.accounts-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.account-item {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	background: #f8f9fa;
	transition: background-color 0.2s;
}

.account-item:active {
	background-color: #e9ecef;
}

.account-item:last-child {
	margin-bottom: 0;
}
</style>
