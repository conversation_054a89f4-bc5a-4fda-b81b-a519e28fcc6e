<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="back-btn" @click="goBack" :style="{ color: themeColors.textReverse }">←</text>
			<text class="header-title" :style="{ color: themeColors.textReverse }">使用帮助</text>
		</view>
		
		<!-- 帮助内容 -->
		<view class="help-content">
			<!-- 快速入门 -->
			<view class="help-section" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="section-header">
					<text class="section-icon">🚀</text>
					<text class="section-title" :style="{ color: themeColors.textPrimary }">快速入门</text>
				</view>
				<view class="help-item">
					<text class="item-title" :style="{ color: themeColors.textPrimary }">1. 开始记账</text>
					<text class="item-content" :style="{ color: themeColors.textSecondary }">点击首页右下角的“+”按钮或快速操作中的“快速支出/收入”开始记账</text>
				</view>
				<view class="help-item">
					<text class="item-title" :style="{ color: themeColors.textPrimary }">2. 语音记账</text>
					<text class="item-content" :style="{ color: themeColors.textSecondary }">在记账页面点击🎤按钮，说出“买菜花了30元”等自然语言，系统会自动识别并填写</text>
				</view>
				<view class="help-item">
					<text class="item-title" :style="{ color: themeColors.textPrimary }">3. 拍照记账</text>
					<text class="item-content" :style="{ color: themeColors.textSecondary }">在记账页面点击📷按钮，拍摄发票或小票，系统会自动识别金额和商家信息</text>
				</view>
			</view>
			
			<!-- 快速操作功能 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">⚡</text>
					<text class="section-title">快速操作功能</text>
				</view>
				<view class="help-item">
					<text class="item-title">💰 快速支出</text>
					<text class="item-content">直接跳转到记账页面的支出模式，快速记录日常花费</text>
				</view>
				<view class="help-item">
					<text class="item-title">💰 快速收入</text>
					<text class="item-content">直接跳转到记账页面的收入模式，快速记录收入来源</text>
				</view>
				<view class="help-item">
					<text class="item-title">📊 统计报表</text>
					<text class="item-content">查看详细的收支统计，包含饼图、折线图等可视化数据</text>
				</view>
				<view class="help-item">
					<text class="item-title">🔍 搜索记录</text>
					<text class="item-content">快速搜索历史记录，支持按金额、备注、分类等条件查找</text>
				</view>
				<view class="help-item">
					<text class="item-title">🏦 账户管理</text>
					<text class="item-content">管理多个账户，查看余额，进行账户间转账操作</text>
				</view>
				<view class="help-item">
					<text class="item-title">📈 预算管理</text>
					<text class="item-content">设置和管理各分类的预算，监控支出进度，避免超支</text>
				</view>
				<view class="help-item">
					<text class="item-title">🎯 储蓄目标</text>
					<text class="item-content">设置储蓄目标，跟踪存钱进度，实现理财规划</text>
				</view>
				<view class="help-item">
					<text class="item-title">⏰ 账单提醒</text>
					<text class="item-content">设置周期性提醒，如房租、水电费等固定支出</text>
				</view>
			</view>

			<!-- 进阶功能 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">🚀</text>
					<text class="section-title">进阶功能</text>
				</view>
				<view class="help-item">
					<text class="item-title">👨‍👩‍👧‍👦 家庭账本</text>
					<text class="item-content">创建家庭共享账本，多人协作记账，管理家庭财务</text>
				</view>
				<view class="help-item">
					<text class="item-title">📊 投资追踪</text>
					<text class="item-content">记录股票、基金等投资，追踪收益率和投资组合表现</text>
				</view>
				<view class="help-item">
					<text class="item-title">💳 借债管理</text>
					<text class="item-content">管理借入借出记录，跟踪还款进度，避免遗忘</text>
				</view>
				<view class="help-item">
					<text class="item-title">🧾 报销管理</text>
					<text class="item-content">管理公司报销申请，分类整理报销单据</text>
				</view>
				<view class="help-item">
					<text class="item-title">🔄 周期记账</text>
					<text class="item-content">设置周期性记账模板，自动生成重复性收支记录</text>
				</view>
			</view>

			<!-- 账户管理 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">🏦</text>
					<text class="section-title">账户管理详解</text>
				</view>
				<view class="help-item">
					<text class="item-title">多账户支持</text>
					<text class="item-content">支持现金、银行卡、支付宝、微信等多种账户类型，方便分类管理资金</text>
				</view>
				<view class="help-item">
					<text class="item-title">账户转账</text>
					<text class="item-content">在账户管理页面可以进行账户间转账，自动更新余额</text>
				</view>
				<view class="help-item">
					<text class="item-title">账户详情</text>
					<text class="item-content">点击账户可查看详细的收支明细，支持按时间和类型筛选</text>
				</view>
			</view>
			
			<!-- 分类标签 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">🏷️</text>
					<text class="section-title">分类与标签</text>
				</view>
				<view class="help-item">
					<text class="item-title">智能分类</text>
					<text class="item-content">系统预设了餐饮、交通、购物等常用分类，也支持自定义分类</text>
				</view>
				<view class="help-item">
					<text class="item-title">标签管理</text>
					<text class="item-content">为记录添加标签，如"出差"、"聚餐"等，方便后续查找和统计</text>
				</view>
				<view class="help-item">
					<text class="item-title">报销分组</text>
					<text class="item-content">支持日常消费和公司报销分组，便于区分个人和工作支出</text>
				</view>
			</view>
			
			<!-- 预算目标 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">🎯</text>
					<text class="section-title">预算与目标</text>
				</view>
				<view class="help-item">
					<text class="item-title">预算管理</text>
					<text class="item-content">为不同分类设置月度预算，实时监控支出情况，避免超支</text>
				</view>
				<view class="help-item">
					<text class="item-title">储蓄目标</text>
					<text class="item-content">设置储蓄目标，如"买车基金"、"旅行基金"等，跟踪进度</text>
				</view>
				<view class="help-item">
					<text class="item-title">账单提醒</text>
					<text class="item-content">设置周期性提醒，如房租、水电费等，避免忘记缴费</text>
				</view>
			</view>
			
			<!-- 数据统计 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">📊</text>
					<text class="section-title">数据统计</text>
				</view>
				<view class="help-item">
					<text class="item-title">多维度统计</text>
					<text class="item-content">支持按月、年、全部时间查看收支统计，了解财务状况</text>
				</view>
				<view class="help-item">
					<text class="item-title">可视化图表</text>
					<text class="item-content">饼图显示支出分布，折线图展示月度趋势，数据一目了然</text>
				</view>
				<view class="help-item">
					<text class="item-title">数据导出</text>
					<text class="item-content">支持导出CSV格式数据，可在电脑上进一步分析</text>
				</view>
			</view>
			
			<!-- 数据同步 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">☁️</text>
					<text class="section-title">数据同步</text>
				</view>
				<view class="help-item">
					<text class="item-title">云端备份</text>
					<text class="item-content">数据自动同步到云端，换设备也不怕数据丢失</text>
				</view>
				<view class="help-item">
					<text class="item-title">多设备同步</text>
					<text class="item-content">在多个设备上使用，数据实时同步，随时随地记账</text>
				</view>
				<view class="help-item">
					<text class="item-title">离线使用</text>
					<text class="item-content">支持离线记账，有网络时自动同步到云端</text>
				</view>
			</view>
			
			<!-- 常见问题 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">❓</text>
					<text class="section-title">常见问题</text>
				</view>
				<view class="help-item">
					<text class="item-title">Q: 如何删除错误的记录？</text>
					<text class="item-content">A: 在记录列表中点击记录，进入详情页面，点击删除按钮即可</text>
				</view>
				<view class="help-item">
					<text class="item-title">Q: 语音识别不准确怎么办？</text>
					<text class="item-content">A: 请在安静环境下清晰说话，识别后可以手动修改金额和分类</text>
				</view>
				<view class="help-item">
					<text class="item-title">Q: 如何备份数据？</text>
					<text class="item-content">A: 在设置页面点击"数据同步"，数据会自动备份到云端</text>
				</view>
				<view class="help-item">
					<text class="item-title">Q: 忘记某笔支出的分类怎么办？</text>
					<text class="item-content">A: 可以使用搜索功能，输入关键词快速找到相关记录</text>
				</view>
			</view>
			
			<!-- 联系我们 -->
			<view class="help-section">
				<view class="section-header">
					<text class="section-icon">📞</text>
					<text class="section-title">联系我们</text>
				</view>
				<view class="contact-info">
					<text class="contact-text">如果您在使用过程中遇到问题或有建议，欢迎联系我们：</text>
					<text class="contact-item">📧 邮箱：<EMAIL></text>
					<text class="contact-item">💬 微信：SuiMiAccount</text>
					<text class="contact-item">🌐 官网：www.suimiaccount.com</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import ThemeManager from '@/utils/themeManager.js';

export default {
	data() {
		return {
			themeColors: ThemeManager.getColors()
		};
	},
	onLoad() {
		// 添加主题监听器
		ThemeManager.addListener(this.onThemeChange);
	},
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		// 主题变化回调
		onThemeChange(theme) {
			this.themeColors = theme.colors;
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
	display: flex;
	align-items: center;
}

.back-btn {
	font-size: 36rpx;
	margin-right: 20rpx;
	padding: 10rpx;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 帮助内容 */
.help-content {
	padding: 40rpx;
}

.help-section {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.section-icon {
	font-size: 40rpx;
	margin-right: 16rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.help-item {
	margin-bottom: 24rpx;
}

.help-item:last-child {
	margin-bottom: 0;
}

.item-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.item-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 联系信息 */
.contact-info {
	display: flex;
	flex-direction: column;
}

.contact-text {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 20rpx;
	line-height: 1.6;
}

.contact-item {
	font-size: 26rpx;
	color: #667eea;
	margin-bottom: 12rpx;
	padding: 8rpx 0;
}

.contact-item:last-child {
	margin-bottom: 0;
}
</style>
