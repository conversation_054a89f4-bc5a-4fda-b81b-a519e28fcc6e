<template>
	<view class="container">
		<view class="header">
			<text class="title">日期识别测试</text>
		</view>
		
		<view class="test-section">
			<view class="section-title">输入测试文本</view>
			<textarea 
				v-model="testText" 
				placeholder="输入包含日期的语音文本，如：昨天买菜花了30块钱"
				class="test-input"
			></textarea>
			<button @click="testDateParse" class="test-btn">测试日期解析</button>
		</view>
		
		<view class="result-section" v-if="parseResult">
			<view class="section-title">解析结果</view>
			<view class="result-item">
				<text class="label">原文:</text>
				<text class="value">{{ testText }}</text>
			</view>
			<view class="result-item" v-if="parseResult.date">
				<text class="label">识别日期:</text>
				<text class="value date">{{ parseResult.date }}</text>
			</view>
			<view class="result-item" v-if="parseResult.dateText">
				<text class="label">日期描述:</text>
				<text class="value">{{ parseResult.dateText }}</text>
			</view>
			<view class="result-item" v-if="!parseResult.date">
				<text class="label">日期状态:</text>
				<text class="value no-date">未识别到日期，将使用当前时间</text>
			</view>
			<view class="result-item">
				<text class="label">金额:</text>
				<text class="value amount">{{ parseResult.amount }}元</text>
			</view>
			<view class="result-item">
				<text class="label">类型:</text>
				<text class="value" :class="parseResult.type">
					{{ parseResult.type === 'income' ? '收入' : '支出' }}
				</text>
			</view>
		</view>
		
		<view class="examples-section">
			<view class="section-title">日期表达示例</view>
			<view class="example-category">
				<text class="category-title">绝对日期</text>
				<view class="examples">
					<view 
						v-for="(example, index) in absoluteExamples" 
						:key="'abs-' + index"
						class="example-item"
						@click="useExample(example)"
					>
						<text class="example-text">{{ example }}</text>
					</view>
				</view>
			</view>
			
			<view class="example-category">
				<text class="category-title">相对日期</text>
				<view class="examples">
					<view 
						v-for="(example, index) in relativeExamples" 
						:key="'rel-' + index"
						class="example-item"
						@click="useExample(example)"
					>
						<text class="example-text">{{ example }}</text>
					</view>
				</view>
			</view>
			
			<view class="example-category">
				<text class="category-title">星期表达</text>
				<view class="examples">
					<view 
						v-for="(example, index) in weekExamples" 
						:key="'week-' + index"
						class="example-item"
						@click="useExample(example)"
					>
						<text class="example-text">{{ example }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="debug-section" v-if="debugInfo">
			<view class="section-title">调试信息</view>
			<text class="debug-text">{{ debugInfo }}</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			testText: '',
			parseResult: null,
			debugInfo: '',
			absoluteExamples: [
				'2024年1月15日买书80元',
				'1月20日午餐50元',
				'2024-01-15买菜30元',
				'1/15买衣服100元',
				'12月25日圣诞聚餐200元'
			],
			relativeExamples: [
				'昨天买菜花了30块钱',
				'今天午餐50元',
				'明天电影票35元',
				'前天打车费用25块',
				'后天聚餐200元'
			],
			weekExamples: [
				'上周一买书80元',
				'这周三理发30块',
				'下周五聚餐200元',
				'周六购物150元',
				'星期天电影35元'
			]
		};
	},
	
	methods: {
		testDateParse() {
			if (!this.testText.trim()) {
				uni.showToast({
					title: '请输入测试文本',
					icon: 'none'
				});
				return;
			}
			
			try {
				// 调用云函数的解析逻辑（模拟）
				this.parseResult = this.parseRecordInfo(this.testText);
				this.debugInfo = `解析成功\n当前时间: ${new Date().toLocaleString()}\n识别到日期: ${this.parseResult.date ? '是' : '否'}`;
				
				if (!this.parseResult) {
					uni.showToast({
						title: '解析失败，未识别到有效信息',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('解析错误:', error);
				this.debugInfo = `解析错误: ${error.message}`;
				uni.showToast({
					title: '解析出错',
					icon: 'none'
				});
			}
		},
		
		useExample(example) {
			this.testText = example;
			this.testDateParse();
		},
		
		// 复制云函数的解析逻辑
		parseRecordInfo(text) {
			console.log('解析语音文本:', text);
			
			// 日期识别模式
			const datePatterns = [
				// 绝对日期
				/(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日号]?/,
				/(\d{1,2})[月\-\/](\d{1,2})[日号]/,
				
				// 相对日期
				/昨天|昨日/,
				/前天|前日/,
				/今天|今日/,
				/明天|明日/,
				/后天|后日/,
				
				// 星期表达
				/上周[一二三四五六日天]/,
				/这周[一二三四五六日天]/,
				/下周[一二三四五六日天]/,
				/周[一二三四五六日天]/,
				/星期[一二三四五六日天]/,
				
				// 时间段表达
				/上个月/,
				/这个月/,
				/下个月/,
				/上月/,
				/本月/,
				/下月/
			];
			
			let dateInfo = null;
			const today = new Date();
			
			// 检查日期模式
			for (const pattern of datePatterns) {
				const match = text.match(pattern);
				if (match) {
					dateInfo = this.parseDateFromMatch(match, pattern, today);
					if (dateInfo) {
						console.log('识别到日期:', dateInfo);
						break;
					}
				}
			}
			
			// 简化的金额和类型识别
			const amountMatch = text.match(/(\d+(?:\.\d+)?)/);
			const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
			
			const incomeKeywords = ['工资', '收入', '奖金', '红包'];
			const isIncome = incomeKeywords.some(keyword => text.includes(keyword));
			const type = isIncome ? 'income' : 'expense';
			
			if (!amount || amount <= 0) {
				return null;
			}
			
			return {
				type,
				amount,
				date: dateInfo ? dateInfo.date : null,
				dateText: dateInfo ? dateInfo.text : null,
				originalText: dateInfo ? dateInfo.originalText : null
			};
		},
		
		// 日期解析逻辑
		parseDateFromMatch(match, pattern, today) {
			const matchText = match[0];
			
			// 绝对日期：2024年1月15日
			if (pattern.source.includes('\\d{4}')) {
				const year = parseInt(match[1]);
				const month = parseInt(match[2]);
				const day = parseInt(match[3]);
				return {
					date: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
					text: `${year}年${month}月${day}日`,
					originalText: matchText
				};
			}
			
			// 月日：1月15日
			if (pattern.source.includes('\\d{1,2}.*\\d{1,2}')) {
				const month = parseInt(match[1]);
				const day = parseInt(match[2]);
				const year = today.getFullYear();
				return {
					date: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
					text: `${month}月${day}日`,
					originalText: matchText
				};
			}
			
			// 相对日期
			const relativeMap = {
				'昨天': -1, '昨日': -1,
				'前天': -2, '前日': -2,
				'今天': 0, '今日': 0,
				'明天': 1, '明日': 1,
				'后天': 2, '后日': 2
			};
			
			for (const [key, offset] of Object.entries(relativeMap)) {
				if (matchText.includes(key)) {
					const targetDate = new Date(today);
					targetDate.setDate(today.getDate() + offset);
					return {
						date: targetDate.toISOString().split('T')[0],
						text: key,
						originalText: matchText
					};
				}
			}
			
			// 星期表达
			const weekMap = {
				'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '日': 0, '天': 0
			};
			
			for (const [weekText, weekDay] of Object.entries(weekMap)) {
				if (matchText.includes(weekText)) {
					const currentWeekDay = today.getDay();
					let offset = weekDay - currentWeekDay;
					
					if (matchText.includes('上周')) {
						offset -= 7;
					} else if (matchText.includes('下周')) {
						offset += 7;
					}
					
					const targetDate = new Date(today);
					targetDate.setDate(today.getDate() + offset);
					return {
						date: targetDate.toISOString().split('T')[0],
						text: matchText,
						originalText: matchText
					};
				}
			}
			
			return null;
		}
	}
};
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.test-section, .result-section, .examples-section, .debug-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.test-input {
	width: 100%;
	height: 120rpx;
	border: 2rpx solid #ddd;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	box-sizing: border-box;
}

.test-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	width: 100%;
}

.result-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 15rpx;
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 10rpx;
}

.label {
	font-size: 28rpx;
	color: #666;
	width: 120rpx;
	flex-shrink: 0;
}

.value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	word-break: break-all;
}

.value.date {
	color: #007bff;
	font-weight: bold;
}

.value.no-date {
	color: #ffc107;
	font-style: italic;
}

.value.amount {
	color: #28a745;
	font-weight: bold;
}

.value.income {
	color: #28a745;
	font-weight: bold;
}

.value.expense {
	color: #dc3545;
	font-weight: bold;
}

.example-category {
	margin-bottom: 30rpx;
}

.category-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #495057;
	margin-bottom: 15rpx;
	display: block;
}

.examples {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.example-item {
	background: #e3f2fd;
	border: 2rpx solid #2196f3;
	border-radius: 30rpx;
	padding: 15rpx 25rpx;
	cursor: pointer;
}

.example-item:active {
	background: #bbdefb;
}

.example-text {
	font-size: 26rpx;
	color: #1976d2;
}

.debug-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	white-space: pre-line;
}
</style>
