/**
 * 主题管理器
 * 负责应用主题的切换、存储和应用
 */

// 主题配置
const THEMES = {
  light: {
    name: '清新蓝',
    key: 'light',
    colors: {
      // 主色调
      primary: '#667eea',
      primaryLight: '#764ba2',
      primaryDark: '#5a67d8',
      
      // 背景色
      background: '#f8fafc',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      // 文字颜色
      textPrimary: '#2d3748',
      textSecondary: '#718096',
      textMuted: '#a0aec0',
      textReverse: '#ffffff',
      
      // 边框颜色
      border: '#e2e8f0',
      borderLight: '#f7fafc',
      
      // 状态颜色
      success: '#48bb78',
      warning: '#ed8936',
      error: '#f56565',
      info: '#4299e1',
      
      // 收支颜色
      income: '#48bb78',
      expense: '#f56565',
      
      // 阴影
      shadow: 'rgba(0, 0, 0, 0.1)',
      shadowCard: 'rgba(0, 0, 0, 0.05)',
      
      // 渐变
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
    }
  },
  
  warm: {
    name: '温暖橙',
    key: 'warm',
    colors: {
      primary: '#ff6b35',
      primaryLight: '#ff8c42',
      primaryDark: '#e55a2b',
      
      background: '#fff8f5',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#2d3748',
      textSecondary: '#718096',
      textMuted: '#a0aec0',
      textReverse: '#ffffff',
      
      border: '#fed7cc',
      borderLight: '#fef5f0',
      
      success: '#48bb78',
      warning: '#ed8936',
      error: '#f56565',
      info: '#4299e1',
      
      income: '#48bb78',
      expense: '#f56565',
      
      shadow: 'rgba(255, 107, 53, 0.1)',
      shadowCard: 'rgba(255, 107, 53, 0.05)',
      
      gradient: 'linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 140, 66, 0.1) 100%)'
    }
  },
  
  purple: {
    name: '优雅紫',
    key: 'purple',
    colors: {
      primary: '#9f7aea',
      primaryLight: '#b794f6',
      primaryDark: '#805ad5',
      
      background: '#faf5ff',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#2d3748',
      textSecondary: '#718096',
      textMuted: '#a0aec0',
      textReverse: '#ffffff',
      
      border: '#e9d8fd',
      borderLight: '#faf5ff',
      
      success: '#48bb78',
      warning: '#ed8936',
      error: '#f56565',
      info: '#4299e1',
      
      income: '#48bb78',
      expense: '#f56565',
      
      shadow: 'rgba(159, 122, 234, 0.1)',
      shadowCard: 'rgba(159, 122, 234, 0.05)',
      
      gradient: 'linear-gradient(135deg, #9f7aea 0%, #b794f6 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(159, 122, 234, 0.1) 0%, rgba(183, 148, 246, 0.1) 100%)'
    }
  },
  
  dark: {
    name: '深色模式',
    key: 'dark',
    colors: {
      primary: '#667eea',
      primaryLight: '#764ba2',
      primaryDark: '#5a67d8',
      
      background: '#1a202c',
      backgroundSecondary: '#2d3748',
      backgroundCard: '#2d3748',
      
      textPrimary: '#f7fafc',
      textSecondary: '#e2e8f0',
      textMuted: '#a0aec0',
      textReverse: '#1a202c',
      
      border: '#4a5568',
      borderLight: '#2d3748',
      
      success: '#48bb78',
      warning: '#ed8936',
      error: '#f56565',
      info: '#4299e1',
      
      income: '#48bb78',
      expense: '#f56565',
      
      shadow: 'rgba(0, 0, 0, 0.3)',
      shadowCard: 'rgba(0, 0, 0, 0.2)',
      
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'
    }
  },
  
  green: {
    name: '自然绿',
    key: 'green',
    colors: {
      primary: '#48bb78',
      primaryLight: '#68d391',
      primaryDark: '#38a169',
      
      background: '#f0fff4',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#2d3748',
      textSecondary: '#718096',
      textMuted: '#a0aec0',
      textReverse: '#ffffff',
      
      border: '#c6f6d5',
      borderLight: '#f0fff4',
      
      success: '#48bb78',
      warning: '#ed8936',
      error: '#f56565',
      info: '#4299e1',
      
      income: '#48bb78',
      expense: '#f56565',
      
      shadow: 'rgba(72, 187, 120, 0.1)',
      shadowCard: 'rgba(72, 187, 120, 0.05)',
      
      gradient: 'linear-gradient(135deg, #48bb78 0%, #68d391 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(104, 211, 145, 0.1) 100%)'
    }
  },
  
  pink: {
    name: '浪漫粉',
    key: 'pink',
    colors: {
      primary: '#ed64a6',
      primaryLight: '#f687b3',
      primaryDark: '#d53f8c',
      
      background: '#fffaf0',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#2d3748',
      textSecondary: '#718096',
      textMuted: '#a0aec0',
      textReverse: '#ffffff',
      
      border: '#fbb6ce',
      borderLight: '#fffaf0',
      
      success: '#48bb78',
      warning: '#ed8936',
      error: '#f56565',
      info: '#4299e1',
      
      income: '#48bb78',
      expense: '#f56565',
      
      shadow: 'rgba(237, 100, 166, 0.1)',
      shadowCard: 'rgba(237, 100, 166, 0.05)',
      
      gradient: 'linear-gradient(135deg, #ed64a6 0%, #f687b3 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(237, 100, 166, 0.1) 0%, rgba(246, 135, 179, 0.1) 100%)'
    }
  },
  
  ocean: {
    name: '深海蓝',
    key: 'ocean',
    colors: {
      primary: '#0ea5e9',
      primaryLight: '#38bdf8',
      primaryDark: '#0284c7',
      
      background: '#f0f9ff',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#0f172a',
      textSecondary: '#475569',
      textMuted: '#94a3b8',
      textReverse: '#ffffff',
      
      border: '#bae6fd',
      borderLight: '#f0f9ff',
      
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      
      income: '#10b981',
      expense: '#ef4444',
      
      shadow: 'rgba(14, 165, 233, 0.1)',
      shadowCard: 'rgba(14, 165, 233, 0.05)',
      
      gradient: 'linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(56, 189, 248, 0.1) 100%)'
    }
  },
  
  sunset: {
    name: '夕阳橙',
    key: 'sunset',
    colors: {
      primary: '#fb7185',
      primaryLight: '#fda4af',
      primaryDark: '#e11d48',
      
      background: 'linear-gradient(135deg, #fff1f2 0%, #fef3c7 100%)',
      backgroundSecondary: '#ffffff',
      backgroundCard: 'rgba(255, 255, 255, 0.9)',
      
      textPrimary: '#1f2937',
      textSecondary: '#6b7280',
      textMuted: '#9ca3af',
      textReverse: '#ffffff',
      
      border: '#fecaca',
      borderLight: '#fef2f2',
      
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
      info: '#2563eb',
      
      income: '#059669',
      expense: '#dc2626',
      
      shadow: 'rgba(251, 113, 133, 0.15)',
      shadowCard: 'rgba(251, 113, 133, 0.08)',
      
      gradient: 'linear-gradient(135deg, #fb7185 0%, #fbbf24 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(251, 113, 133, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%)'
    }
  },
  
  forest: {
    name: '森林绿',
    key: 'forest',
    colors: {
      primary: '#16a34a',
      primaryLight: '#22c55e',
      primaryDark: '#15803d',
      
      background: '#f0fdf4',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#14532d',
      textSecondary: '#374151',
      textMuted: '#6b7280',
      textReverse: '#ffffff',
      
      border: '#bbf7d0',
      borderLight: '#f0fdf4',
      
      success: '#16a34a',
      warning: '#ea580c',
      error: '#dc2626',
      info: '#0891b2',
      
      income: '#16a34a',
      expense: '#dc2626',
      
      shadow: 'rgba(22, 163, 74, 0.1)',
      shadowCard: 'rgba(22, 163, 74, 0.05)',
      
      gradient: 'linear-gradient(135deg, #16a34a 0%, #22c55e 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(22, 163, 74, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%)'
    }
  },
  
  midnight: {
    name: '午夜蓝',
    key: 'midnight',
    colors: {
      primary: '#6366f1',
      primaryLight: '#818cf8',
      primaryDark: '#4f46e5',
      
      background: '#0f172a',
      backgroundSecondary: '#1e293b',
      backgroundCard: '#334155',
      
      textPrimary: '#f1f5f9',
      textSecondary: '#cbd5e1',
      textMuted: '#94a3b8',
      textReverse: '#0f172a',
      
      border: '#475569',
      borderLight: '#334155',
      
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4',
      
      income: '#22c55e',
      expense: '#ef4444',
      
      shadow: 'rgba(99, 102, 241, 0.2)',
      shadowCard: 'rgba(99, 102, 241, 0.1)',
      
      gradient: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%)'
    }
  },
  
  gold: {
    name: '奢华金',
    key: 'gold',
    colors: {
      primary: '#f59e0b',
      primaryLight: '#fbbf24',
      primaryDark: '#d97706',
      
      background: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
      backgroundSecondary: '#ffffff',
      backgroundCard: 'rgba(255, 255, 255, 0.95)',
      
      textPrimary: '#92400e',
      textSecondary: '#a16207',
      textMuted: '#ca8a04',
      textReverse: '#ffffff',
      
      border: '#fde68a',
      borderLight: '#fffbeb',
      
      success: '#16a34a',
      warning: '#f59e0b',
      error: '#dc2626',
      info: '#0891b2',
      
      income: '#16a34a',
      expense: '#dc2626',
      
      shadow: 'rgba(245, 158, 11, 0.15)',
      shadowCard: 'rgba(245, 158, 11, 0.08)',
      
      gradient: 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%)'
    }
  },
  
  lavender: {
    name: '薰衣草',
    key: 'lavender',
    colors: {
      primary: '#8b5cf6',
      primaryLight: '#a78bfa',
      primaryDark: '#7c3aed',
      
      background: '#faf5ff',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#581c87',
      textSecondary: '#7c2d12',
      textMuted: '#a855f7',
      textReverse: '#ffffff',
      
      border: '#ddd6fe',
      borderLight: '#faf5ff',
      
      success: '#059669',
      warning: '#ea580c',
      error: '#dc2626',
      info: '#0284c7',
      
      income: '#059669',
      expense: '#dc2626',
      
      shadow: 'rgba(139, 92, 246, 0.12)',
      shadowCard: 'rgba(139, 92, 246, 0.06)',
      
      gradient: 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%)'
    }
  },
  
  coral: {
    name: '珊瑚橙',
    key: 'coral',
    colors: {
      primary: '#ff6b6b',
      primaryLight: '#ff8e8e',
      primaryDark: '#e55555',
      
      background: '#fff5f5',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#2d3748',
      textSecondary: '#4a5568',
      textMuted: '#718096',
      textReverse: '#ffffff',
      
      border: '#fed7d7',
      borderLight: '#fff5f5',
      
      success: '#38a169',
      warning: '#dd6b20',
      error: '#e53e3e',
      info: '#3182ce',
      
      income: '#38a169',
      expense: '#e53e3e',
      
      shadow: 'rgba(255, 107, 107, 0.12)',
      shadowCard: 'rgba(255, 107, 107, 0.06)',
      
      gradient: 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(254, 202, 87, 0.1) 100%)'
    }
  },
  
  mint: {
    name: '薄荷绿',
    key: 'mint',
    colors: {
      primary: '#20b2aa',
      primaryLight: '#40e0d0',
      primaryDark: '#008b8b',
      
      background: '#f0fdfa',
      backgroundSecondary: '#ffffff',
      backgroundCard: '#ffffff',
      
      textPrimary: '#134e4a',
      textSecondary: '#0f766e',
      textMuted: '#5eead4',
      textReverse: '#ffffff',
      
      border: '#a7f3d0',
      borderLight: '#f0fdfa',
      
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4',
      
      income: '#10b981',
      expense: '#ef4444',
      
      shadow: 'rgba(32, 178, 170, 0.12)',
      shadowCard: 'rgba(32, 178, 170, 0.06)',
      
      gradient: 'linear-gradient(135deg, #20b2aa 0%, #40e0d0 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(32, 178, 170, 0.1) 0%, rgba(64, 224, 208, 0.1) 100%)'
    }
  },
  
  royal: {
    name: '皇室紫',
    key: 'royal',
    colors: {
      primary: '#6d28d9',
      primaryLight: '#8b5cf6',
      primaryDark: '#5b21b6',
      
      background: 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)',
      backgroundSecondary: '#ffffff',
      backgroundCard: 'rgba(255, 255, 255, 0.95)',
      
      textPrimary: '#581c87',
      textSecondary: '#6b21a8',
      textMuted: '#a855f7',
      textReverse: '#ffffff',
      
      border: '#c4b5fd',
      borderLight: '#faf5ff',
      
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
      info: '#0284c7',
      
      income: '#059669',
      expense: '#dc2626',
      
      shadow: 'rgba(109, 40, 217, 0.15)',
      shadowCard: 'rgba(109, 40, 217, 0.08)',
      
      gradient: 'linear-gradient(135deg, #6d28d9 0%, #a855f7 100%)',
      gradientLight: 'linear-gradient(135deg, rgba(109, 40, 217, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%)'
    }
  }
};

class ThemeManager {
  constructor() {
    this.currentTheme = 'light';
    this.listeners = [];
    this.init();
  }
  
  /**
   * 初始化主题管理器
   */
  init() {
    // 从本地存储读取主题设置
    const savedTheme = uni.getStorageSync('app_theme') || 'light';
    this.setTheme(savedTheme, false);
  }
  
  /**
   * 获取所有可用主题
   */
  getThemes() {
    return Object.values(THEMES);
  }
  
  /**
   * 获取主题列表（用于选择器）
   */
  getThemeList() {
    return Object.values(THEMES).map(theme => ({
      key: theme.key,
      name: theme.name,
      colors: theme.colors
    }));
  }
  
  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return THEMES[this.currentTheme];
  }
  
  /**
   * 获取当前主题名称
   */
  getCurrentThemeName() {
    return THEMES[this.currentTheme]?.name || '清新蓝';
  }
  
  /**
   * 设置主题
   * @param {string} themeKey - 主题键名
   * @param {boolean} save - 是否保存到本地存储
   */
  setTheme(themeKey, save = true) {
    if (!THEMES[themeKey]) {
      console.warn(`主题 ${themeKey} 不存在，使用默认主题`);
      themeKey = 'light';
    }
    
    this.currentTheme = themeKey;
    
    if (save) {
      uni.setStorageSync('app_theme', themeKey);
    }
    
    // 应用主题到页面
    this.applyTheme();
    
    // 通知监听器
    this.notifyListeners();
  }
  
  /**
   * 切换到下一个主题
   */
  switchToNext() {
    const themes = Object.keys(THEMES);
    const currentIndex = themes.indexOf(this.currentTheme);
    const nextIndex = (currentIndex + 1) % themes.length;
    const nextTheme = themes[nextIndex];
    
    this.setTheme(nextTheme);
    
    return THEMES[nextTheme];
  }
  
  /**
   * 应用主题到页面
   */
  applyTheme() {
    const theme = this.getCurrentTheme();
    
    // 设置页面背景色
    try {
      // #ifdef APP-PLUS
      plus.navigator.setStatusBarBackground(theme.colors.primary);
      plus.navigator.setStatusBarStyle('light');
      // #endif
      
      // 设置导航栏颜色
      uni.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: theme.colors.primary
      });
    } catch (error) {
      console.warn('设置导航栏颜色失败:', error);
    }
  }
  
  /**
   * 获取主题颜色
   * @param {string} colorKey - 颜色键名
   */
  getColor(colorKey) {
    const theme = this.getCurrentTheme();
    return theme.colors[colorKey] || '#667eea';
  }
  
  /**
   * 获取所有主题颜色
   */
  getColors() {
    const theme = this.getCurrentTheme();
    return theme.colors;
  }
  
  /**
   * 添加主题变化监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    if (typeof listener === 'function') {
      this.listeners.push(listener);
    }
  }
  
  /**
   * 移除主题变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
  
  /**
   * 通知所有监听器
   */
  notifyListeners() {
    const theme = this.getCurrentTheme();
    this.listeners.forEach(listener => {
      try {
        listener(theme);
      } catch (error) {
        console.error('主题监听器执行错误:', error);
      }
    });
  }
  
  /**
   * 检查是否为深色主题
   */
  isDarkTheme() {
    return this.currentTheme === 'dark';
  }
  
  /**
   * 生成主题CSS变量
   */
  generateCSSVariables() {
    const colors = this.getColors();
    let cssVars = '';
    
    Object.keys(colors).forEach(key => {
      cssVars += `--theme-${key}: ${colors[key]};\n`;
    });
    
    return cssVars;
  }
}

// 创建全局实例
const themeManager = new ThemeManager();

export default themeManager;
