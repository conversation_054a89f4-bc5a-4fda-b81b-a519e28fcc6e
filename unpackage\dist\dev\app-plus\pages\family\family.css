
.family-container[data-v-775accd8] {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header[data-v-775accd8] {
  padding: 44px 20px 20px;
  background: rgba(255, 255, 255, 0.1);
}
.header-content[data-v-775accd8] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title[data-v-775accd8] {
  font-size: 28px;
  font-weight: bold;
  color: white;
}
.add-btn[data-v-775accd8] {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-icon[data-v-775accd8] {
  font-size: 24px;
  color: white;
  font-weight: bold;
}
.current-book[data-v-775accd8] {
  margin: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.book-header[data-v-775accd8] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}
.book-info[data-v-775accd8] {
  display: flex;
  align-items: center;
  flex: 1;
}
.book-icon[data-v-775accd8] {
  font-size: 40px;
  margin-right: 16px;
}
.book-details[data-v-775accd8] {
  display: flex;
  flex-direction: column;
}
.book-name[data-v-775accd8] {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}
.book-desc[data-v-775accd8] {
  font-size: 14px;
  color: #666;
}
.book-actions[data-v-775accd8] {
  display: flex;
  gap: 12px;
}
.action-btn[data-v-775accd8] {
  padding: 8px 16px;
  background: #f0f0f0;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
}
.members-section[data-v-775accd8] {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}
.section-title[data-v-775accd8] {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}
.members-scroll[data-v-775accd8] {
  white-space: nowrap;
}
.member-item[data-v-775accd8] {
  display: inline-block;
  text-align: center;
  margin-right: 16px;
  vertical-align: top;
}
.member-avatar[data-v-775accd8] {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.avatar-text[data-v-775accd8] {
  color: white;
  font-size: 18px;
  font-weight: bold;
}
.member-name[data-v-775accd8] {
  display: block;
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
}
.member-role[data-v-775accd8] {
  display: block;
  font-size: 10px;
  color: #999;
}
.books-section[data-v-775accd8] {
  margin: 20px;
}
.book-list[data-v-775accd8] {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.book-card[data-v-775accd8] {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.book-card.active[data-v-775accd8] {
  border: 2px solid #667eea;
}
.card-header[data-v-775accd8] {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.card-icon[data-v-775accd8] {
  font-size: 24px;
  margin-right: 12px;
}
.card-info[data-v-775accd8] {
  flex: 1;
}
.card-name[data-v-775accd8] {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}
.card-desc[data-v-775accd8] {
  font-size: 12px;
  color: #666;
}
.card-footer[data-v-775accd8] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.member-count[data-v-775accd8] {
  font-size: 12px;
  color: #999;
}
.card-date[data-v-775accd8] {
  font-size: 12px;
  color: #999;
}
.modal[data-v-775accd8] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content[data-v-775accd8] {
  background: white;
  border-radius: 16px;
  width: 80%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}
.member-modal[data-v-775accd8] {
  max-height: 70%;
}
.modal-header[data-v-775accd8] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title[data-v-775accd8] {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.modal-close[data-v-775accd8] {
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-body[data-v-775accd8] {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}
.form-group[data-v-775accd8] {
  margin-bottom: 20px;
}
.form-label[data-v-775accd8] {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.form-input[data-v-775accd8] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}
.form-textarea[data-v-775accd8] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
}
.icon-scroll[data-v-775accd8] {
  white-space: nowrap;
}
.icon-item[data-v-775accd8] {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 2px solid #ddd;
  border-radius: 8px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-item.selected[data-v-775accd8] {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}
.icon-text[data-v-775accd8] {
  font-size: 24px;
}
.role-options[data-v-775accd8] {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.role-option[data-v-775accd8] {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}
.role-option.selected[data-v-775accd8] {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}
.modal-footer[data-v-775accd8] {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}
.btn[data-v-775accd8] {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
}
.btn-cancel[data-v-775accd8] {
  background: #f0f0f0;
  color: #666;
}
.btn-primary[data-v-775accd8] {
  background: #667eea;
  color: white;
}
.member-list[data-v-775accd8] {
  margin-bottom: 20px;
}
.member-row[data-v-775accd8] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.member-info[data-v-775accd8] {
  display: flex;
  align-items: center;
  flex: 1;
}
.member-details[data-v-775accd8] {
  margin-left: 12px;
}
.member-actions[data-v-775accd8] {
  display: flex;
  gap: 12px;
}
.action-text[data-v-775accd8] {
  font-size: 14px;
  color: #667eea;
}
.action-text.danger[data-v-775accd8] {
  color: #ff4757;
}
.add-member-section[data-v-775accd8] {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}
