
.page[data-v-c10c040c] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-c10c040c] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-c10c040c] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-c10c040c] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-c10c040c] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 搜索框 */
.search-section[data-v-c10c040c] {
	padding: 0.625rem 1.25rem;
}
.search-box[data-v-c10c040c] {
	background: white;
	border-radius: 1.5625rem;
	padding: 0.625rem 0.9375rem;
	display: flex;
	align-items: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.search-icon[data-v-c10c040c] {
	font-size: 1rem;
	margin-right: 0.625rem;
	color: #999;
}
.search-input[data-v-c10c040c] {
	flex: 1;
	font-size: 0.875rem;
	color: #333;
	border: none;
	outline: none;
}
.clear-btn[data-v-c10c040c] {
	font-size: 1.25rem;
	color: #ccc;
	margin-left: 0.625rem;
	font-weight: bold;
}

/* 筛选条件 */
.filter-section[data-v-c10c040c] {
	padding: 0 1.25rem 0.625rem;
}
.filter-tabs[data-v-c10c040c] {
	background: white;
	border-radius: 0.625rem;
	display: flex;
	padding: 0.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.filter-tab[data-v-c10c040c] {
	flex: 1;
	text-align: center;
	padding: 0.625rem;
	border-radius: 0.5rem;
	transition: all 0.3s;
}
.filter-tab.active[data-v-c10c040c] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.tab-text[data-v-c10c040c] {
	font-size: 0.875rem;
	font-weight: 500;
}

/* 搜索结果 */
.results-section[data-v-c10c040c] {
	flex: 1;
	padding: 0 1.25rem;
}
.empty-state[data-v-c10c040c] {
	text-align: center;
	padding: 3.75rem 1.25rem;
}
.empty-icon[data-v-c10c040c] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-c10c040c] {
	font-size: 0.875rem;
	color: #999;
}
.results-header[data-v-c10c040c] {
	padding: 0.625rem 0;
}
.results-count[data-v-c10c040c] {
	font-size: 0.8125rem;
	color: #666;
}

/* 记录列表 */
.records-list[data-v-c10c040c] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
}
.record-item[data-v-c10c040c] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.record-item[data-v-c10c040c]:last-child {
	border-bottom: none;
}
.record-item[data-v-c10c040c]:active {
	background-color: #f8f9fa;
}
.record-left[data-v-c10c040c] {
	flex: 1;
}
.record-category[data-v-c10c040c] {
	display: flex;
	align-items: center;
	margin-bottom: 0.25rem;
}
.category-icon[data-v-c10c040c] {
	font-size: 1rem;
	margin-right: 0.5rem;
}
.category-name[data-v-c10c040c] {
	font-size: 1rem;
	font-weight: 500;
	color: #333;
}
.record-note[data-v-c10c040c] {
	font-size: 0.8125rem;
	color: #666;
	margin-bottom: 0.25rem;
	display: block;
}
.record-time[data-v-c10c040c] {
	font-size: 0.75rem;
	color: #999;
}
.record-right[data-v-c10c040c] {
	text-align: right;
}
.record-amount[data-v-c10c040c] {
	font-size: 1rem;
	font-weight: bold;
}
.record-amount.expense[data-v-c10c040c] {
	color: #ff4757;
}
.record-amount.income[data-v-c10c040c] {
	color: #2ed573;
}
