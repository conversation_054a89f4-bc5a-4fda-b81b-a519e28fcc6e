/**
 * 主题配置文件
 * 定义全局CSS变量和主题样式
 */

export const THEME_VARIABLES = {
  light: {
    '--theme-primary': '#667eea',
    '--theme-primary-light': '#764ba2',
    '--theme-primary-dark': '#5a67d8',
    '--theme-background': '#f8fafc',
    '--theme-background-secondary': '#ffffff',
    '--theme-background-card': '#ffffff',
    '--theme-text-primary': '#2d3748',
    '--theme-text-secondary': '#718096',
    '--theme-text-muted': '#a0aec0',
    '--theme-text-reverse': '#ffffff',
    '--theme-border': '#e2e8f0',
    '--theme-border-light': '#f7fafc',
    '--theme-success': '#48bb78',
    '--theme-warning': '#ed8936',
    '--theme-error': '#f56565',
    '--theme-info': '#4299e1',
    '--theme-income': '#48bb78',
    '--theme-expense': '#f56565',
    '--theme-shadow': 'rgba(0, 0, 0, 0.1)',
    '--theme-shadow-card': 'rgba(0, 0, 0, 0.05)',
    '--theme-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    '--theme-gradient-light': 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
  },
  
  warm: {
    '--theme-primary': '#ff6b35',
    '--theme-primary-light': '#ff8c42',
    '--theme-primary-dark': '#e55a2b',
    '--theme-background': '#fff8f5',
    '--theme-background-secondary': '#ffffff',
    '--theme-background-card': '#ffffff',
    '--theme-text-primary': '#2d3748',
    '--theme-text-secondary': '#718096',
    '--theme-text-muted': '#a0aec0',
    '--theme-text-reverse': '#ffffff',
    '--theme-border': '#fed7cc',
    '--theme-border-light': '#fef5f0',
    '--theme-success': '#48bb78',
    '--theme-warning': '#ed8936',
    '--theme-error': '#f56565',
    '--theme-info': '#4299e1',
    '--theme-income': '#48bb78',
    '--theme-expense': '#f56565',
    '--theme-shadow': 'rgba(255, 107, 53, 0.1)',
    '--theme-shadow-card': 'rgba(255, 107, 53, 0.05)',
    '--theme-gradient': 'linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%)',
    '--theme-gradient-light': 'linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 140, 66, 0.1) 100%)'
  },
  
  purple: {
    '--theme-primary': '#9f7aea',
    '--theme-primary-light': '#b794f6',
    '--theme-primary-dark': '#805ad5',
    '--theme-background': '#faf5ff',
    '--theme-background-secondary': '#ffffff',
    '--theme-background-card': '#ffffff',
    '--theme-text-primary': '#2d3748',
    '--theme-text-secondary': '#718096',
    '--theme-text-muted': '#a0aec0',
    '--theme-text-reverse': '#ffffff',
    '--theme-border': '#e9d8fd',
    '--theme-border-light': '#faf5ff',
    '--theme-success': '#48bb78',
    '--theme-warning': '#ed8936',
    '--theme-error': '#f56565',
    '--theme-info': '#4299e1',
    '--theme-income': '#48bb78',
    '--theme-expense': '#f56565',
    '--theme-shadow': 'rgba(159, 122, 234, 0.1)',
    '--theme-shadow-card': 'rgba(159, 122, 234, 0.05)',
    '--theme-gradient': 'linear-gradient(135deg, #9f7aea 0%, #b794f6 100%)',
    '--theme-gradient-light': 'linear-gradient(135deg, rgba(159, 122, 234, 0.1) 0%, rgba(183, 148, 246, 0.1) 100%)'
  },
  
  dark: {
    '--theme-primary': '#667eea',
    '--theme-primary-light': '#764ba2',
    '--theme-primary-dark': '#5a67d8',
    '--theme-background': '#1a202c',
    '--theme-background-secondary': '#2d3748',
    '--theme-background-card': '#2d3748',
    '--theme-text-primary': '#f7fafc',
    '--theme-text-secondary': '#e2e8f0',
    '--theme-text-muted': '#a0aec0',
    '--theme-text-reverse': '#1a202c',
    '--theme-border': '#4a5568',
    '--theme-border-light': '#2d3748',
    '--theme-success': '#48bb78',
    '--theme-warning': '#ed8936',
    '--theme-error': '#f56565',
    '--theme-info': '#4299e1',
    '--theme-income': '#48bb78',
    '--theme-expense': '#f56565',
    '--theme-shadow': 'rgba(0, 0, 0, 0.3)',
    '--theme-shadow-card': 'rgba(0, 0, 0, 0.2)',
    '--theme-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    '--theme-gradient-light': 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'
  },
  
  green: {
    '--theme-primary': '#48bb78',
    '--theme-primary-light': '#68d391',
    '--theme-primary-dark': '#38a169',
    '--theme-background': '#f0fff4',
    '--theme-background-secondary': '#ffffff',
    '--theme-background-card': '#ffffff',
    '--theme-text-primary': '#2d3748',
    '--theme-text-secondary': '#718096',
    '--theme-text-muted': '#a0aec0',
    '--theme-text-reverse': '#ffffff',
    '--theme-border': '#c6f6d5',
    '--theme-border-light': '#f0fff4',
    '--theme-success': '#48bb78',
    '--theme-warning': '#ed8936',
    '--theme-error': '#f56565',
    '--theme-info': '#4299e1',
    '--theme-income': '#48bb78',
    '--theme-expense': '#f56565',
    '--theme-shadow': 'rgba(72, 187, 120, 0.1)',
    '--theme-shadow-card': 'rgba(72, 187, 120, 0.05)',
    '--theme-gradient': 'linear-gradient(135deg, #48bb78 0%, #68d391 100%)',
    '--theme-gradient-light': 'linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(104, 211, 145, 0.1) 100%)'
  },
  
  pink: {
    '--theme-primary': '#ed64a6',
    '--theme-primary-light': '#f687b3',
    '--theme-primary-dark': '#d53f8c',
    '--theme-background': '#fffaf0',
    '--theme-background-secondary': '#ffffff',
    '--theme-background-card': '#ffffff',
    '--theme-text-primary': '#2d3748',
    '--theme-text-secondary': '#718096',
    '--theme-text-muted': '#a0aec0',
    '--theme-text-reverse': '#ffffff',
    '--theme-border': '#fbb6ce',
    '--theme-border-light': '#fffaf0',
    '--theme-success': '#48bb78',
    '--theme-warning': '#ed8936',
    '--theme-error': '#f56565',
    '--theme-info': '#4299e1',
    '--theme-income': '#48bb78',
    '--theme-expense': '#f56565',
    '--theme-shadow': 'rgba(237, 100, 166, 0.1)',
    '--theme-shadow-card': 'rgba(237, 100, 166, 0.05)',
    '--theme-gradient': 'linear-gradient(135deg, #ed64a6 0%, #f687b3 100%)',
    '--theme-gradient-light': 'linear-gradient(135deg, rgba(237, 100, 166, 0.1) 0%, rgba(246, 135, 179, 0.1) 100%)'
  }
};

/**
 * 应用主题CSS变量到页面
 */
export function applyThemeVariables(themeKey) {
  const variables = THEME_VARIABLES[themeKey];
  if (!variables) return;
  
  const root = document.documentElement;
  Object.keys(variables).forEach(key => {
    root.style.setProperty(key, variables[key]);
  });
}

/**
 * 获取主题CSS变量字符串
 */
export function getThemeVariablesString(themeKey) {
  const variables = THEME_VARIABLES[themeKey];
  if (!variables) return '';
  
  return Object.keys(variables)
    .map(key => `${key}: ${variables[key]};`)
    .join('\n');
}
