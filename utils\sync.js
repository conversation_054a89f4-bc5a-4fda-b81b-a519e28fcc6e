import StorageManager from './storageManager.js';

export default class SyncManager {
  static async syncData() {
    const records = StorageManager.getRecords();
    const res = await uniCloud.callFunction({
      name: 'syncRecords',
      data: { localRecords: records }
    });
    if (res.result.code === 0) {
      // 下载的记录合并到本地
      if (res.result.downloaded && res.result.downloaded.length) {
        const merged = this.mergeRecords(records, res.result.downloaded);
        StorageManager.saveRecords(merged);
      }
      StorageManager.setLastSyncTime(Date.now());
    }
    return res.result;
  }

  static mergeRecords(localList, downloaded) {
    const map = {};
    localList.forEach(r => (map[r._id] = r));
    downloaded.forEach(r => (map[r._id] = r));
    return Object.values(map);
  }

  static syncAfterRecord() {
    setTimeout(() => {
      this.syncData();
    }, 600);
  }
}
