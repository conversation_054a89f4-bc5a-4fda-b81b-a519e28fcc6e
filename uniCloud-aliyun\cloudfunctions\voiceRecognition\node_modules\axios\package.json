{"name": "axios", "version": "0.27.2", "description": "Promise based HTTP client for the browser and node.js", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "grunt test && dtslint", "start": "node ./sandbox/server.js", "preversion": "grunt version && npm test", "build": "NODE_ENV=production grunt build", "examples": "node ./examples/server.js", "coveralls": "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "fix": "eslint --fix lib/**/*.js"}, "repository": {"type": "git", "url": "https://github.com/axios/axios.git"}, "keywords": ["xhr", "http", "ajax", "promise", "node"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/axios/axios/issues"}, "homepage": "https://axios-http.com", "devDependencies": {"abortcontroller-polyfill": "^1.7.3", "coveralls": "^3.1.1", "dtslint": "^4.2.1", "es6-promise": "^4.2.8", "formidable": "^2.0.1", "grunt": "^1.4.1", "grunt-banner": "^0.6.0", "grunt-cli": "^1.4.3", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-eslint": "^24.0.0", "grunt-karma": "^4.0.2", "grunt-mocha-test": "^0.13.3", "grunt-webpack": "^5.0.0", "istanbul-instrumenter-loader": "^3.0.1", "jasmine-core": "^2.4.1", "karma": "^6.3.17", "karma-chrome-launcher": "^3.1.1", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^1.1.1", "karma-jasmine-ajax": "^0.1.13", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.8", "karma-webpack": "^4.0.2", "load-grunt-tasks": "^5.1.0", "minimist": "^1.2.6", "mocha": "^8.2.1", "sinon": "^4.5.0", "terser-webpack-plugin": "^4.2.3", "typescript": "^4.6.3", "url-search-params": "^0.10.0", "webpack": "^4.44.2", "webpack-dev-server": "^3.11.0"}, "browser": {"./lib/adapters/http.js": "./lib/adapters/xhr.js", "./lib/defaults/env/FormData.js": "./lib/helpers/null.js"}, "jsdelivr": "dist/axios.min.js", "unpkg": "dist/axios.min.js", "typings": "./index.d.ts", "dependencies": {"follow-redirects": "^1.14.9", "form-data": "^4.0.0"}, "bundlesize": [{"path": "./dist/axios.min.js", "threshold": "5kB"}]}