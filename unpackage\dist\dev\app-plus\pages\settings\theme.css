
.page[data-v-9667293f] {
	min-height: 100vh;
}
.header[data-v-9667293f] {
	padding: 1.875rem 1.25rem 1.25rem;
	text-align: center;
}
.header-title[data-v-9667293f] {
	color: #ffffff;
	font-size: 1.125rem;
	font-weight: bold;
}
.current-theme[data-v-9667293f] {
	margin: 1.25rem 0.9375rem;
	border-radius: 0.625rem;
	overflow: hidden;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.preview-card[data-v-9667293f] {
	padding: 1.25rem;
	text-align: center;
}
.preview-title[data-v-9667293f] {
	color: rgba(255, 255, 255, 0.8);
	font-size: 0.75rem;
	display: block;
	margin-bottom: 0.3125rem;
}
.preview-name[data-v-9667293f] {
	color: #ffffff;
	font-size: 1rem;
	font-weight: bold;
}
.theme-section[data-v-9667293f] {
	margin: 1.25rem 0.9375rem;
}
.section-title[data-v-9667293f] {
	font-size: 0.875rem;
	font-weight: bold;
	margin-bottom: 0.9375rem;
}
.theme-grid[data-v-9667293f] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.theme-item[data-v-9667293f] {
	border-radius: 0.625rem;
	padding: 0.625rem;
	border: 0.0625rem solid;
	position: relative;
	transition: all 0.3s ease;
}
.theme-item.active[data-v-9667293f] {
	transform: scale(1.02);
	box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.15);
}
.theme-preview[data-v-9667293f] {
	border-radius: 0.375rem;
	overflow: hidden;
	margin-bottom: 0.625rem;
	height: 3.75rem;
}
.preview-header[data-v-9667293f] {
	height: 1.25rem;
}
.preview-body[data-v-9667293f] {
	height: 2.5rem;
	padding: 0.3125rem;
	display: flex;
	gap: 0.25rem;
}
.preview-item[data-v-9667293f] {
	flex: 1;
	border-radius: 0.1875rem;
}
.theme-info[data-v-9667293f] {
	text-align: center;
}
.theme-name[data-v-9667293f] {
	font-size: 0.8125rem;
	font-weight: bold;
	margin-bottom: 0.3125rem;
	display: block;
}
.theme-colors[data-v-9667293f] {
	display: flex;
	justify-content: center;
	gap: 0.25rem;
}
.color-dot[data-v-9667293f] {
	width: 0.5rem;
	height: 0.5rem;
	border-radius: 50%;
}
.selected-icon[data-v-9667293f] {
	position: absolute;
	top: 0.3125rem;
	right: 0.3125rem;
	width: 1.25rem;
	height: 1.25rem;
	background: #48bb78;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.selected-icon .icon[data-v-9667293f] {
	color: #ffffff;
	font-size: 0.625rem;
	font-weight: bold;
}
.settings-section[data-v-9667293f] {
	margin: 1.25rem 0.9375rem;
}
.setting-item[data-v-9667293f] {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0.9375rem;
	border-radius: 0.625rem;
	margin-bottom: 0.625rem;
}
.item-left[data-v-9667293f] {
	display: flex;
	align-items: center;
	flex: 1;
}
.item-icon[data-v-9667293f] {
	font-size: 1rem;
	margin-right: 0.625rem;
}
.item-content[data-v-9667293f] {
	flex: 1;
}
.item-title[data-v-9667293f] {
	font-size: 0.875rem;
	font-weight: bold;
	margin-bottom: 0.25rem;
	display: block;
}
.item-desc[data-v-9667293f] {
	font-size: 0.75rem;
	line-height: 1.4;
}
.presets-section[data-v-9667293f] {
	margin: 1.25rem 0.9375rem 1.875rem;
}
.preset-buttons[data-v-9667293f] {
	display: flex;
	gap: 0.625rem;
}
.preset-btn[data-v-9667293f] {
	flex: 1;
	padding: 0.9375rem 0.625rem;
	border-radius: 0.625rem;
	border: 0.0625rem solid;
	text-align: center;
	transition: all 0.3s ease;
}
.preset-btn[data-v-9667293f]:active {
	transform: scale(0.98);
}
.preset-icon[data-v-9667293f] {
	font-size: 1rem;
	margin-bottom: 0.3125rem;
	display: block;
}
.preset-text[data-v-9667293f] {
	font-size: 0.75rem;
	font-weight: bold;
}
