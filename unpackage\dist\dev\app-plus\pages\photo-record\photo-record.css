
.page[data-v-99dec9aa] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-99dec9aa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-content[data-v-99dec9aa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-99dec9aa] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-99dec9aa] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 拍照区域 */
.photo-section[data-v-99dec9aa] {
	margin: 1.25rem;
}
.photo-placeholder[data-v-99dec9aa] {
	background: white;
	border-radius: 0.625rem;
	padding: 3.125rem 1.25rem;
	text-align: center;
	border: 0.0625rem dashed #ddd;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.photo-icon[data-v-99dec9aa] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.photo-text[data-v-99dec9aa] {
	font-size: 1rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.5rem;
}
.photo-tip[data-v-99dec9aa] {
	font-size: 0.8125rem;
	color: #666;
}
.photo-preview[data-v-99dec9aa] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.preview-image[data-v-99dec9aa] {
	width: 100%;
	max-height: 12.5rem;
	border-radius: 0.375rem;
	margin-bottom: 0.9375rem;
}
.photo-actions[data-v-99dec9aa] {
	display: flex;
	gap: 0.625rem;
}
.retake-btn[data-v-99dec9aa], .analyze-btn[data-v-99dec9aa] {
	flex: 1;
	height: 2.5rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	font-weight: bold;
	border: none;
}
.retake-btn[data-v-99dec9aa] {
	background: #f8f9fa;
	color: #666;
}
.analyze-btn[data-v-99dec9aa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.analyze-btn[data-v-99dec9aa]:disabled {
	background: #ccc;
	color: #999;
}

/* 识别结果 */
.result-section[data-v-99dec9aa] {
	margin: 0 1.25rem 1.25rem;
}
.result-card[data-v-99dec9aa] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.result-header[data-v-99dec9aa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.9375rem;
}
.result-title[data-v-99dec9aa] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.result-confidence[data-v-99dec9aa] {
	font-size: 0.75rem;
	color: #4CAF50;
	background: rgba(76, 175, 80, 0.1);
	padding: 0.25rem 0.5rem;
	border-radius: 0.625rem;
}
.recognized-text[data-v-99dec9aa] {
	background: #f8f9fa;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.9375rem;
}
.text-label[data-v-99dec9aa] {
	font-size: 0.8125rem;
	color: #666;
	display: block;
	margin-bottom: 0.5rem;
}
.text-content[data-v-99dec9aa] {
	font-size: 0.875rem;
	color: #333;
	line-height: 1.6;
	white-space: pre-line;
}
.parsed-records[data-v-99dec9aa] {
	margin-bottom: 0.9375rem;
}
.records-title[data-v-99dec9aa] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.record-item[data-v-99dec9aa] {
	background: #f8f9fa;
	border-radius: 0.375rem;
	padding: 0.75rem;
	margin-bottom: 0.5rem;
	border: 0.0625rem solid transparent;
	transition: all 0.3s;
}
.record-item.selected[data-v-99dec9aa] {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.1);
}
.record-info[data-v-99dec9aa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
}
.record-type[data-v-99dec9aa] {
	font-size: 0.75rem;
	color: #666;
	background: rgba(102, 126, 234, 0.1);
	padding: 0.1875rem 0.375rem;
	border-radius: 0.375rem;
}
.record-amount[data-v-99dec9aa] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
}
.record-desc[data-v-99dec9aa] {
	font-size: 0.8125rem;
	color: #333;
	margin-bottom: 0.25rem;
}
.record-category[data-v-99dec9aa] {
	font-size: 0.75rem;
	color: #666;
}
.result-actions[data-v-99dec9aa] {
	display: flex;
	gap: 0.625rem;
}
.edit-btn[data-v-99dec9aa], .save-btn[data-v-99dec9aa] {
	flex: 1;
	height: 2.5rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	font-weight: bold;
	border: none;
}
.edit-btn[data-v-99dec9aa] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-99dec9aa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.save-btn[data-v-99dec9aa]:disabled {
	background: #ccc;
	color: #999;
}

/* 使用说明 */
.tips-section[data-v-99dec9aa] {
	margin: 0 1.25rem;
}
.tips-card[data-v-99dec9aa] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.tips-header[data-v-99dec9aa] {
	margin-bottom: 0.9375rem;
}
.tips-title[data-v-99dec9aa] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.tips-content[data-v-99dec9aa] {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}
.tip-item[data-v-99dec9aa] {
	font-size: 0.8125rem;
	color: #666;
	line-height: 1.5;
}

/* 弹窗样式 */
.popup-overlay[data-v-99dec9aa] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.popup-content[data-v-99dec9aa] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	width: 100%;
	max-height: 90vh;
	display: flex;
	flex-direction: column;
}
.popup-header[data-v-99dec9aa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-99dec9aa] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-99dec9aa] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-scroll[data-v-99dec9aa] {
	flex: 1;
	max-height: 60vh;
}
.form-section[data-v-99dec9aa] {
	padding-bottom: 1.25rem;
}
.edit-record[data-v-99dec9aa] {
	background: #f8f9fa;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.9375rem;
}
.record-header[data-v-99dec9aa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.9375rem;
}
.record-index[data-v-99dec9aa] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
}
.delete-record[data-v-99dec9aa] {
	font-size: 0.8125rem;
	color: #ff4757;
}
.form-item[data-v-99dec9aa] {
	margin-bottom: 0.9375rem;
}
.form-label[data-v-99dec9aa] {
	font-size: 0.8125rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.5rem;
}
.form-input[data-v-99dec9aa] {
	width: 100%;
	padding: 0.625rem;
	background: white;
	border-radius: 0.25rem;
	border: 0.03125rem solid #ddd;
	font-size: 0.875rem;
	color: #333;
}
.type-selector[data-v-99dec9aa] {
	display: flex;
	gap: 0.5rem;
}
.type-option[data-v-99dec9aa] {
	flex: 1;
	padding: 0.625rem;
	background: white;
	border-radius: 0.25rem;
	text-align: center;
	border: 0.03125rem solid #ddd;
	transition: all 0.3s;
}
.type-option.active[data-v-99dec9aa] {
	background: #667eea;
	color: white;
	border-color: #667eea;
}
.add-record-btn[data-v-99dec9aa] {
	width: 100%;
	height: 2.5rem;
	background: #f8f9fa;
	color: #666;
	border: 0.0625rem dashed #ddd;
	border-radius: 0.375rem;
	font-size: 0.875rem;
}
.popup-actions[data-v-99dec9aa] {
	display: flex;
	gap: 0.625rem;
	margin-top: 0.625rem;
}
.cancel-btn[data-v-99dec9aa], .confirm-btn[data-v-99dec9aa] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.cancel-btn[data-v-99dec9aa] {
	background: #f8f9fa;
	color: #666;
}
.confirm-btn[data-v-99dec9aa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
