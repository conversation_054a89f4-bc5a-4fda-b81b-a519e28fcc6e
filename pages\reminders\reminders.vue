<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">账单提醒</text>
				<text class="header-action" @click="addReminder">+</text>
			</view>
		</view>
		
		<!-- 即将到期 -->
		<view v-if="upcomingReminders.length > 0" class="upcoming-section">
			<view class="section-header">
				<text class="section-title">⏰ 即将到期</text>
			</view>
			<view class="reminders-list">
				<view 
					v-for="reminder in upcomingReminders" 
					:key="reminder.id"
					class="reminder-item urgent"
					@click="viewReminder(reminder)"
				>
					<view class="reminder-left">
						<view class="reminder-icon" :style="{backgroundColor: reminder.color}">
							<text class="icon-text">{{reminder.icon}}</text>
						</view>
						<view class="reminder-info">
							<text class="reminder-name">{{reminder.name}}</text>
							<text class="reminder-amount" v-if="reminder.amount">
								¥{{reminder.amount.toFixed(2)}}
							</text>
							<text class="reminder-date">{{formatDate(reminder.nextDate)}}</text>
						</view>
					</view>
					<view class="reminder-right">
						<text class="days-left">{{getDaysLeft(reminder.nextDate)}}天</text>
						<text class="complete-btn" @click.stop="completeReminder(reminder)">完成</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 所有提醒 -->
		<view class="all-reminders-section">
			<view class="section-header">
				<text class="section-title">📋 所有提醒</text>
				<view class="filter-tabs">
					<text 
						v-for="tab in filterTabs" 
						:key="tab.value"
						class="filter-tab"
						:class="{active: currentFilter === tab.value}"
						@click="changeFilter(tab.value)"
					>
						{{tab.label}}
					</text>
				</view>
			</view>
			
			<view v-if="filteredReminders.length === 0" class="empty-state">
				<text class="empty-icon">📅</text>
				<text class="empty-text">暂无账单提醒</text>
				<button class="add-reminder-btn" @click="addReminder">添加第一个提醒</button>
			</view>
			
			<view v-else class="reminders-list">
				<view 
					v-for="reminder in filteredReminders" 
					:key="reminder.id"
					class="reminder-item"
					:class="{disabled: !reminder.enabled}"
					@click="viewReminder(reminder)"
				>
					<view class="reminder-left">
						<view class="reminder-icon" :style="{backgroundColor: reminder.color}">
							<text class="icon-text">{{reminder.icon}}</text>
						</view>
						<view class="reminder-info">
							<text class="reminder-name">{{reminder.name}}</text>
							<text class="reminder-amount" v-if="reminder.amount">
								¥{{reminder.amount.toFixed(2)}}
							</text>
							<text class="reminder-cycle">{{getCycleText(reminder.cycle)}}</text>
							<text class="reminder-next">下次：{{formatDate(reminder.nextDate)}}</text>
						</view>
					</view>
					<view class="reminder-right">
						<switch 
							:checked="reminder.enabled" 
							@change="toggleReminder(reminder)"
							color="#667eea"
						/>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加提醒弹窗 -->
		<view v-if="showReminderPopup" class="popup-overlay" @click="closeReminderPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingReminder ? '编辑提醒' : '新建账单提醒'}}</text>
					<text class="popup-close" @click="closeReminderPopup">×</text>
				</view>
				
				<scroll-view class="form-scroll" scroll-y>
					<view class="form-section">
						<view class="form-item">
							<text class="form-label">提醒名称</text>
							<input 
								class="form-input" 
								v-model="reminderForm.name" 
								placeholder="如：信用卡还款、房租、水电费"
								maxlength="20"
							/>
						</view>
						
						<view class="form-item">
							<text class="form-label">金额（可选）</text>
							<input 
								class="form-input" 
								v-model="reminderForm.amount" 
								type="digit"
								placeholder="0.00"
							/>
						</view>
						
						<view class="form-item">
							<text class="form-label">提醒类型</text>
							<view class="type-selector">
								<view 
									v-for="type in reminderTypes" 
									:key="type.value"
									class="type-item"
									:class="{active: reminderForm.type === type.value}"
									@click="selectType(type.value)"
								>
									<text class="type-icon">{{type.icon}}</text>
									<text class="type-name">{{type.name}}</text>
								</view>
							</view>
						</view>
						
						<view class="form-item">
							<text class="form-label">重复周期</text>
							<picker 
								:range="cycleOptions" 
								range-key="name"
								:value="selectedCycleIndex" 
								@change="onCycleChange"
								class="cycle-picker"
							>
								<view class="picker-text">
									{{getSelectedCycleName()}}
								</view>
							</picker>
						</view>
						
						<view class="form-item">
							<text class="form-label">首次提醒日期</text>
							<picker 
								mode="date" 
								:value="reminderForm.firstDate" 
								@change="onFirstDateChange"
								class="date-picker"
							>
								<view class="picker-text">
									{{reminderForm.firstDate || '请选择日期'}}
								</view>
							</picker>
						</view>
						
						<view class="form-item">
							<text class="form-label">提前提醒</text>
							<picker 
								:range="advanceDays" 
								:value="selectedAdvanceIndex" 
								@change="onAdvanceChange"
								class="advance-picker"
							>
								<view class="picker-text">
									提前{{advanceDays[selectedAdvanceIndex]}}天
								</view>
							</picker>
						</view>
						
						<view class="form-item">
							<text class="form-label">图标颜色</text>
							<view class="color-selector">
								<view 
									v-for="color in reminderColors" 
									:key="color"
									class="color-item"
									:class="{active: reminderForm.color === color}"
									:style="{backgroundColor: color}"
									@click="selectColor(color)"
								></view>
							</view>
						</view>
					</view>
				</scroll-view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeReminderPopup">取消</button>
					<button class="save-btn" @click="saveReminder">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			reminders: [],
			currentFilter: 'all',
			showReminderPopup: false,
			editingReminder: null,
			reminderForm: {
				name: '',
				amount: '',
				type: 'bill',
				cycle: 'monthly',
				firstDate: '',
				advanceDays: 3,
				color: '#FF9800',
				icon: '💳'
			},
			filterTabs: [
				{ label: '全部', value: 'all' },
				{ label: '账单', value: 'bill' },
				{ label: '收入', value: 'income' },
				{ label: '已禁用', value: 'disabled' }
			],
			reminderTypes: [
				{ name: '账单', value: 'bill', icon: '💳' },
				{ name: '收入', value: 'income', icon: '💰' },
				{ name: '其他', value: 'other', icon: '📋' }
			],
			cycleOptions: [
				{ name: '每月', value: 'monthly' },
				{ name: '每周', value: 'weekly' },
				{ name: '每年', value: 'yearly' },
				{ name: '仅一次', value: 'once' }
			],
			advanceDays: [0, 1, 2, 3, 5, 7, 10, 15],
			reminderColors: [
				'#FF9800', '#F44336', '#4CAF50', '#2196F3',
				'#9C27B0', '#00BCD4', '#8BC34A', '#FF5722'
			],
			selectedCycleIndex: 0,
			selectedAdvanceIndex: 3
		};
	},
	
	computed: {
		// 即将到期的提醒（7天内）
		upcomingReminders() {
			const now = new Date();
			const sevenDaysLater = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
			
			return this.reminders.filter(reminder => {
				if (!reminder.enabled) return false;
				const nextDate = new Date(reminder.nextDate);
				return nextDate >= now && nextDate <= sevenDaysLater;
			}).sort((a, b) => new Date(a.nextDate) - new Date(b.nextDate));
		},
		
		// 过滤后的提醒
		filteredReminders() {
			switch (this.currentFilter) {
				case 'bill':
					return this.reminders.filter(r => r.type === 'bill');
				case 'income':
					return this.reminders.filter(r => r.type === 'income');
				case 'disabled':
					return this.reminders.filter(r => !r.enabled);
				default:
					return this.reminders;
			}
		}
	},
	
	onLoad() {
		this.loadData();
	},
	
	onShow() {
		this.loadData();
	},
	
	methods: {
		loadData() {
			this.reminders = StorageManager.getReminders();
			this.updateNextDates();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		// 更新下次提醒日期
		updateNextDates() {
			const now = new Date();
			let updated = false;
			
			this.reminders.forEach(reminder => {
				if (reminder.enabled && new Date(reminder.nextDate) < now) {
					reminder.nextDate = this.calculateNextDate(reminder);
					updated = true;
				}
			});
			
			if (updated) {
				StorageManager.saveReminders(this.reminders);
			}
		},
		
		// 计算下次提醒日期
		calculateNextDate(reminder) {
			const firstDate = new Date(reminder.firstDate);
			const now = new Date();
			
			switch (reminder.cycle) {
				case 'weekly':
					while (firstDate < now) {
						firstDate.setDate(firstDate.getDate() + 7);
					}
					break;
				case 'monthly':
					while (firstDate < now) {
						firstDate.setMonth(firstDate.getMonth() + 1);
					}
					break;
				case 'yearly':
					while (firstDate < now) {
						firstDate.setFullYear(firstDate.getFullYear() + 1);
					}
					break;
				default: // once
					return reminder.firstDate;
			}
			
			// 减去提前天数
			firstDate.setDate(firstDate.getDate() - reminder.advanceDays);
			return firstDate.getTime();
		},
		
		// 切换筛选器
		changeFilter(filter) {
			this.currentFilter = filter;
		},
		
		// 添加提醒
		addReminder() {
			this.editingReminder = null;
			this.resetReminderForm();
			this.showReminderPopup = true;
		},
		
		// 查看提醒详情
		viewReminder(reminder) {
			this.editingReminder = reminder;
			this.fillReminderForm(reminder);
			this.showReminderPopup = true;
		},
		
		// 重置表单
		resetReminderForm() {
			this.reminderForm = {
				name: '',
				amount: '',
				type: 'bill',
				cycle: 'monthly',
				firstDate: '',
				advanceDays: 3,
				color: '#FF9800',
				icon: '💳'
			};
			this.selectedCycleIndex = 0;
			this.selectedAdvanceIndex = 3;
		},
		
		// 填充表单
		fillReminderForm(reminder) {
			this.reminderForm = { ...reminder };
			this.selectedCycleIndex = this.cycleOptions.findIndex(c => c.value === reminder.cycle);
			this.selectedAdvanceIndex = this.advanceDays.indexOf(reminder.advanceDays);
		},
		
		// 关闭提醒弹窗
		closeReminderPopup() {
			this.showReminderPopup = false;
			this.editingReminder = null;
		},
		
		// 选择类型
		selectType(type) {
			this.reminderForm.type = type;
			const typeObj = this.reminderTypes.find(t => t.value === type);
			if (typeObj) {
				this.reminderForm.icon = typeObj.icon;
			}
		},
		
		// 周期变化
		onCycleChange(e) {
			this.selectedCycleIndex = e.detail.value;
			this.reminderForm.cycle = this.cycleOptions[e.detail.value].value;
		},
		
		// 获取选中周期名称
		getSelectedCycleName() {
			return this.cycleOptions[this.selectedCycleIndex]?.name || '请选择';
		},
		
		// 首次日期变化
		onFirstDateChange(e) {
			this.reminderForm.firstDate = e.detail.value;
		},
		
		// 提前天数变化
		onAdvanceChange(e) {
			this.selectedAdvanceIndex = e.detail.value;
			this.reminderForm.advanceDays = this.advanceDays[e.detail.value];
		},
		
		// 选择颜色
		selectColor(color) {
			this.reminderForm.color = color;
		},
		
		// 保存提醒
		saveReminder() {
			if (!this.reminderForm.name.trim()) {
				uni.showToast({
					title: '请输入提醒名称',
					icon: 'none'
				});
				return;
			}
			
			if (!this.reminderForm.firstDate) {
				uni.showToast({
					title: '请选择首次提醒日期',
					icon: 'none'
				});
				return;
			}
			
			try {
				const reminderData = {
					id: this.editingReminder ? this.editingReminder.id : `reminder_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
					name: this.reminderForm.name.trim(),
					amount: parseFloat(this.reminderForm.amount) || 0,
					type: this.reminderForm.type,
					cycle: this.reminderForm.cycle,
					firstDate: this.reminderForm.firstDate,
					advanceDays: this.reminderForm.advanceDays,
					color: this.reminderForm.color,
					icon: this.reminderForm.icon,
					enabled: this.editingReminder ? this.editingReminder.enabled : true,
					createdAt: this.editingReminder ? this.editingReminder.createdAt : Date.now(),
					updatedAt: Date.now()
				};
				
				// 计算下次提醒日期
				reminderData.nextDate = this.calculateNextDate(reminderData);
				
				let reminders = StorageManager.getReminders();
				
				if (this.editingReminder) {
					const index = reminders.findIndex(r => r.id === this.editingReminder.id);
					if (index !== -1) {
						reminders[index] = reminderData;
					}
				} else {
					reminders.unshift(reminderData);
				}
				
				StorageManager.saveReminders(reminders);
				this.loadData();
				
				uni.showToast({
					title: this.editingReminder ? '提醒已更新' : '提醒已创建',
					icon: 'success'
				});
				
				this.closeReminderPopup();
				
			} catch (error) {
				console.error('保存提醒失败:', error);
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			}
		},
		
		// 切换提醒开关
		toggleReminder(reminder) {
			const reminders = StorageManager.getReminders();
			const index = reminders.findIndex(r => r.id === reminder.id);
			if (index !== -1) {
				reminders[index].enabled = !reminders[index].enabled;
				StorageManager.saveReminders(reminders);
				this.loadData();
			}
		},
		
		// 完成提醒
		completeReminder(reminder) {
			uni.showModal({
				title: '完成提醒',
				content: `确认已完成"${reminder.name}"？`,
				success: (res) => {
					if (res.confirm) {
						// 更新下次提醒日期
						const reminders = StorageManager.getReminders();
						const index = reminders.findIndex(r => r.id === reminder.id);
						if (index !== -1) {
							if (reminder.cycle === 'once') {
								// 一次性提醒，禁用
								reminders[index].enabled = false;
							} else {
								// 重复提醒，计算下次日期
								reminders[index].nextDate = this.calculateNextDate(reminder);
							}
							StorageManager.saveReminders(reminders);
							this.loadData();
						}
						
						uni.showToast({
							title: '已完成',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 格式化日期
		formatDate(timestamp) {
			const date = new Date(timestamp);
			const today = new Date();
			const tomorrow = new Date(today);
			tomorrow.setDate(tomorrow.getDate() + 1);
			
			if (date.toDateString() === today.toDateString()) {
				return '今天';
			} else if (date.toDateString() === tomorrow.toDateString()) {
				return '明天';
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		
		// 获取剩余天数
		getDaysLeft(timestamp) {
			const now = new Date();
			const date = new Date(timestamp);
			const diffTime = date - now;
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			return Math.max(diffDays, 0);
		},
		
		// 获取周期文本
		getCycleText(cycle) {
			const cycleMap = {
				'weekly': '每周',
				'monthly': '每月',
				'yearly': '每年',
				'once': '仅一次'
			};
			return cycleMap[cycle] || cycle;
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

.upcoming-section, .all-reminders-section {
	margin: 40rpx;
}

.upcoming-section {
	margin-top: -20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.filter-tabs {
	display: flex;
	gap: 20rpx;
}

.filter-tab {
	font-size: 26rpx;
	color: #666;
	padding: 12rpx 24rpx;
	background: white;
	border-radius: 20rpx;
	transition: all 0.3s;
}

.filter-tab.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.reminders-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.reminder-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s;
}

.reminder-item.urgent {
	border-left: 8rpx solid #FF5722;
}

.reminder-item.disabled {
	opacity: 0.5;
}

.reminder-item:active {
	transform: translateY(2rpx);
}

.reminder-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.reminder-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
}

.reminder-info {
	flex: 1;
}

.reminder-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.reminder-amount {
	font-size: 28rpx;
	color: #FF9800;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.reminder-date, .reminder-cycle, .reminder-next {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.reminder-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 12rpx;
}

.days-left {
	font-size: 24rpx;
	color: #FF5722;
	font-weight: bold;
}

.complete-btn {
	background: #4CAF50;
	color: white;
	padding: 12rpx 24rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	font-weight: bold;
}

.empty-state {
	background: white;
	border-radius: 20rpx;
	padding: 100rpx 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.add-reminder-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 48rpx;
	font-size: 28rpx;
	font-weight: bold;
}

.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	width: 100%;
	max-height: 90vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-scroll {
	flex: 1;
	max-height: 60vh;
}

.form-section {
	padding-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

.type-selector {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.type-item {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s;
}

.type-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.type-icon {
	font-size: 28rpx;
	display: block;
	margin-bottom: 8rpx;
}

.type-name {
	font-size: 22rpx;
}

.cycle-picker, .date-picker, .advance-picker {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.color-selector {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.color-item {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	border: 4rpx solid transparent;
	transition: all 0.3s;
}

.color-item.active {
	border-color: #333;
	transform: scale(1.1);
}

.popup-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.cancel-btn, .save-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
</style>
