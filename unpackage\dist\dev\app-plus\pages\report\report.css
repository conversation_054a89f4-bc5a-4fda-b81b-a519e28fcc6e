
.page[data-v-12a8021c] {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 1.25rem;
}

/* 头部样式 */
.header[data-v-12a8021c] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-title[data-v-12a8021c] {
	font-size: 1.375rem;
	font-weight: bold;
	text-align: center;
}

/* 统计卡片 */
.stats-card[data-v-12a8021c] {
	background: white;
	margin: -1.25rem 1.25rem 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}

/* 时间选择 */
.time-selector[data-v-12a8021c] {
	margin-bottom: 1.25rem;
}
.time-tabs[data-v-12a8021c] {
	display: flex;
	background: #f8f9fa;
	border-radius: 0.5rem;
	padding: 0.1875rem;
}
.time-tab[data-v-12a8021c] {
	flex: 1;
	text-align: center;
	padding: 0.5rem 0.625rem;
	border-radius: 0.375rem;
	transition: all 0.3s;
	border: 0.0625rem solid transparent;
}
.time-tab.active[data-v-12a8021c] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.tab-text[data-v-12a8021c] {
	font-size: 0.875rem;
	font-weight: 500;
}

/* 统计数据 */
.stats-overview[data-v-12a8021c] {
	display: flex;
	align-items: center;
}
.stat-item[data-v-12a8021c] {
	flex: 1;
	text-align: center;
}
.stat-label[data-v-12a8021c] {
	font-size: 0.875rem;
	color: #666;
	display: block;
	margin-bottom: 0.25rem;
}
.stat-value[data-v-12a8021c] {
	font-size: 1.125rem;
	font-weight: bold;
	display: block;
	margin-bottom: 0.125rem;
}
.stat-count[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #999;
	display: block;
}
.stat-divider[data-v-12a8021c] {
	width: 0.0625rem;
	height: 2.5rem;
	background-color: #eee;
	margin: 0 0.9375rem;
}

/* 总览卡片 */
.overview-card[data-v-12a8021c] {
	background: white;
	margin: 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	display: flex;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.overview-item[data-v-12a8021c] {
	flex: 1;
	text-align: center;
}
.overview-label[data-v-12a8021c] {
	font-size: 0.8125rem;
	color: #666;
	display: block;
	margin-bottom: 0.3125rem;
}
.overview-value[data-v-12a8021c] {
	font-size: 1rem;
	font-weight: bold;
	display: block;
}
.overview-value.expense[data-v-12a8021c] {
	color: #ff4757;
}
.overview-value.income[data-v-12a8021c] {
	color: #2ed573;
}
.overview-divider[data-v-12a8021c] {
	width: 0.0625rem;
	background-color: #eee;
	margin: 0 0.9375rem;
}
.overview-count[data-v-12a8021c] {
	font-size: 0.6875rem;
	color: #999;
	display: block;
	margin-top: 0.1875rem;
}

/* 趋势分析 */
.trend-card[data-v-12a8021c] {
	background: white;
	margin: 0 1.25rem 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.card-header[data-v-12a8021c] {
	margin-bottom: 0.9375rem;
}
.card-title[data-v-12a8021c] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.trend-content[data-v-12a8021c] {
	display: flex;
	justify-content: space-around;
}
.trend-item[data-v-12a8021c] {
	text-align: center;
	flex: 1;
}
.trend-label[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #666;
	display: block;
	margin-bottom: 0.3125rem;
}
.trend-value[data-v-12a8021c] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
	display: block;
}
.trend-value.expense[data-v-12a8021c] {
	color: #ff4757;
}

/* 分类统计 */
.category-stats[data-v-12a8021c] {
	background: white;
	margin: 0 1.25rem 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.stats-header[data-v-12a8021c] {
	margin-bottom: 0.9375rem;
}
.stats-title[data-v-12a8021c] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.empty[data-v-12a8021c] {
	text-align: center;
	padding: 1.875rem 0;
}
.empty-text[data-v-12a8021c] {
	color: #999;
	font-size: 0.875rem;
}
.stats-list[data-v-12a8021c] {
	/* 统计列表样式 */
}
.stat-item[data-v-12a8021c] {
	position: relative;
	margin-bottom: 0.9375rem;
	padding-bottom: 0.625rem;
}
.stat-item[data-v-12a8021c]:last-child {
	margin-bottom: 0;
}
.stat-left[data-v-12a8021c] {
	display: flex;
	align-items: center;
	margin-bottom: 0.3125rem;
}
.stat-icon[data-v-12a8021c] {
	font-size: 0.875rem;
	margin-right: 0.375rem;
}
.stat-name[data-v-12a8021c] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
}
.stat-right[data-v-12a8021c] {
	position: absolute;
	top: 0;
	right: 0;
	text-align: right;
}
.stat-amount[data-v-12a8021c] {
	font-size: 0.875rem;
	color: #333;
	font-weight: bold;
	display: block;
}
.stat-percent[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #666;
	display: block;
	margin-top: 0.125rem;
}
.stat-bar[data-v-12a8021c] {
	height: 0.25rem;
	background: #f0f0f0;
	border-radius: 0.125rem;
	overflow: hidden;
	margin-top: 0.3125rem;
}
.stat-progress[data-v-12a8021c] {
	height: 100%;
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
	border-radius: 0.125rem;
	transition: width 0.3s ease;
}
.stat-progress.income[data-v-12a8021c] {
	background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
}

/* 图表样式 */
.chart-card[data-v-12a8021c] {
	background: white;
	border-radius: 0.625rem;
	margin: 0.9375rem;
	padding: 0.9375rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0,0,0,0.08);
}

/* 饼图样式 */
.pie-chart-container[data-v-12a8021c] {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 1.25rem;
}
.pie-chart[data-v-12a8021c] {
	position: relative;
	width: 9.375rem;
	height: 9.375rem;
	border-radius: 50%;
	animation: pieGrow-12a8021c 0.8s ease-out;
	box-shadow: 0 0.125rem 0.625rem rgba(0,0,0,0.1);
}
.pie-center[data-v-12a8021c] {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 4.6875rem;
	height: 4.6875rem;
	background: white;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0,0,0,0.1);
}
.pie-total[data-v-12a8021c] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.pie-label[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #666;
	margin-top: 0.125rem;
}
.pie-legend[data-v-12a8021c] {
	display: flex;
	flex-direction: column;
	gap: 0.625rem;
	width: 100%;
}
.legend-item[data-v-12a8021c] {
	display: flex;
	align-items: center;
	gap: 0.625rem;
}
.legend-color[data-v-12a8021c] {
	width: 0.75rem;
	height: 0.75rem;
	border-radius: 0.125rem;
	flex-shrink: 0;
}
.legend-name[data-v-12a8021c] {
	flex: 1;
	font-size: 0.875rem;
	color: #333;
}
.legend-percent[data-v-12a8021c] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #666;
}

/* 折线图样式 */
.line-chart-container[data-v-12a8021c] {
	padding: 0.625rem 0;
}
.line-chart[data-v-12a8021c] {
	position: relative;
	height: 12.5rem;
	display: flex;
	flex-direction: column;
}
.y-axis[data-v-12a8021c] {
	position: absolute;
	left: 0;
	top: 0;
	height: 10rem;
	width: 2.5rem;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: flex-end;
	padding-right: 0.625rem;
}
.y-label[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #999;
}
.chart-area[data-v-12a8021c] {
	position: relative;
	height: 10rem;
	margin-left: 2.5rem;
	margin-bottom: 1.25rem;
	border-left: 0.0625rem solid #eee;
	border-bottom: 0.0625rem solid #eee;
}
.grid-lines[data-v-12a8021c] {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.grid-line[data-v-12a8021c] {
	height: 0.03125rem;
	background: #f5f5f5;
	width: 100%;
}
.data-points[data-v-12a8021c] {
	position: relative;
	width: 100%;
	height: 100%;
}
.data-point[data-v-12a8021c] {
	position: absolute;
	transform: translate(-50%, 50%);
}
.point[data-v-12a8021c] {
	width: 0.5rem;
	height: 0.5rem;
	border-radius: 50%;
	background: #ff4757;
	border: 0.125rem solid white;
	box-shadow: 0 0.0625rem 0.25rem rgba(255, 71, 87, 0.3);
	animation: pointPulse-12a8021c 2s infinite;
}
.point-value[data-v-12a8021c] {
	position: absolute;
	top: -1.25rem;
	left: 50%;
	transform: translateX(-50%);
	font-size: 0.6875rem;
	color: #666;
	white-space: nowrap;
	background: rgba(255, 255, 255, 0.9);
	padding: 0.125rem 0.25rem;
	border-radius: 0.25rem;
}
.x-axis[data-v-12a8021c] {
	display: flex;
	justify-content: space-between;
	margin-left: 2.5rem;
	padding-top: 0.625rem;
}
.x-label[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #999;
	text-align: center;
	flex: 1;
}

/* 动画 */
@keyframes pieGrow-12a8021c {
from {
		transform: scale(0);
}
to {
		transform: scale(1);
}
}
@keyframes pointPulse-12a8021c {
0%, 100% {
		transform: translate(-50%, 50%) scale(1);
}
50% {
		transform: translate(-50%, 50%) scale(1.2);
}
}

/* 导出功能样式 */
.export-section[data-v-12a8021c] {
	margin: 0 1.25rem 1.25rem;
}
.section-header[data-v-12a8021c] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.625rem;
}
.section-title[data-v-12a8021c] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.export-actions[data-v-12a8021c] {
	display: flex;
	justify-content: space-around;
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem 0.625rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.export-item[data-v-12a8021c] {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0.625rem;
	border-radius: 0.5rem;
	transition: all 0.2s;
	min-width: 3.75rem;
}
.export-item[data-v-12a8021c]:active {
	background-color: #f8f9fa;
	transform: scale(0.95);
}
.export-icon[data-v-12a8021c] {
	font-size: 1.25rem;
	margin-bottom: 0.375rem;
}
.export-text[data-v-12a8021c] {
	font-size: 0.75rem;
	color: #333;
	font-weight: 500;
}
