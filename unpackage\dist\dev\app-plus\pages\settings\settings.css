
.page[data-v-7fad0a1c] {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 1.25rem;
}

/* 头部样式 */
.header[data-v-7fad0a1c] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-title[data-v-7fad0a1c] {
	font-size: 1.375rem;
	font-weight: bold;
	text-align: center;
}

/* 统计卡片 */
.stats-card[data-v-7fad0a1c] {
	background: white;
	margin: -1.25rem 1.25rem 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	display: flex;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.stat-item[data-v-7fad0a1c] {
	flex: 1;
	text-align: center;
}
.stat-label[data-v-7fad0a1c] {
	font-size: 0.875rem;
	color: #666;
	display: block;
	margin-bottom: 0.3125rem;
}
.stat-value[data-v-7fad0a1c] {
	font-size: 1rem;
	font-weight: bold;
	display: block;
}
.stat-divider[data-v-7fad0a1c] {
	width: 0.0625rem;
	background-color: #eee;
	margin: 0 0.9375rem;
}

/* 分组样式 */
.section[data-v-7fad0a1c] {
	margin: 0 1.25rem 1.25rem;
}
.section-title[data-v-7fad0a1c] {
	padding: 0.9375rem 0 0.625rem;
	font-size: 0.875rem;
	color: #666;
	font-weight: 500;
}

/* 设置项样式 */
.setting-item[data-v-7fad0a1c] {
	background: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-radius: 0.625rem;
	margin-bottom: 0.0625rem;
	transition: background-color 0.2s;
}
.setting-item[data-v-7fad0a1c]:first-child {
	border-top-left-radius: 0.625rem;
	border-top-right-radius: 0.625rem;
}
.setting-item[data-v-7fad0a1c]:last-child {
	border-bottom-left-radius: 0.625rem;
	border-bottom-right-radius: 0.625rem;
	margin-bottom: 0;
}
.setting-item[data-v-7fad0a1c]:active {
	background-color: #f8f9fa;
}
.item-left[data-v-7fad0a1c] {
	display: flex;
	align-items: center;
	flex: 1;
}
.item-right[data-v-7fad0a1c] {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}
.item-icon[data-v-7fad0a1c] {
	font-size: 1rem;
	margin-right: 0.75rem;
}
.item-title[data-v-7fad0a1c] {
	font-size: 1rem;
	color: #333;
	font-weight: 500;
}
.item-value[data-v-7fad0a1c] {
	font-size: 0.875rem;
	color: #666;
}
.item-arrow[data-v-7fad0a1c] {
	font-size: 0.875rem;
	color: #ccc;
	font-weight: bold;
}

/* 同步信息 */
.sync-info[data-v-7fad0a1c] {
	padding: 0.625rem 1.25rem;
	text-align: center;
}
.sync-text[data-v-7fad0a1c] {
	font-size: 0.75rem;
	color: #999;
}
