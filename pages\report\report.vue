<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="header-title">统计报表</text>
		</view>
		
		<!-- 统计卡片（包含时间选择） -->
		<view class="stats-card" :style="{ backgroundColor: themeColors.backgroundCard }">
			<!-- 时间选择 -->
			<view class="time-selector">
				<view class="time-tabs">
					<view 
						v-for="(tab, index) in timeTabs" 
						:key="index" 
						class="time-tab" 
						:class="{active: currentTimeIndex === index}"
						:style="{ 
							backgroundColor: currentTimeIndex === index ? themeColors.primary : 'transparent',
							borderColor: themeColors.border,
							color: currentTimeIndex === index ? themeColors.textReverse : themeColors.textPrimary
						}"
						@click="switchTime(index)"
					>
						<text class="tab-text" :style="{ color: currentTimeIndex === index ? themeColors.textReverse : themeColors.textPrimary }">{{tab.name}}</text>
					</view>
				</view>
			</view>
			
			<!-- 统计数据 -->
			<view class="stats-overview">
				<view class="stat-item">
					<text class="stat-label" :style="{ color: themeColors.textSecondary }">支出</text>
					<text class="stat-value" :style="{ color: themeColors.expense }">¥{{currentExpense.toFixed(2)}}</text>
					<text class="stat-count" :style="{ color: themeColors.textMuted }">{{expenseCount}}笔</text>
				</view>
				<view class="stat-divider" :style="{ backgroundColor: themeColors.border }"></view>
				<view class="stat-item">
					<text class="stat-label" :style="{ color: themeColors.textSecondary }">收入</text>
					<text class="stat-value" :style="{ color: themeColors.income }">¥{{currentIncome.toFixed(2)}}</text>
					<text class="stat-count" :style="{ color: themeColors.textMuted }">{{incomeCount}}笔</text>
				</view>
				<view class="stat-divider" :style="{ backgroundColor: themeColors.border }"></view>
				<view class="stat-item">
					<text class="stat-label" :style="{ color: themeColors.textSecondary }">结余</text>
					<text class="stat-value" :style="{ color: balance >= 0 ? themeColors.income : themeColors.expense }">¥{{balance.toFixed(2)}}</text>
					<text class="stat-count" :style="{ color: themeColors.textMuted }">{{totalCount}}笔</text>
				</view>
			</view>
		</view>
		

		
		<!-- 趋势分析 -->
		<view class="trend-card" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="card-header">
				<text class="card-title" :style="{ color: themeColors.textPrimary }">趋势分析</text>
			</view>
			<view class="trend-content">
				<view class="trend-item">
					<text class="trend-label" :style="{ color: themeColors.textSecondary }">日均支出</text>
					<text class="trend-value" :style="{ color: themeColors.expense }">¥{{dailyAvgExpense.toFixed(2)}}</text>
				</view>
				<view class="trend-item">
					<text class="trend-label" :style="{ color: themeColors.textSecondary }">最大单笔</text>
					<text class="trend-value" :style="{ color: themeColors.textPrimary }">¥{{maxAmount.toFixed(2)}}</text>
				</view>
				<view class="trend-item">
					<text class="trend-label" :style="{ color: themeColors.textSecondary }">记账天数</text>
					<text class="trend-value" :style="{ color: themeColors.textPrimary }">{{recordDays}}天</text>
				</view>
			</view>
		</view>
		
		<!-- 支出饼图 -->
		<view class="chart-card" v-if="expenseStats.length > 0" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="card-header">
				<text class="card-title" :style="{ color: themeColors.textPrimary }">支出分类</text>
			</view>
			<view class="pie-chart-container">
				<view class="pie-chart" :style="{background: pieChartBackground}">
					<view class="pie-center">
						<text class="pie-total" :style="{ color: themeColors.textPrimary }">¥{{currentExpense.toFixed(0)}}</text>
						<text class="pie-label" :style="{ color: themeColors.textSecondary }">总支出</text>
					</view>
				</view>
				<view class="pie-legend">
					<view 
						v-for="(stat, index) in expenseStats.slice(0, 5)" 
						:key="stat.categoryId"
						class="legend-item"
					>
						<view class="legend-color" :style="{backgroundColor: getChartColor(index)}"></view>
						<text class="legend-name" :style="{ color: themeColors.textPrimary }">{{stat.name}}</text>
						<text class="legend-percent" :style="{ color: themeColors.textSecondary }">{{stat.percent}}%</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 月度趋势图 -->
		<view class="chart-card" v-if="monthlyTrend.length > 0" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="card-header">
				<text class="card-title" :style="{ color: themeColors.textPrimary }">月度趋势</text>
			</view>
			<view class="line-chart-container">
				<view class="line-chart">
					<!-- Y轴标签 -->
					<view class="y-axis">
						<text class="y-label" v-for="label in yAxisLabels" :key="label" :style="{ color: themeColors.textMuted }">{{label}}</text>
					</view>
					
					<!-- 图表区域 -->
					<view class="chart-area">
						<!-- 网格线 -->
						<view class="grid-lines">
							<view class="grid-line" v-for="i in 4" :key="i" :style="{ borderColor: themeColors.border }"></view>
						</view>
						
						<!-- 数据点和线 -->
						<view class="data-points">
							<view 
								v-for="(point, index) in chartPoints" 
								:key="index"
								class="data-point"
								:style="{
									left: point.x + '%',
									bottom: point.y + '%'
								}"
							>
								<view class="point expense"></view>
								<text class="point-value" :style="{ color: themeColors.textPrimary }">¥{{point.value}}</text>
							</view>
						</view>
					</view>
					
					<!-- X轴标签 -->
					<view class="x-axis">
						<text class="x-label" v-for="month in monthlyTrend" :key="month.month" :style="{ color: themeColors.textMuted }">{{month.month}}月</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 支出分类统计 -->
		<view class="category-stats" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="stats-header">
				<text class="stats-title" :style="{ color: themeColors.textPrimary }">支出分类</text>
			</view>
			
			<view v-if="expenseStats.length === 0" class="empty">
				<text class="empty-text" :style="{ color: themeColors.textMuted }">暂无支出记录</text>
			</view>
			
			<view v-else class="stats-list">
				<view v-for="stat in expenseStats" :key="stat.categoryId" class="stat-item">
					<view class="stat-left">
						<text class="stat-icon">{{stat.icon}}</text>
						<text class="stat-name" :style="{ color: themeColors.textPrimary }">{{stat.name}}</text>
					</view>
					<view class="stat-right">
						<text class="stat-amount" :style="{ color: themeColors.expense }">¥{{stat.amount.toFixed(2)}}</text>
						<text class="stat-percent" :style="{ color: themeColors.textSecondary }">{{stat.percent}}%</text>
					</view>
					<view class="stat-bar" :style="{ backgroundColor: themeColors.border }">
						<view class="stat-progress" :style="{width: stat.percent + '%', backgroundColor: themeColors.primary}"></view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 收入分类统计 -->
		<view class="category-stats" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="stats-header">
				<text class="stats-title" :style="{ color: themeColors.textPrimary }">收入分类</text>
			</view>
			
			<view v-if="incomeStats.length === 0" class="empty">
				<text class="empty-text" :style="{ color: themeColors.textMuted }">暂无收入记录</text>
			</view>
			
			<view v-else class="stats-list">
				<view v-for="stat in incomeStats" :key="stat.categoryId" class="stat-item">
					<view class="stat-left">
						<text class="stat-icon">{{stat.icon}}</text>
						<text class="stat-name" :style="{ color: themeColors.textPrimary }">{{stat.name}}</text>
					</view>
					<view class="stat-right">
						<text class="stat-amount" :style="{ color: themeColors.income }">¥{{stat.amount.toFixed(2)}}</text>
						<text class="stat-percent" :style="{ color: themeColors.textSecondary }">{{stat.percent}}%</text>
					</view>
					<view class="stat-bar" :style="{ backgroundColor: themeColors.border }">
						<view class="stat-progress" :style="{width: stat.percent + '%', backgroundColor: themeColors.income}"></view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 导出功能 -->
		<view class="export-section" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="section-header">
				<text class="section-title" :style="{ color: themeColors.textPrimary }">数据导出</text>
			</view>
			<view class="export-actions">
				<view class="export-item" @click="exportToCSV" :style="{ borderColor: themeColors.border }">
					<text class="export-icon">📄</text>
					<text class="export-text" :style="{ color: themeColors.textPrimary }">导出CSV</text>
				</view>
				<view class="export-item" @click="exportToExcel" :style="{ borderColor: themeColors.border }">
					<text class="export-icon">📈</text>
					<text class="export-text" :style="{ color: themeColors.textPrimary }">导出Excel</text>
				</view>
				<view class="export-item" @click="shareReport" :style="{ borderColor: themeColors.border }">
					<text class="export-icon">📤</text>
					<text class="export-text" :style="{ color: themeColors.textPrimary }">分享报表</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import ThemeManager from '@/utils/themeManager.js';

export default {
	data() {
		return {
			records: [],
			currentTimeIndex: 0,
			timeTabs: [
				{ name: '本月', value: 'month' },
				{ name: '本年', value: 'year' },
				{ name: '全部', value: 'all' }
			],
			chartColors: [
				'#FF6B7A', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57',
				'#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43'
			],
			themeColors: ThemeManager.getColors()
		};
	},
	computed: {
		// 当前时间范围的记录
		currentRecords() {
			const timeType = this.timeTabs[this.currentTimeIndex].value;
			const now = new Date();
			
			if (timeType === 'all') {
				return this.records;
			}
			
			return this.records.filter(record => {
				const recordDate = new Date(record.date);
				
				if (timeType === 'month') {
					return recordDate.getMonth() === now.getMonth() && 
						   recordDate.getFullYear() === now.getFullYear();
				} else if (timeType === 'year') {
					return recordDate.getFullYear() === now.getFullYear();
				}
				
				return true;
			});
		},
		
		// 当前支出
		currentExpense() {
			return this.currentRecords
				.filter(r => r.type === 'expense')
				.reduce((sum, r) => sum + r.amount, 0);
		},
		
		// 当前收入
		currentIncome() {
			return this.currentRecords
				.filter(r => r.type === 'income')
				.reduce((sum, r) => sum + r.amount, 0);
		},
		
		// 结余
		balance() {
			return this.currentIncome - this.currentExpense;
		},
		
		// 支出统计
		expenseStats() {
			return this.getCategoryStats('expense');
		},
		
		// 收入统计
		incomeStats() {
			return this.getCategoryStats('income');
		},
		
		// 支出笔数
		expenseCount() {
			return this.currentRecords.filter(r => r.type === 'expense').length;
		},
		
		// 收入笔数
		incomeCount() {
			return this.currentRecords.filter(r => r.type === 'income').length;
		},
		
		// 总笔数
		totalCount() {
			return this.currentRecords.length;
		},
		
		// 日均支出
		dailyAvgExpense() {
			if (this.recordDays === 0) return 0;
			return this.currentExpense / this.recordDays;
		},
		
		// 最大单笔金额
		maxAmount() {
			if (this.currentRecords.length === 0) return 0;
			return Math.max(...this.currentRecords.map(r => r.amount));
		},
		
		// 记账天数
		recordDays() {
			if (this.currentRecords.length === 0) return 0;
			
			const dates = [...new Set(this.currentRecords.map(r => {
				const date = new Date(r.date);
				return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
			}))]; 
			
			return dates.length;
		},
		
		// 饼图背景
		pieChartBackground() {
			if (this.expenseStats.length === 0) {
				return 'conic-gradient(#f0f0f0 0deg 360deg)';
			}
			
			let currentAngle = 0;
			const gradientStops = [];
			
			this.expenseStats.slice(0, 5).forEach((stat, index) => {
				const angle = (stat.percent / 100) * 360;
				const color = this.chartColors[index];
				
				gradientStops.push(`${color} ${currentAngle}deg ${currentAngle + angle}deg`);
				currentAngle += angle;
			});
			
			// 填充剩余角度
			if (currentAngle < 360) {
				gradientStops.push(`#f0f0f0 ${currentAngle}deg 360deg`);
			}
			
			return `conic-gradient(${gradientStops.join(', ')})`;
		},
		
		// 月度趋势数据
		monthlyTrend() {
			if (this.currentTimeIndex !== 1) return []; // 只在本年显示
			
			const currentYear = new Date().getFullYear();
			const monthlyData = {};
			
			// 初始化所有月份
			for (let i = 1; i <= 12; i++) {
				monthlyData[i] = { month: i, expense: 0, income: 0 };
			}
			
			// 统计每月数据
			this.records.forEach(record => {
				const date = new Date(record.date);
				if (date.getFullYear() === currentYear) {
					const month = date.getMonth() + 1;
					if (record.type === 'expense') {
						monthlyData[month].expense += record.amount;
					} else if (record.type === 'income') {
						monthlyData[month].income += record.amount;
					}
				}
			});
			
			return Object.values(monthlyData).filter(data => data.expense > 0 || data.income > 0);
		},
		
		// 图表点数据
		chartPoints() {
			if (this.monthlyTrend.length === 0) return [];
			
			const maxAmount = Math.max(...this.monthlyTrend.map(m => m.expense));
			if (maxAmount === 0) return [];
			
			return this.monthlyTrend.map((month, index) => ({
				x: (index / (this.monthlyTrend.length - 1)) * 100,
				y: (month.expense / maxAmount) * 80,
				value: month.expense.toFixed(0)
			}));
		},
		
		// Y轴标签
		yAxisLabels() {
			if (this.monthlyTrend.length === 0) return [];
			
			const maxAmount = Math.max(...this.monthlyTrend.map(m => m.expense));
			if (maxAmount === 0) return [];
			
			const step = maxAmount / 4;
			return [
				(maxAmount).toFixed(0),
				(maxAmount * 0.75).toFixed(0),
				(maxAmount * 0.5).toFixed(0),
				(maxAmount * 0.25).toFixed(0),
				'0'
			];
		}
	},
	onShow() {
		this.loadData();
	},
	
	onLoad() {
		// 设置主题监听器
		ThemeManager.addListener(this.onThemeChange);
	},
	
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	methods: {
		// 加载数据
		loadData() {
			this.records = StorageManager.getRecords();
		},
		
		// 切换时间范围
		switchTime(index) {
			this.currentTimeIndex = index;
		},
		
		// 获取分类统计
		getCategoryStats(type) {
			const typeRecords = this.currentRecords.filter(r => r.type === type);
			const total = typeRecords.reduce((sum, r) => sum + r.amount, 0);
			
			if (total === 0) return [];
			
			// 按分类统计
			const categoryMap = {};
			typeRecords.forEach(record => {
				if (!categoryMap[record.categoryId]) {
					categoryMap[record.categoryId] = {
						categoryId: record.categoryId,
						name: this.getCategoryName(record.categoryId),
						icon: this.getCategoryIcon(record.categoryId),
						amount: 0
					};
				}
				categoryMap[record.categoryId].amount += record.amount;
			});
			
			// 转换为数组并计算百分比
			const stats = Object.values(categoryMap).map(item => ({
				...item,
				percent: Math.round((item.amount / total) * 100)
			}));
			
			// 按金额排序
			return stats.sort((a, b) => b.amount - a.amount);
		},
		
		// 获取分类名称
		getCategoryName(categoryId) {
			const category = this.getDefaultCategories().find(c => c.id === categoryId);
			return category ? category.name : '未知分类';
		},
		
		// 获取分类图标
		getCategoryIcon(categoryId) {
			const category = this.getDefaultCategories().find(c => c.id === categoryId);
			return category ? category.icon : '💰';
		},
		
		// 获取默认分类
		getDefaultCategories() {
			return [
				// 支出分类
				{ id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily' },
				{ id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily' },
				{ id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily' },
				{ id: 'entertainment', name: '娱乐', icon: '🎮', type: 'expense', groupId: 'daily' },
				{ id: 'medical', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily' },
				{ id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily' },
				{ id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily' },
				{ id: 'utilities', name: '水电', icon: '💡', type: 'expense', groupId: 'daily' },
				
				// 收入分类
				{ id: 'salary', name: '工资', icon: '💰', type: 'income', groupId: 'daily' },
				{ id: 'bonus', name: '奖金', icon: '🎁', type: 'income', groupId: 'daily' },
				{ id: 'investment', name: '投资', icon: '📈', type: 'income', groupId: 'daily' },
				{ id: 'other_income', name: '其他收入', icon: '💵', type: 'income', groupId: 'daily' },
				
				// 报销分类
				{ id: 'business_meal', name: '商务餐饮', icon: '🍽️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'business_transport', name: '差旅交通', icon: '✈️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'accommodation', name: '住宿费', icon: '🏨', type: 'expense', groupId: 'reimbursement' },
				{ id: 'office_supplies', name: '办公用品', icon: '📎', type: 'expense', groupId: 'reimbursement' },
				{ id: 'communication', name: '通讯费', icon: '📱', type: 'expense', groupId: 'reimbursement' },
				{ id: 'training', name: '培训费', icon: '🎓', type: 'expense', groupId: 'reimbursement' },
				{ id: 'entertainment_business', name: '商务招待', icon: '🍷', type: 'expense', groupId: 'reimbursement' },
				{ id: 'other_reimbursement', name: '其他报销', icon: '📋', type: 'expense', groupId: 'reimbursement' }
			];
		},
		
		// 获取图表颜色
		getChartColor(index) {
			return this.chartColors[index % this.chartColors.length];
		},
		
		// 导出CSV
		exportToCSV() {
			const timeRange = this.timeTabs[this.currentTimeIndex].name;
			const records = this.getFilteredRecords();
			
			let csvContent = '时间,类型,分类,金额,备注\n';
			
			records.forEach(record => {
				const date = new Date(record.date).toLocaleDateString();
				const type = record.type === 'expense' ? '支出' : '收入';
				const category = this.getCategoryName(record.categoryId);
				const amount = record.amount.toFixed(2);
				const note = record.note || '';
				
				csvContent += `${date},${type},${category},${amount},${note}\n`;
			});
			
			// 添加统计信息
			csvContent += '\n统计信息\n';
			csvContent += `时间范围,${timeRange}\n`;
			csvContent += `总支出,${this.currentExpense.toFixed(2)}\n`;
			csvContent += `总收入,${this.currentIncome.toFixed(2)}\n`;
			csvContent += `结余,${this.balance.toFixed(2)}\n`;
			
			uni.setClipboardData({
				data: csvContent,
				success: () => {
					uni.showToast({ title: 'CSV数据已复制到剪贴板', icon: 'success' });
				}
			});
		},
		
		// 导出Excel
		exportToExcel() {
			uni.showModal({
				title: '导出Excel',
				content: '此功能将在后续版本中实现，支持导出为Excel格式文件',
				showCancel: false
			});
		},
		
		// 分享报表
		shareReport() {
			const timeRange = this.timeTabs[this.currentTimeIndex].name;
			const shareText = `${timeRange}财务报表\n` +
				`支出：¥${this.currentExpense.toFixed(2)}\n` +
				`收入：¥${this.currentIncome.toFixed(2)}\n` +
				`结余：¥${this.balance.toFixed(2)}\n` +
				`\n由碎米记账生成`;
			
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				summary: shareText,
				success: () => {
					uni.showToast({ title: '分享成功', icon: 'success' });
				},
				fail: () => {
					// 如果分享失败，就复制到剪贴板
					uni.setClipboardData({
						data: shareText,
						success: () => {
							uni.showToast({ title: '报表内容已复制到剪贴板', icon: 'success' });
						}
					});
				}
			});
		},
		
		// 主题变化回调
		onThemeChange(theme) {
			this.themeColors = theme.colors;
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-title {
	font-size: 44rpx;
	font-weight: bold;
	text-align: center;
}

/* 统计卡片 */
.stats-card {
	background: white;
	margin: -40rpx 40rpx 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 时间选择 */
.time-selector {
	margin-bottom: 40rpx;
}

.time-tabs {
	display: flex;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 6rpx;
}

.time-tab {
	flex: 1;
	text-align: center;
	padding: 16rpx 20rpx;
	border-radius: 12rpx;
	transition: all 0.3s;
	border: 2rpx solid transparent;
}

.time-tab.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 统计数据 */
.stats-overview {
	display: flex;
	align-items: center;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-label {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.stat-value {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 4rpx;
}

.stat-count {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.stat-divider {
	width: 2rpx;
	height: 80rpx;
	background-color: #eee;
	margin: 0 30rpx;
}

/* 总览卡片 */
.overview-card {
	background: white;
	margin: 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.overview-item {
	flex: 1;
	text-align: center;
}

.overview-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.overview-value {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
}

.overview-value.expense {
	color: #ff4757;
}

.overview-value.income {
	color: #2ed573;
}

.overview-divider {
	width: 2rpx;
	background-color: #eee;
	margin: 0 30rpx;
}

.overview-count {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-top: 6rpx;
}

/* 趋势分析 */
.trend-card {
	background: white;
	margin: 0 40rpx 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	margin-bottom: 30rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.trend-content {
	display: flex;
	justify-content: space-around;
}

.trend-item {
	text-align: center;
	flex: 1;
}

.trend-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.trend-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.trend-value.expense {
	color: #ff4757;
}

/* 分类统计 */
.category-stats {
	background: white;
	margin: 0 40rpx 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-header {
	margin-bottom: 30rpx;
}

.stats-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.empty {
	text-align: center;
	padding: 60rpx 0;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}

.stats-list {
	/* 统计列表样式 */
}

.stat-item {
	position: relative;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
}

.stat-item:last-child {
	margin-bottom: 0;
}

.stat-left {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.stat-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
}

.stat-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.stat-right {
	position: absolute;
	top: 0;
	right: 0;
	text-align: right;
}

.stat-amount {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
}

.stat-percent {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-top: 4rpx;
}

.stat-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
	margin-top: 10rpx;
}

.stat-progress {
	height: 100%;
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
	border-radius: 4rpx;
	transition: width 0.3s ease;
}

.stat-progress.income {
	background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
}

/* 图表样式 */
.chart-card {
	background: white;
	border-radius: 20rpx;
	margin: 30rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

/* 饼图样式 */
.pie-chart-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 40rpx;
}

.pie-chart {
	position: relative;
	width: 300rpx;
	height: 300rpx;
	border-radius: 50%;
	animation: pieGrow 0.8s ease-out;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.pie-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 150rpx;
	height: 150rpx;
	background: white;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.pie-total {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.pie-label {
	font-size: 24rpx;
	color: #666;
	margin-top: 4rpx;
}

.pie-legend {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	width: 100%;
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.legend-color {
	width: 24rpx;
	height: 24rpx;
	border-radius: 4rpx;
	flex-shrink: 0;
}

.legend-name {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.legend-percent {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
}

/* 折线图样式 */
.line-chart-container {
	padding: 20rpx 0;
}

.line-chart {
	position: relative;
	height: 400rpx;
	display: flex;
	flex-direction: column;
}

.y-axis {
	position: absolute;
	left: 0;
	top: 0;
	height: 320rpx;
	width: 80rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: flex-end;
	padding-right: 20rpx;
}

.y-label {
	font-size: 24rpx;
	color: #999;
}

.chart-area {
	position: relative;
	height: 320rpx;
	margin-left: 80rpx;
	margin-bottom: 40rpx;
	border-left: 2rpx solid #eee;
	border-bottom: 2rpx solid #eee;
}

.grid-lines {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.grid-line {
	height: 1rpx;
	background: #f5f5f5;
	width: 100%;
}

.data-points {
	position: relative;
	width: 100%;
	height: 100%;
}

.data-point {
	position: absolute;
	transform: translate(-50%, 50%);
}

.point {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #ff4757;
	border: 4rpx solid white;
	box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
	animation: pointPulse 2s infinite;
}

.point-value {
	position: absolute;
	top: -40rpx;
	left: 50%;
	transform: translateX(-50%);
	font-size: 22rpx;
	color: #666;
	white-space: nowrap;
	background: rgba(255, 255, 255, 0.9);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.x-axis {
	display: flex;
	justify-content: space-between;
	margin-left: 80rpx;
	padding-top: 20rpx;
}

.x-label {
	font-size: 24rpx;
	color: #999;
	text-align: center;
	flex: 1;
}

/* 动画 */
@keyframes pieGrow {
	from {
		transform: scale(0);
	}
	to {
		transform: scale(1);
	}
}

@keyframes pointPulse {
	0%, 100% {
		transform: translate(-50%, 50%) scale(1);
	}
	50% {
		transform: translate(-50%, 50%) scale(1.2);
	}
}

/* 导出功能样式 */
.export-section {
	margin: 0 40rpx 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.export-actions {
	display: flex;
	justify-content: space-around;
	background: white;
	border-radius: 20rpx;
	padding: 40rpx 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.export-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 16rpx;
	transition: all 0.2s;
	min-width: 120rpx;
}

.export-item:active {
	background-color: #f8f9fa;
	transform: scale(0.95);
}

.export-icon {
	font-size: 40rpx;
	margin-bottom: 12rpx;
}

.export-text {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}
</style>
