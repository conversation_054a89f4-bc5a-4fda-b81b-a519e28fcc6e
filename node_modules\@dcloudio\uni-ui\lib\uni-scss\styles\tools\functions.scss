// 合并 map
@function map-deep-merge($parent-map, $child-map){
	$result: $parent-map;
	@each $key, $child in $child-map {
		$parent-has-key: map-has-key($result, $key);
		$parent-value: map-get($result, $key);
		$parent-type: type-of($parent-value);
		$child-type: type-of($child);
		$parent-is-map: $parent-type == map;
		$child-is-map: $child-type == map;
			
		@if (not $parent-has-key) or ($parent-type != $child-type) or (not ($parent-is-map and $child-is-map)){
			$result: map-merge($result, ( $key: $child ));
		}@else {
			$result: map-merge($result, ( $key: map-deep-merge($parent-value, $child) ));
		}
	}
	@return $result;
};
