<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="header-title">主题设置</text>
		</view>
		
		<!-- 当前主题预览 -->
		<view class="current-theme" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="preview-card" :style="{ background: themeColors.gradient }">
				<text class="preview-title">当前主题</text>
				<text class="preview-name">{{ currentTheme.name }}</text>
			</view>
		</view>
		
		<!-- 主题列表 -->
		<view class="theme-section">
			<view class="section-title" :style="{ color: themeColors.textPrimary }">
				<text>选择主题</text>
			</view>
			
			<view class="theme-grid">
				<view 
					v-for="theme in themeList" 
					:key="theme.key"
					class="theme-item"
					:class="{ active: currentTheme.key === theme.key }"
					:style="{ 
						backgroundColor: themeColors.backgroundCard,
						borderColor: currentTheme.key === theme.key ? themeColors.primary : themeColors.border
					}"
					@click="selectTheme(theme)"
				>
					<!-- 主题预览 -->
					<view class="theme-preview">
						<view 
							class="preview-header" 
							:style="{ background: theme.colors.gradient }"
						></view>
						<view 
							class="preview-body" 
							:style="{ backgroundColor: theme.colors.background }"
						>
							<view class="preview-item" :style="{ backgroundColor: theme.colors.backgroundCard }"></view>
							<view class="preview-item" :style="{ backgroundColor: theme.colors.backgroundCard }"></view>
						</view>
					</view>
					
					<!-- 主题信息 -->
					<view class="theme-info">
						<text class="theme-name" :style="{ color: themeColors.textPrimary }">{{ theme.name }}</text>
						<view class="theme-colors">
							<view 
								v-for="(color, index) in getThemeColors(theme)" 
								:key="index"
								class="color-dot" 
								:style="{ backgroundColor: color }"
							></view>
						</view>
					</view>
					
					<!-- 选中标识 -->
					<view v-if="currentTheme.key === theme.key" class="selected-icon">
						<text class="icon">✓</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 主题设置 -->
		<view class="settings-section">
			<view class="section-title" :style="{ color: themeColors.textPrimary }">
				<text>主题设置</text>
			</view>
			
			<view class="setting-item" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="item-left">
					<text class="item-icon">🌙</text>
					<view class="item-content">
						<text class="item-title" :style="{ color: themeColors.textPrimary }">跟随系统</text>
						<text class="item-desc" :style="{ color: themeColors.textSecondary }">根据系统设置自动切换深色模式</text>
					</view>
				</view>
				<switch 
					:checked="followSystem" 
					@change="onFollowSystemChange" 
					:color="themeColors.primary" 
				/>
			</view>
			
			<view class="setting-item" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="item-left">
					<text class="item-icon">🎨</text>
					<view class="item-content">
						<text class="item-title" :style="{ color: themeColors.textPrimary }">动态主题</text>
						<text class="item-desc" :style="{ color: themeColors.textSecondary }">根据时间自动切换主题</text>
					</view>
				</view>
				<switch 
					:checked="dynamicTheme" 
					@change="onDynamicThemeChange" 
					:color="themeColors.primary" 
				/>
			</view>
		</view>
		
		<!-- 预设方案 -->
		<view class="presets-section">
			<view class="section-title" :style="{ color: themeColors.textPrimary }">
				<text>快速切换</text>
			</view>
			
			<view class="preset-buttons">
				<view 
					class="preset-btn"
					:style="{ 
						backgroundColor: themeColors.backgroundCard,
						borderColor: themeColors.border
					}"
					@click="switchToLight"
				>
					<text class="preset-icon">☀️</text>
					<text class="preset-text" :style="{ color: themeColors.textPrimary }">浅色</text>
				</view>
				
				<view 
					class="preset-btn"
					:style="{ 
						backgroundColor: themeColors.backgroundCard,
						borderColor: themeColors.border
					}"
					@click="switchToDark"
				>
					<text class="preset-icon">🌙</text>
					<text class="preset-text" :style="{ color: themeColors.textPrimary }">深色</text>
				</view>
				
				<view 
					class="preset-btn"
					:style="{ 
						backgroundColor: themeColors.backgroundCard,
						borderColor: themeColors.border
					}"
					@click="switchRandom"
				>
					<text class="preset-icon">🎲</text>
					<text class="preset-text" :style="{ color: themeColors.textPrimary }">随机</text>
				</view>
				
				<view 
					class="preset-btn"
					:style="{ 
						backgroundColor: themeColors.backgroundCard,
						borderColor: themeColors.border
					}"
					@click="openThemeTest"
				>
					<text class="preset-icon">🧪</text>
					<text class="preset-text" :style="{ color: themeColors.textPrimary }">测试</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import ThemeManager from '@/utils/themeManager.js';

export default {
	name: 'ThemePage',
	data() {
		return {
			currentTheme: ThemeManager.getCurrentTheme(),
			themeList: ThemeManager.getThemeList(),
			followSystem: false,
			dynamicTheme: false,
			themeColors: ThemeManager.getColors()
		};
	},
	
	onLoad() {
		this.loadSettings();
		this.setupThemeListener();
	},
	
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	
	methods: {
		/**
		 * 加载设置
		 */
		loadSettings() {
			this.followSystem = uni.getStorageSync('theme_follow_system') || false;
			this.dynamicTheme = uni.getStorageSync('theme_dynamic') || false;
		},
		
		/**
		 * 设置主题监听器
		 */
		setupThemeListener() {
			ThemeManager.addListener(this.onThemeChange);
		},
		
		/**
		 * 主题变化回调
		 */
		onThemeChange(theme) {
			this.currentTheme = theme;
			this.themeColors = theme.colors;
		},
		
		/**
		 * 选择主题
		 */
		selectTheme(theme) {
			ThemeManager.setTheme(theme.key);
			uni.showToast({
				title: `已切换为${theme.name}`,
				icon: 'success'
			});
		},
		
		/**
		 * 获取主题颜色用于预览
		 */
		getThemeColors(theme) {
			return [
				theme.colors.primary,
				theme.colors.success,
				theme.colors.warning,
				theme.colors.error
			];
		},
		
		/**
		 * 跟随系统设置变化
		 */
		onFollowSystemChange(e) {
			this.followSystem = e.detail.value;
			uni.setStorageSync('theme_follow_system', this.followSystem);
			
			if (this.followSystem) {
				this.checkSystemTheme();
			}
			
			uni.showToast({
				title: this.followSystem ? '已开启跟随系统' : '已关闭跟随系统',
				icon: 'success'
			});
		},
		
		/**
		 * 动态主题设置变化
		 */
		onDynamicThemeChange(e) {
			this.dynamicTheme = e.detail.value;
			uni.setStorageSync('theme_dynamic', this.dynamicTheme);
			
			if (this.dynamicTheme) {
				this.startDynamicTheme();
			} else {
				this.stopDynamicTheme();
			}
			
			uni.showToast({
				title: this.dynamicTheme ? '已开启动态主题' : '已关闭动态主题',
				icon: 'success'
			});
		},
		
		/**
		 * 检查系统主题
		 */
		checkSystemTheme() {
			// #ifdef APP-PLUS
			try {
				const systemInfo = uni.getSystemInfoSync();
				if (systemInfo.theme === 'dark') {
					ThemeManager.setTheme('dark');
				} else {
					ThemeManager.setTheme('light');
				}
			} catch (error) {
				console.warn('获取系统主题失败:', error);
			}
			// #endif
		},
		
		/**
		 * 开始动态主题
		 */
		startDynamicTheme() {
			const hour = new Date().getHours();
			let themeKey = 'light';
			
			if (hour >= 6 && hour < 12) {
				themeKey = 'warm'; // 早晨 - 温暖橙
			} else if (hour >= 12 && hour < 18) {
				themeKey = 'light'; // 下午 - 清新蓝
			} else if (hour >= 18 && hour < 22) {
				themeKey = 'purple'; // 傍晚 - 优雅紫
			} else {
				themeKey = 'dark'; // 夜晚 - 深色模式
			}
			
			ThemeManager.setTheme(themeKey);
		},
		
		/**
		 * 停止动态主题
		 */
		stopDynamicTheme() {
			// 恢复到用户选择的主题
		},
		
		/**
		 * 切换到浅色主题
		 */
		switchToLight() {
			ThemeManager.setTheme('light');
		},
		
		/**
		 * 切换到深色主题
		 */
		switchToDark() {
			ThemeManager.setTheme('dark');
		},
		
		/**
		 * 随机切换主题
		 */
		switchRandom() {
			const themes = Object.keys(this.themeList);
			const randomIndex = Math.floor(Math.random() * themes.length);
			const randomTheme = this.themeList[randomIndex];
			
			this.selectTheme(randomTheme);
		},
		
		/**
		 * 打开主题测试页面
		 */
		openThemeTest() {
			uni.navigateTo({
				url: '/pages/settings/theme-test'
			});
		}
	}
};
</script>

<style scoped>
.page {
	min-height: 100vh;
}

.header {
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
}

.header-title {
	color: #ffffff;
	font-size: 36rpx;
	font-weight: bold;
}

.current-theme {
	margin: 40rpx 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.preview-card {
	padding: 40rpx;
	text-align: center;
}

.preview-title {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	display: block;
	margin-bottom: 10rpx;
}

.preview-name {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
}

.theme-section {
	margin: 40rpx 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
}

.theme-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.theme-item {
	border-radius: 20rpx;
	padding: 20rpx;
	border: 2rpx solid;
	position: relative;
	transition: all 0.3s ease;
}

.theme-item.active {
	transform: scale(1.02);
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.theme-preview {
	border-radius: 12rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
	height: 120rpx;
}

.preview-header {
	height: 40rpx;
}

.preview-body {
	height: 80rpx;
	padding: 10rpx;
	display: flex;
	gap: 8rpx;
}

.preview-item {
	flex: 1;
	border-radius: 6rpx;
}

.theme-info {
	text-align: center;
}

.theme-name {
	font-size: 26rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.theme-colors {
	display: flex;
	justify-content: center;
	gap: 8rpx;
}

.color-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.selected-icon {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #48bb78;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.selected-icon .icon {
	color: #ffffff;
	font-size: 20rpx;
	font-weight: bold;
}

.settings-section {
	margin: 40rpx 30rpx;
}

.setting-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
}

.item-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.item-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.item-content {
	flex: 1;
}

.item-title {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
	display: block;
}

.item-desc {
	font-size: 24rpx;
	line-height: 1.4;
}

.presets-section {
	margin: 40rpx 30rpx 60rpx;
}

.preset-buttons {
	display: flex;
	gap: 20rpx;
}

.preset-btn {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	border: 2rpx solid;
	text-align: center;
	transition: all 0.3s ease;
}

.preset-btn:active {
	transform: scale(0.98);
}

.preset-icon {
	font-size: 32rpx;
	margin-bottom: 10rpx;
	display: block;
}

.preset-text {
	font-size: 24rpx;
	font-weight: bold;
}
</style>
