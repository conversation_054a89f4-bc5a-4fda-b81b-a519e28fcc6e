
.page[data-v-1cf27b2a] {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 6.25rem;
}

/* 头部样式 */
.header[data-v-1cf27b2a] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.875rem 1.25rem 1.25rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-title[data-v-1cf27b2a] {
  font-size: 1.375rem;
  font-weight: bold;
  flex: 1;
  text-align: center;
}
.help-btn[data-v-1cf27b2a] {
  font-size: 1.125rem;
  padding: 0.3125rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  width: 1.75rem;
  height: 1.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.help-btn[data-v-1cf27b2a]:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 统计卡片 */
.stats-card[data-v-1cf27b2a] {
  background: white;
  margin: -1.25rem 1.25rem 1.25rem;
  border-radius: 0.625rem;
  padding: 1.25rem;
  display: flex;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.stat-item[data-v-1cf27b2a] {
  flex: 1;
  text-align: center;
}
.stat-label[data-v-1cf27b2a] {
  font-size: 0.875rem;
  color: #666;
  display: block;
  margin-bottom: 0.3125rem;
}
.stat-value[data-v-1cf27b2a] {
  font-size: 1.125rem;
  font-weight: bold;
  display: block;
}
.stat-value.expense[data-v-1cf27b2a] {
  color: #ff4757;
}
.stat-value.income[data-v-1cf27b2a] {
  color: #2ed573;
}
.stat-divider[data-v-1cf27b2a] {
  width: 0.0625rem;
  background-color: #eee;
  margin: 0 0.9375rem;
}

/* 快速操作 */
.quick-actions[data-v-1cf27b2a] {
  margin: 0 1.25rem 1.25rem;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.375rem;
}
.action-item[data-v-1cf27b2a] {
  background: white;
  border-radius: 0.5rem;
  padding: 0.625rem 0.25rem;
  text-align: center;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}
.action-item[data-v-1cf27b2a]:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.action-icon[data-v-1cf27b2a] {
  font-size: 1rem;
  display: block;
  margin-bottom: 0.1875rem;
}
.action-text[data-v-1cf27b2a] {
  font-size: 0.625rem;
  color: #333;
  font-weight: 500;
}

/* 账户总览 */
.accounts-overview[data-v-1cf27b2a] {
  margin: 0 1.25rem 1.25rem;
}
.accounts-summary[data-v-1cf27b2a] {
  background: white;
  border-radius: 0.625rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.summary-item[data-v-1cf27b2a] {
  text-align: center;
}
.summary-label[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: #666;
  display: block;
  margin-bottom: 0.25rem;
}
.summary-value[data-v-1cf27b2a] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.accounts-list[data-v-1cf27b2a] {
  background: white;
  border-radius: 0.625rem;
  padding: 0.625rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.account-item[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
  transition: background-color 0.2s;
}
.account-item[data-v-1cf27b2a]:last-child {
  border-bottom: none;
}
.account-item[data-v-1cf27b2a]:active {
  background-color: #f8f9fa;
}
.account-icon[data-v-1cf27b2a] {
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.625rem;
}
.icon-text[data-v-1cf27b2a] {
  font-size: 0.875rem;
}
.account-info[data-v-1cf27b2a] {
  flex: 1;
}
.account-name[data-v-1cf27b2a] {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 0.125rem;
}
.account-balance[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: #666;
}

/* 记录区域 */
.records-section[data-v-1cf27b2a] {
  margin: 0 1.25rem;
}
.section-header[data-v-1cf27b2a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.section-title[data-v-1cf27b2a] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.view-all[data-v-1cf27b2a] {
  font-size: 0.875rem;
  color: #667eea;
}
.empty[data-v-1cf27b2a] {
  background: white;
  border-radius: 0.625rem;
  padding: 2.5rem 1.25rem;
  text-align: center;
}
.empty-text[data-v-1cf27b2a] {
  color: #999;
  font-size: 0.875rem;
}

/* 记录列表 */
.records-list[data-v-1cf27b2a] {
  background: white;
  border-radius: 0.625rem;
  overflow: hidden;
}
.record-item[data-v-1cf27b2a] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.9375rem 1.25rem;
  border-bottom: 0.03125rem solid #f0f0f0;
  transition: background-color 0.2s;
}
.record-item[data-v-1cf27b2a]:last-child {
  border-bottom: none;
}
.record-item[data-v-1cf27b2a]:active {
  background-color: #f8f9fa;
}
.record-left[data-v-1cf27b2a] {
  flex: 1;
}
.record-category[data-v-1cf27b2a] {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.category-icon[data-v-1cf27b2a] {
  font-size: 1rem;
  margin-right: 0.5rem;
}
.category-name[data-v-1cf27b2a] {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}
.record-note[data-v-1cf27b2a] {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.25rem;
  display: block;
}
.record-time[data-v-1cf27b2a] {
  font-size: 0.75rem;
  color: #999;
}
.record-right[data-v-1cf27b2a] {
  text-align: right;
}
.record-amount[data-v-1cf27b2a] {
  font-size: 1rem;
  font-weight: bold;
}
.record-amount.expense[data-v-1cf27b2a] {
  color: #ff4757;
}
.record-amount.income[data-v-1cf27b2a] {
  color: #2ed573;
}

/* 悬浮按钮 */
.fab[data-v-1cf27b2a] {
  position: fixed;
  bottom: 3.75rem;
  right: 1.25rem;
  width: 3.75rem;
  height: 3.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.25rem 0.75rem rgba(102, 126, 234, 0.4);
  transition: transform 0.2s;
}
.fab[data-v-1cf27b2a]:active {
  transform: scale(0.95);
}
.fab-icon[data-v-1cf27b2a] {
  color: white;
  font-size: 1.5rem;
  font-weight: 300;
  line-height: 1;
}
