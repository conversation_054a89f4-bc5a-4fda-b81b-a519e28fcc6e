<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">搜索记录</text>
			</view>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input" 
					v-model="searchKeyword" 
					placeholder="搜索备注、分类名称..."
					@input="onSearchInput"
					focus
				/>
				<text class="clear-btn" v-if="searchKeyword" @click="clearSearch">×</text>
			</view>
		</view>
		
		<!-- 筛选条件 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{active: filterType === 'all'}"
					@click="setFilterType('all')"
				>
					<text class="tab-text">全部</text>
				</view>
				<view 
					class="filter-tab" 
					:class="{active: filterType === 'expense'}"
					@click="setFilterType('expense')"
				>
					<text class="tab-text">支出</text>
				</view>
				<view 
					class="filter-tab" 
					:class="{active: filterType === 'income'}"
					@click="setFilterType('income')"
				>
					<text class="tab-text">收入</text>
				</view>
			</view>
		</view>
		
		<!-- 搜索结果 -->
		<view class="results-section">
			<view v-if="!searchKeyword && filteredRecords.length === 0" class="empty-state">
				<text class="empty-icon">🔍</text>
				<text class="empty-text">输入关键词搜索记录</text>
			</view>
			
			<view v-else-if="filteredRecords.length === 0" class="empty-state">
				<text class="empty-icon">😔</text>
				<text class="empty-text">没有找到相关记录</text>
			</view>
			
			<view v-else class="results-list">
				<view class="results-header">
					<text class="results-count">找到 {{filteredRecords.length}} 条记录</text>
				</view>
				
				<view class="records-list">
					<view 
						v-for="record in filteredRecords" 
						:key="record.id" 
						class="record-item" 
						@click="viewRecord(record)"
					>
						<view class="record-left">
							<view class="record-category">
								<text class="category-icon">{{getCategoryIcon(record.categoryId)}}</text>
								<text class="category-name">{{getCategoryName(record.categoryId)}}</text>
							</view>
							<text class="record-note" v-if="record.note">{{record.note}}</text>
							<text class="record-time">{{formatTime(record.date)}}</text>
						</view>
						<view class="record-right">
							<text class="record-amount" :class="record.type === 'expense' ? 'expense' : 'income'">
								{{record.type === 'expense' ? '-' : '+'}}¥{{record.amount.toFixed(2)}}
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			searchKeyword: '',
			filterType: 'all',
			records: [],
			categories: []
		};
	},
	computed: {
		filteredRecords() {
			let results = this.records;
			
			// 按类型筛选
			if (this.filterType !== 'all') {
				results = results.filter(record => record.type === this.filterType);
			}
			
			// 按关键词搜索
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.toLowerCase();
				results = results.filter(record => {
					// 搜索备注
					const noteMatch = record.note && record.note.toLowerCase().includes(keyword);
					// 搜索分类名称
					const categoryName = this.getCategoryName(record.categoryId).toLowerCase();
					const categoryMatch = categoryName.includes(keyword);
					// 搜索金额
					const amountMatch = record.amount.toString().includes(keyword);
					
					return noteMatch || categoryMatch || amountMatch;
				});
			}
			
			// 按时间倒序排列
			return results.sort((a, b) => b.date - a.date);
		}
	},
	onLoad() {
		this.loadData();
	},
	methods: {
		loadData() {
			this.records = StorageManager.getRecords();
			this.categories = this.getDefaultCategories();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		onSearchInput() {
			// 实时搜索，可以添加防抖优化
		},
		
		clearSearch() {
			this.searchKeyword = '';
		},
		
		setFilterType(type) {
			this.filterType = type;
		},
		
		viewRecord(record) {
			uni.navigateTo({ 
				url: `/pages/add-record/add-record?id=${record.id}&mode=view` 
			});
		},
		
		getCategoryName(categoryId) {
			const category = this.categories.find(c => c.id === categoryId);
			return category ? category.name : '未知分类';
		},
		
		getCategoryIcon(categoryId) {
			const category = this.categories.find(c => c.id === categoryId);
			return category ? category.icon : '💰';
		},
		
		formatTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();
			const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
			const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
			
			if (date >= today) {
				return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
			} else if (date >= yesterday) {
				return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
			}
		},
		
		getDefaultCategories() {
			return [
				// 支出分类
				{ id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily' },
				{ id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily' },
				{ id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily' },
				{ id: 'entertainment', name: '娱乐', icon: '🎬', type: 'expense', groupId: 'daily' },
				{ id: 'healthcare', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily' },
				{ id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily' },
				{ id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily' },
				{ id: 'utilities', name: '水电费', icon: '💡', type: 'expense', groupId: 'daily' },
				{ id: 'other_expense', name: '其他支出', icon: '💸', type: 'expense', groupId: 'daily' },
				
				// 收入分类
				{ id: 'salary', name: '工资', icon: '💰', type: 'income', groupId: 'daily' },
				{ id: 'bonus', name: '奖金', icon: '🎁', type: 'income', groupId: 'daily' },
				{ id: 'investment', name: '投资', icon: '📈', type: 'income', groupId: 'daily' },
				{ id: 'other_income', name: '其他收入', icon: '💵', type: 'income', groupId: 'daily' },
				
				// 报销分类
				{ id: 'business_meal', name: '商务餐饮', icon: '🍽️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'business_transport', name: '差旅交通', icon: '✈️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'accommodation', name: '住宿费', icon: '🏨', type: 'expense', groupId: 'reimbursement' },
				{ id: 'office_supplies', name: '办公用品', icon: '📎', type: 'expense', groupId: 'reimbursement' },
				{ id: 'communication', name: '通讯费', icon: '📱', type: 'expense', groupId: 'reimbursement' },
				{ id: 'training', name: '培训费', icon: '🎓', type: 'expense', groupId: 'reimbursement' },
				{ id: 'entertainment_business', name: '商务招待', icon: '🍷', type: 'expense', groupId: 'reimbursement' },
				{ id: 'other_reimbursement', name: '其他报销', icon: '📋', type: 'expense', groupId: 'reimbursement' }
			];
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 搜索框 */
.search-section {
	padding: 20rpx 40rpx;
}

.search-box {
	background: white;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
	color: #999;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	border: none;
	outline: none;
}

.clear-btn {
	font-size: 40rpx;
	color: #ccc;
	margin-left: 20rpx;
	font-weight: bold;
}

/* 筛选条件 */
.filter-section {
	padding: 0 40rpx 20rpx;
}

.filter-tabs {
	background: white;
	border-radius: 20rpx;
	display: flex;
	padding: 8rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.filter-tab {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 16rpx;
	transition: all 0.3s;
}

.filter-tab.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 搜索结果 */
.results-section {
	flex: 1;
	padding: 0 40rpx;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.results-header {
	padding: 20rpx 0;
}

.results-count {
	font-size: 26rpx;
	color: #666;
}

/* 记录列表 */
.records-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.record-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.record-item:last-child {
	border-bottom: none;
}

.record-item:active {
	background-color: #f8f9fa;
}

.record-left {
	flex: 1;
}

.record-category {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.category-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.category-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.record-note {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.record-time {
	font-size: 24rpx;
	color: #999;
}

.record-right {
	text-align: right;
}

.record-amount {
	font-size: 32rpx;
	font-weight: bold;
}

.record-amount.expense {
	color: #ff4757;
}

.record-amount.income {
	color: #2ed573;
}
</style>
