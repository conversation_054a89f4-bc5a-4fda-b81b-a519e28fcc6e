
.page[data-v-32f99cfa] {
	background-color: #f5f5f5;
	min-height: 100vh;
}
.header[data-v-32f99cfa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-content[data-v-32f99cfa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-32f99cfa], .header-action[data-v-32f99cfa] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-32f99cfa] {
	font-size: 1.125rem;
	font-weight: bold;
}
.upcoming-section[data-v-32f99cfa], .all-reminders-section[data-v-32f99cfa] {
	margin: 1.25rem;
}
.upcoming-section[data-v-32f99cfa] {
	margin-top: -0.625rem;
}
.section-header[data-v-32f99cfa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.9375rem;
}
.section-title[data-v-32f99cfa] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.filter-tabs[data-v-32f99cfa] {
	display: flex;
	gap: 0.625rem;
}
.filter-tab[data-v-32f99cfa] {
	font-size: 0.8125rem;
	color: #666;
	padding: 0.375rem 0.75rem;
	background: white;
	border-radius: 0.625rem;
	transition: all 0.3s;
}
.filter-tab.active[data-v-32f99cfa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.reminders-list[data-v-32f99cfa] {
	display: flex;
	flex-direction: column;
	gap: 0.625rem;
}
.reminder-item[data-v-32f99cfa] {
	background: white;
	border-radius: 0.625rem;
	padding: 0.9375rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
	transition: all 0.3s;
}
.reminder-item.urgent[data-v-32f99cfa] {
	border-left: 0.25rem solid #FF5722;
}
.reminder-item.disabled[data-v-32f99cfa] {
	opacity: 0.5;
}
.reminder-item[data-v-32f99cfa]:active {
	transform: translateY(0.0625rem);
}
.reminder-left[data-v-32f99cfa] {
	display: flex;
	align-items: center;
	flex: 1;
}
.reminder-icon[data-v-32f99cfa] {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 1.25rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.75rem;
}
.icon-text[data-v-32f99cfa] {
	font-size: 1rem;
}
.reminder-info[data-v-32f99cfa] {
	flex: 1;
}
.reminder-name[data-v-32f99cfa] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.reminder-amount[data-v-32f99cfa] {
	font-size: 0.875rem;
	color: #FF9800;
	font-weight: bold;
	display: block;
	margin-bottom: 0.25rem;
}
.reminder-date[data-v-32f99cfa], .reminder-cycle[data-v-32f99cfa], .reminder-next[data-v-32f99cfa] {
	font-size: 0.75rem;
	color: #666;
	display: block;
	margin-bottom: 0.125rem;
}
.reminder-right[data-v-32f99cfa] {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 0.375rem;
}
.days-left[data-v-32f99cfa] {
	font-size: 0.75rem;
	color: #FF5722;
	font-weight: bold;
}
.complete-btn[data-v-32f99cfa] {
	background: #4CAF50;
	color: white;
	padding: 0.375rem 0.75rem;
	border-radius: 0.5rem;
	font-size: 0.6875rem;
	font-weight: bold;
}
.empty-state[data-v-32f99cfa] {
	background: white;
	border-radius: 0.625rem;
	padding: 3.125rem 1.25rem;
	text-align: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.empty-icon[data-v-32f99cfa] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-32f99cfa] {
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 1.25rem;
}
.add-reminder-btn[data-v-32f99cfa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 1.5625rem;
	padding: 0.75rem 1.5rem;
	font-size: 0.875rem;
	font-weight: bold;
}
.popup-overlay[data-v-32f99cfa] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.popup-content[data-v-32f99cfa] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	width: 100%;
	max-height: 90vh;
	display: flex;
	flex-direction: column;
}
.popup-header[data-v-32f99cfa] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-32f99cfa] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-32f99cfa] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-scroll[data-v-32f99cfa] {
	flex: 1;
	max-height: 60vh;
}
.form-section[data-v-32f99cfa] {
	padding-bottom: 1.25rem;
}
.form-item[data-v-32f99cfa] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-32f99cfa] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-input[data-v-32f99cfa] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
}
.type-selector[data-v-32f99cfa] {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.5rem;
}
.type-item[data-v-32f99cfa] {
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	text-align: center;
	transition: all 0.3s;
}
.type-item.active[data-v-32f99cfa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.type-icon[data-v-32f99cfa] {
	font-size: 0.875rem;
	display: block;
	margin-bottom: 0.25rem;
}
.type-name[data-v-32f99cfa] {
	font-size: 0.6875rem;
}
.cycle-picker[data-v-32f99cfa], .date-picker[data-v-32f99cfa], .advance-picker[data-v-32f99cfa] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
}
.picker-text[data-v-32f99cfa] {
	font-size: 0.875rem;
	color: #333;
}
.color-selector[data-v-32f99cfa] {
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
}
.color-item[data-v-32f99cfa] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	border: 0.125rem solid transparent;
	transition: all 0.3s;
}
.color-item.active[data-v-32f99cfa] {
	border-color: #333;
	transform: scale(1.1);
}
.popup-actions[data-v-32f99cfa] {
	display: flex;
	gap: 0.625rem;
	margin-top: 0.625rem;
}
.cancel-btn[data-v-32f99cfa], .save-btn[data-v-32f99cfa] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.cancel-btn[data-v-32f99cfa] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-32f99cfa] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
