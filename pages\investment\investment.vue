<template>
  <view class="investment-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-content">
        <text class="title">投资追踪</text>
        <view class="add-btn" @click="showAddDialog = true">
          <text class="add-icon">+</text>
        </view>
      </view>
    </view>

    <!-- 投资概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-item">
          <text class="overview-label">总投资</text>
          <text class="overview-value">¥{{totalInvestment.toFixed(2)}}</text>
        </view>
        <view class="overview-item">
          <text class="overview-label">当前市值</text>
          <text class="overview-value">¥{{currentValue.toFixed(2)}}</text>
        </view>
        <view class="overview-item">
          <text class="overview-label">总收益</text>
          <text class="overview-value" :class="{ profit: totalProfit >= 0, loss: totalProfit < 0 }">
            {{totalProfit >= 0 ? '+' : ''}}¥{{totalProfit.toFixed(2)}}
          </text>
        </view>
        <view class="overview-item">
          <text class="overview-label">收益率</text>
          <text class="overview-value" :class="{ profit: totalProfitRate >= 0, loss: totalProfitRate < 0 }">
            {{totalProfitRate >= 0 ? '+' : ''}}{{totalProfitRate.toFixed(2)}}%
          </text>
        </view>
      </view>
    </view>

    <!-- 投资类型筛选 -->
    <view class="filter-section">
      <scroll-view class="filter-scroll" scroll-x="true">
        <view class="filter-item" 
              v-for="type in investmentTypes" 
              :key="type.id"
              @click="selectedType = selectedType === type.id ? '' : type.id"
              :class="{ active: selectedType === type.id }">
          <text class="filter-icon">{{type.icon}}</text>
          <text class="filter-name">{{type.name}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 投资列表 -->
    <view class="investment-list">
      <view class="investment-item" 
            v-for="investment in filteredInvestments" 
            :key="investment.id"
            @click="viewInvestment(investment)">
        <view class="item-header">
          <view class="item-info">
            <text class="item-name">{{investment.name}}</text>
            <text class="item-symbol" v-if="investment.symbol">{{investment.symbol}}</text>
          </view>
          <view class="item-type">
            <text class="type-icon">{{getTypeIcon(investment.type)}}</text>
          </view>
        </view>
        
        <view class="item-details">
          <view class="detail-row">
            <text class="detail-label">持有数量</text>
            <text class="detail-value">{{investment.quantity}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">买入价格</text>
            <text class="detail-value">¥{{investment.buyPrice.toFixed(2)}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">当前价格</text>
            <text class="detail-value">¥{{investment.currentPrice.toFixed(2)}}</text>
          </view>
        </view>
        
        <view class="item-profit">
          <view class="profit-info">
            <text class="profit-label">总成本</text>
            <text class="profit-value">¥{{(investment.quantity * investment.buyPrice).toFixed(2)}}</text>
          </view>
          <view class="profit-info">
            <text class="profit-label">当前市值</text>
            <text class="profit-value">¥{{(investment.quantity * investment.currentPrice).toFixed(2)}}</text>
          </view>
          <view class="profit-info">
            <text class="profit-label">收益</text>
            <text class="profit-value" :class="{ profit: getProfit(investment) >= 0, loss: getProfit(investment) < 0 }">
              {{getProfit(investment) >= 0 ? '+' : ''}}¥{{getProfit(investment).toFixed(2)}}
            </text>
          </view>
          <view class="profit-info">
            <text class="profit-label">收益率</text>
            <text class="profit-value" :class="{ profit: getProfitRate(investment) >= 0, loss: getProfitRate(investment) < 0 }">
              {{getProfitRate(investment) >= 0 ? '+' : ''}}{{getProfitRate(investment).toFixed(2)}}%
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加投资对话框 -->
    <view class="modal" v-if="showAddDialog" @click="closeAddDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{editingInvestment ? '编辑投资' : '添加投资'}}</text>
          <text class="modal-close" @click="closeAddDialog">×</text>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">投资名称</text>
            <input class="form-input" v-model="investmentForm.name" placeholder="请输入投资名称" />
          </view>
          
          <view class="form-group">
            <text class="form-label">投资类型</text>
            <view class="type-options">
              <view class="type-option" 
                    v-for="type in investmentTypes" 
                    :key="type.id"
                    @click="investmentForm.type = type.id"
                    :class="{ selected: investmentForm.type === type.id }">
                <text class="type-icon">{{type.icon}}</text>
                <text class="type-name">{{type.name}}</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">代码/符号</text>
            <input class="form-input" v-model="investmentForm.symbol" placeholder="如：000001、AAPL等" />
          </view>
          
          <view class="form-group">
            <text class="form-label">持有数量</text>
            <input class="form-input" v-model="investmentForm.quantity" type="number" placeholder="请输入持有数量" />
          </view>
          
          <view class="form-group">
            <text class="form-label">买入价格</text>
            <input class="form-input" v-model="investmentForm.buyPrice" type="digit" placeholder="请输入买入价格" />
          </view>
          
          <view class="form-group">
            <text class="form-label">当前价格</text>
            <input class="form-input" v-model="investmentForm.currentPrice" type="digit" placeholder="请输入当前价格" />
          </view>
          
          <view class="form-group">
            <text class="form-label">关联账户</text>
            <picker @change="onAccountChange" :value="accountIndex" :range="accountNames">
              <view class="picker-view">
                <text class="picker-text">{{selectedAccountName || '请选择账户'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">备注</text>
            <textarea class="form-textarea" v-model="investmentForm.note" placeholder="请输入备注信息"></textarea>
          </view>
        </view>
        <view class="modal-footer">
          <view class="btn btn-cancel" @click="closeAddDialog">取消</view>
          <view class="btn btn-primary" @click="saveInvestment">{{editingInvestment ? '保存' : '添加'}}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import { createInvestment, validateInvestment } from '@/utils/advancedModels.js';

export default {
  data() {
    return {
      investments: [],
      accounts: [],
      selectedType: '',
      showAddDialog: false,
      editingInvestment: null,
      investmentForm: {
        name: '',
        type: 'stock',
        symbol: '',
        quantity: '',
        buyPrice: '',
        currentPrice: '',
        accountId: '',
        note: ''
      },
      investmentTypes: [],
      accountIndex: 0
    };
  },
  
  computed: {
    filteredInvestments() {
      if (!this.selectedType) return this.investments;
      return this.investments.filter(investment => investment.type === this.selectedType);
    },
    
    totalInvestment() {
      return this.investments.reduce((total, investment) => {
        return total + (investment.quantity * investment.buyPrice);
      }, 0);
    },
    
    currentValue() {
      return this.investments.reduce((total, investment) => {
        return total + (investment.quantity * investment.currentPrice);
      }, 0);
    },
    
    totalProfit() {
      return this.currentValue - this.totalInvestment;
    },
    
    totalProfitRate() {
      if (this.totalInvestment === 0) return 0;
      return (this.totalProfit / this.totalInvestment) * 100;
    },
    
    accountNames() {
      return this.accounts.map(account => account.name);
    },
    
    selectedAccountName() {
      if (this.investmentForm.accountId) {
        const account = this.accounts.find(acc => acc.id === this.investmentForm.accountId);
        return account ? account.name : '';
      }
      return '';
    }
  },
  
  onLoad() {
    this.loadData();
  },
  
  methods: {
    loadData() {
      this.investments = StorageManager.getInvestments().filter(investment => !investment.deleted);
      this.accounts = StorageManager.getAccounts().filter(account => !account.deleted);
      this.investmentTypes = StorageManager.getDefaultInvestmentTypes();
    },
    
    closeAddDialog() {
      this.showAddDialog = false;
      this.editingInvestment = null;
    },
    
    onAccountChange(e) {
      this.accountIndex = e.detail.value;
      this.investmentForm.accountId = this.accounts[this.accountIndex]?.id || '';
    },
    
    saveInvestment() {
      // 验证表单
      if (!this.investmentForm.name.trim()) {
        uni.showToast({
          title: '请输入投资名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.investmentForm.quantity || this.investmentForm.quantity <= 0) {
        uni.showToast({
          title: '请输入有效的持有数量',
          icon: 'none'
        });
        return;
      }
      
      if (!this.investmentForm.buyPrice || this.investmentForm.buyPrice <= 0) {
        uni.showToast({
          title: '请输入有效的买入价格',
          icon: 'none'
        });
        return;
      }
      
      if (!this.investmentForm.currentPrice || this.investmentForm.currentPrice <= 0) {
        uni.showToast({
          title: '请输入有效的当前价格',
          icon: 'none'
        });
        return;
      }
      
      if (this.editingInvestment) {
        // 编辑投资
        const index = this.investments.findIndex(investment => investment.id === this.editingInvestment.id);
        if (index !== -1) {
          this.investments[index] = {
            ...this.investments[index],
            name: this.investmentForm.name,
            type: this.investmentForm.type,
            symbol: this.investmentForm.symbol,
            quantity: Number(this.investmentForm.quantity),
            buyPrice: Number(this.investmentForm.buyPrice),
            currentPrice: Number(this.investmentForm.currentPrice),
            accountId: this.investmentForm.accountId,
            note: this.investmentForm.note,
            updatedAt: Date.now()
          };
        }
      } else {
        // 添加新投资
        const newInvestment = createInvestment({
          name: this.investmentForm.name,
          type: this.investmentForm.type,
          symbol: this.investmentForm.symbol,
          quantity: Number(this.investmentForm.quantity),
          buyPrice: Number(this.investmentForm.buyPrice),
          currentPrice: Number(this.investmentForm.currentPrice),
          accountId: this.investmentForm.accountId,
          note: this.investmentForm.note
        });
        
        this.investments.unshift(newInvestment);
      }
      
      StorageManager.saveInvestments(this.investments);
      this.closeAddDialog();
      
      uni.showToast({
        title: this.editingInvestment ? '投资已更新' : '投资已添加',
        icon: 'success'
      });
    },
    
    viewInvestment(investment) {
      // 简化版详情显示
      uni.showModal({
        title: investment.name,
        content: `持有数量: ${investment.quantity}\n买入价格: ¥${investment.buyPrice.toFixed(2)}\n当前价格: ¥${investment.currentPrice.toFixed(2)}\n收益: ${this.getProfit(investment) >= 0 ? '+' : ''}¥${this.getProfit(investment).toFixed(2)}`,
        showCancel: true,
        cancelText: '编辑',
        confirmText: '关闭',
        success: (res) => {
          if (res.cancel) {
            this.editInvestment(investment);
          }
        }
      });
    },
    
    editInvestment(investment) {
      this.editingInvestment = investment;
      this.investmentForm = {
        name: investment.name,
        type: investment.type,
        symbol: investment.symbol,
        quantity: investment.quantity.toString(),
        buyPrice: investment.buyPrice.toString(),
        currentPrice: investment.currentPrice.toString(),
        accountId: investment.accountId,
        note: investment.note
      };
      
      // 设置账户选择器
      const accountIndex = this.accounts.findIndex(acc => acc.id === investment.accountId);
      this.accountIndex = accountIndex >= 0 ? accountIndex : 0;
      
      this.showAddDialog = true;
    },
    
    getProfit(investment) {
      const totalCost = investment.quantity * investment.buyPrice;
      const currentValue = investment.quantity * investment.currentPrice;
      return currentValue - totalCost;
    },
    
    getProfitRate(investment) {
      const totalCost = investment.quantity * investment.buyPrice;
      if (totalCost === 0) return 0;
      const profit = this.getProfit(investment);
      return (profit / totalCost) * 100;
    },
    
    getTypeIcon(type) {
      const typeObj = this.investmentTypes.find(t => t.id === type);
      return typeObj ? typeObj.icon : '💎';
    }
  }
};
</script>

<style scoped>
.investment-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  padding: 44px 20px 20px;
  background: rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: white;
}

.add-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.overview-section {
  margin: 20px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.overview-item {
  flex: 1;
  min-width: 120px;
  text-align: center;
}

.overview-label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.overview-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.overview-value.profit {
  color: #4CAF50;
}

.overview-value.loss {
  color: #F44336;
}

.filter-section {
  margin: 20px;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  margin-right: 12px;
  text-align: center;
  min-width: 80px;
}

.filter-item.active {
  background: white;
}

.filter-icon {
  display: block;
  font-size: 20px;
  margin-bottom: 4px;
}

.filter-name {
  display: block;
  font-size: 12px;
  color: white;
}

.filter-item.active .filter-name {
  color: #333;
}

.investment-list {
  margin: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.investment-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.item-symbol {
  font-size: 12px;
  color: #999;
}

.type-icon {
  font-size: 24px;
}

.item-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row {
  flex: 1;
  min-width: 100px;
}

.detail-label {
  display: block;
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}

.detail-value {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.item-profit {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.profit-info {
  flex: 1;
  min-width: 100px;
}

.profit-label {
  display: block;
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}

.profit-value {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.profit-value.profit {
  color: #4CAF50;
}

.profit-value.loss {
  color: #F44336;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
}

.type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.type-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}

.type-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.type-icon {
  margin-right: 4px;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.picker-text {
  color: #333;
}

.picker-arrow {
  color: #999;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
}

.btn-cancel {
  background: #f0f0f0;
  color: #666;
}

.btn-primary {
  background: #667eea;
  color: white;
}
</style>
