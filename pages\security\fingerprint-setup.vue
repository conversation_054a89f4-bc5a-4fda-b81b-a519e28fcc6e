<template>
  <view class="fingerprint-setup">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="iconfont icon-back">‹</text>
        </view>
        <view class="navbar-title">指纹解锁</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <view class="content">
      <!-- 指纹图标 -->
      <view class="fingerprint-icon">
        <text class="icon">👆</text>
      </view>

      <!-- 标题和描述 -->
      <view class="title">启用指纹解锁</view>
      <view class="description">
        使用指纹快速解锁应用，保护您的财务数据安全
      </view>

      <!-- 状态信息 -->
      <view class="status-info">
        <view class="status-item">
          <text class="status-label">PIN码状态：</text>
          <text class="status-value" :class="{ success: hasPinCode, error: !hasPinCode }">
            {{ hasPinCode ? '已设置' : '未设置' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">设备支持：</text>
          <text class="status-value" :class="{ success: deviceSupported, error: !deviceSupported }">
            {{ deviceSupported ? '支持' : '不支持' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">指纹录入：</text>
          <text class="status-value" :class="{ success: fingerprintEnrolled, error: !fingerprintEnrolled }">
            {{ fingerprintEnrolled ? '已录入' : '未录入' }}
          </text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          v-if="!hasPinCode"
          class="btn btn-primary"
          @click="goToPinSetup"
        >
          先设置PIN码
        </button>
        
        <button 
          v-else-if="!canEnableFingerprint"
          class="btn btn-disabled"
          disabled
        >
          {{ getDisabledReason() }}
        </button>
        
        <button 
          v-else-if="!fingerprintEnabled"
          class="btn btn-primary"
          @click="enableFingerprint"
          :loading="loading"
        >
          启用指纹解锁
        </button>
        
        <button 
          v-else
          class="btn btn-secondary"
          @click="disableFingerprint"
        >
          禁用指纹解锁
        </button>
      </view>

      <!-- 提示信息 -->
      <view class="tips">
        <view class="tip-item">
          <text class="tip-icon">💡</text>
          <text class="tip-text">指纹解锁需要先设置PIN码作为备用解锁方式</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">🔒</text>
          <text class="tip-text">应用会在后台5分钟后自动锁定</text>
        </view>
        <view class="tip-item">
          <text class="tip-icon">⚠️</text>
          <text class="tip-text">请确保您的设备已录入指纹信息</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SecurityManager from '@/utils/securityManager.js';

export default {
  name: 'FingerprintSetup',
  data() {
    return {
      loading: false,
      hasPinCode: false,
      deviceSupported: false,
      fingerprintEnrolled: false,
      fingerprintEnabled: false
    };
  },
  computed: {
    canEnableFingerprint() {
      return this.hasPinCode && this.deviceSupported && this.fingerprintEnrolled;
    }
  },
  async onLoad() {
    await this.checkStatus();
  },
  methods: {
    /**
     * 检查各项状态
     */
    async checkStatus() {
      try {
        uni.showLoading({
          title: '检查中...'
        });

        // 检查PIN码状态
        this.hasPinCode = SecurityManager.hasPinCode();
        
        // 检查指纹解锁状态
        this.fingerprintEnabled = SecurityManager.isFingerprintEnabled();
        
        // 检查设备支持
        this.deviceSupported = await SecurityManager.isFingerprintSupported();
        
        // 检查指纹录入状态
        if (this.deviceSupported) {
          this.fingerprintEnrolled = await SecurityManager.hasEnrolledFingerprint();
        }

        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
        console.error('检查状态失败:', error);
        uni.showToast({
          title: '检查失败',
          icon: 'error'
        });
      }
    },

    /**
     * 启用指纹解锁
     */
    async enableFingerprint() {
      try {
        this.loading = true;
        
        await SecurityManager.enableFingerprint();
        
        this.fingerprintEnabled = true;
        this.loading = false;
        
        uni.showToast({
          title: '指纹解锁已启用',
          icon: 'success'
        });
      } catch (error) {
        this.loading = false;
        console.error('启用指纹解锁失败:', error);
        
        uni.showModal({
          title: '启用失败',
          content: error.message || '启用指纹解锁失败，请检查设备设置',
          showCancel: false
        });
      }
    },

    /**
     * 禁用指纹解锁
     */
    disableFingerprint() {
      uni.showModal({
        title: '确认禁用',
        content: '确定要禁用指纹解锁吗？',
        success: (res) => {
          if (res.confirm) {
            SecurityManager.disableFingerprint();
            this.fingerprintEnabled = false;
            
            uni.showToast({
              title: '指纹解锁已禁用',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 跳转到PIN码设置
     */
    goToPinSetup() {
      uni.navigateTo({
        url: '/pages/security/pin-setup'
      });
    },

    /**
     * 获取禁用原因
     */
    getDisabledReason() {
      if (!this.deviceSupported) {
        return '设备不支持指纹识别';
      }
      if (!this.fingerprintEnrolled) {
        return '请先录入指纹';
      }
      return '无法启用';
    },

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
.fingerprint-setup {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-navbar {
  padding-top: var(--status-bar-height);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.navbar-left {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.icon-back {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  width: 60px;
}

.content {
  padding: 40px 30px;
  text-align: center;
}

.fingerprint-icon {
  margin-bottom: 30px;
}

.icon {
  font-size: 80px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}

.description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  line-height: 1.5;
}

.status-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 16px;
  color: #fff;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
}

.status-value.success {
  color: #4CAF50;
}

.status-value.error {
  color: #FF5722;
}

.action-buttons {
  margin-bottom: 40px;
}

.btn {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #fff;
  color: #667eea;
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.tips {
  text-align: left;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.tip-icon {
  font-size: 16px;
  margin-right: 10px;
  margin-top: 2px;
}

.tip-text {
  flex: 1;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}
</style>
