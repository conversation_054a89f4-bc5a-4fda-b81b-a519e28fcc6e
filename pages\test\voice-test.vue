<template>
	<view class="page">
		<view class="header">
			<text class="title">语音识别测试</text>
		</view>
		
		<view class="test-section">
			<view class="test-item">
				<text class="test-label">浏览器兼容性检查</text>
				<view class="test-result" :class="compatibility.supported ? 'success' : 'error'">
					<text>{{compatibility.supported ? '✓ 支持' : '✗ 不支持'}}</text>
				</view>
			</view>
			
			<view v-if="!compatibility.supported" class="error-details">
				<text class="error-title">不支持的功能：</text>
				<text v-for="issue in compatibility.issues" :key="issue" class="error-item">• {{issue}}</text>
			</view>
			
			<view class="test-item">
				<text class="test-label">API连接测试</text>
				<button class="test-btn" @click="testApiConnection" :disabled="testing">
					{{testing ? '测试中...' : '测试API连接'}}
				</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">录音功能测试</text>
				<button class="test-btn" @click="testRecording" :disabled="testing">
					{{testing ? '测试中...' : '测试录音'}}
				</button>
			</view>
			
			<view v-if="testResults.length > 0" class="results-section">
				<text class="results-title">测试结果</text>
				<view v-for="(result, index) in testResults" :key="index" class="result-item">
					<view class="result-header">
						<text class="result-name">{{result.name}}</text>
						<text class="result-status" :class="result.success ? 'success' : 'error'">
							{{result.success ? '✓' : '✗'}}
						</text>
					</view>
					<text class="result-message">{{result.message}}</text>
					<text v-if="result.details" class="result-details">{{result.details}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import speechRecognitionCompatService from '@/utils/speechRecognitionCompat.js';
import { config } from '@/utils/config.js';

export default {
	data() {
		return {
			compatibility: {
				supported: true,
				issues: []
			},
			testing: false,
			testResults: []
		};
	},
	
	onLoad() {
		this.checkCompatibility();
	},
	
	methods: {
		// 检查兼容性
		checkCompatibility() {
			this.compatibility = speechRecognitionCompatService.checkCompatibility();
		},
		
		// 测试API连接
		async testApiConnection() {
			this.testing = true;
			const testResult = {
				name: 'API连接测试',
				success: false,
				message: '',
				details: ''
			};
			
			try {
				// 创建一个简单的测试请求
				const response = await fetch(config.siliconFlow.apiUrl, {
					method: 'OPTIONS',
					headers: {
						'Authorization': `Bearer ${config.siliconFlow.apiKey}`
					}
				});
				
				testResult.success = true;
				testResult.message = 'API连接正常';
				testResult.details = `状态码: ${response.status}`;
				
			} catch (error) {
				testResult.success = false;
				testResult.message = 'API连接失败';
				testResult.details = error.message;
			}
			
			this.testResults.unshift(testResult);
			this.testing = false;
		},
		
		// 测试录音功能
		async testRecording() {
			this.testing = true;
			const testResult = {
				name: '录音功能测试',
				success: false,
				message: '',
				details: ''
			};
			
			try {
				// 测试获取麦克风权限
				const stream = await navigator.mediaDevices.getUserMedia({ 
					audio: config.speech.audioConstraints 
				});
				
				// 测试MediaRecorder
				const mediaRecorder = new MediaRecorder(stream);
				
				// 停止音频轨道
				stream.getTracks().forEach(track => track.stop());
				
				testResult.success = true;
				testResult.message = '录音功能正常';
				testResult.details = '麦克风权限获取成功，MediaRecorder创建成功';
				
			} catch (error) {
				testResult.success = false;
				testResult.message = '录音功能异常';
				
				if (error.name === 'NotAllowedError') {
					testResult.details = '麦克风权限被拒绝';
				} else if (error.name === 'NotFoundError') {
					testResult.details = '未找到麦克风设备';
				} else {
					testResult.details = error.message;
				}
			}
			
			this.testResults.unshift(testResult);
			this.testing = false;
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 40rpx;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.test-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
	padding-bottom: 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.test-item:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.test-label {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.test-result {
	padding: 16rpx 32rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
}

.test-result.success {
	background: #d4edda;
	color: #155724;
}

.test-result.error {
	background: #f8d7da;
	color: #721c24;
}

.test-btn {
	padding: 20rpx 40rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
}

.test-btn:disabled {
	background: #ccc;
	color: #999;
}

.error-details {
	background: #f8d7da;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 40rpx;
}

.error-title {
	font-size: 28rpx;
	color: #721c24;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.error-item {
	font-size: 26rpx;
	color: #721c24;
	display: block;
	margin-bottom: 10rpx;
}

.results-section {
	margin-top: 60rpx;
	padding-top: 40rpx;
	border-top: 2rpx solid #f0f0f0;
}

.results-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 40rpx;
}

.result-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.result-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.result-status {
	font-size: 32rpx;
	font-weight: bold;
}

.result-status.success {
	color: #28a745;
}

.result-status.error {
	color: #dc3545;
}

.result-message {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.result-details {
	font-size: 24rpx;
	color: #666;
	display: block;
}
</style>
