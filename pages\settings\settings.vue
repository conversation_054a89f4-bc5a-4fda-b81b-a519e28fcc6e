<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="header-title">设置</text>
		</view>
		
		<!-- 设置统计卡片 -->
		<view class="stats-card" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="stat-item">
				<text class="stat-label" :style="{ color: themeColors.textSecondary }">当前主题</text>
				<text class="stat-value" :style="{ color: themeColors.primary }">{{currentThemeName}}</text>
			</view>
			<view class="stat-divider" :style="{ backgroundColor: themeColors.border }"></view>
			<view class="stat-item">
				<text class="stat-label" :style="{ color: themeColors.textSecondary }">安全状态</text>
				<text class="stat-value" :style="{ color: themeColors.success }">已启用</text>
			</view>
			<view class="stat-divider" :style="{ backgroundColor: themeColors.border }"></view>
			<view class="stat-item">
				<text class="stat-label" :style="{ color: themeColors.textSecondary }">数据同步</text>
				<text class="stat-value" :style="{ color: themeColors.info }">正常</text>
			</view>
		</view>
		
		<!-- 数据管理 -->
		<view class="section">
			<view class="section-title">
				<text :style="{ color: themeColors.textPrimary }">数据管理</text>
			</view>
			
			<view class="setting-item" @click="syncData" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="item-left">
					<text class="item-icon">☁️</text>
					<text class="item-title" :style="{ color: themeColors.textPrimary }">数据同步</text>
				</view>
				<text class="item-arrow" :style="{ color: themeColors.textSecondary }">></text>
			</view>
			
			<view class="setting-item" @click="pullFromCloud" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="item-left">
					<text class="item-icon">📥</text>
					<text class="item-title" :style="{ color: themeColors.textPrimary }">从云端拉取</text>
				</view>
				<text class="item-arrow" :style="{ color: themeColors.textSecondary }">></text>
			</view>
			
			<view class="setting-item" @click="exportData" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="item-left">
					<text class="item-icon">📤</text>
					<text class="item-title" :style="{ color: themeColors.textPrimary }">导出数据</text>
				</view>
				<text class="item-arrow" :style="{ color: themeColors.textSecondary }">></text>
			</view>
			
			<view class="sync-info">
				<text class="sync-text">最后同步：{{lastSyncText}}</text>
			</view>
		</view>
		
		<!-- 应用设置 -->
		<view class="section">
			<view class="section-title">
				<text :style="{ color: themeColors.textPrimary }">应用设置</text>
			</view>
			
			<view class="setting-item" @click="goToCategories">
				<view class="item-left">
					<text class="item-icon">📁</text>
					<text class="item-title">分类管理</text>
				</view>
				<text class="item-arrow">></text>
			</view>
			
			<view class="setting-item" @click="goToTags">
				<view class="item-left">
					<text class="item-icon">🏷️</text>
					<text class="item-title">标签管理</text>
				</view>
				<text class="item-arrow">></text>
			</view>
			
			<view class="setting-item" @click="toggleTheme" :style="{ backgroundColor: themeColors.backgroundCard }">
				<view class="item-left">
					<text class="item-icon">🎨</text>
					<text class="item-title" :style="{ color: themeColors.textPrimary }">主题设置</text>
				</view>
				<view class="item-right">
					<text class="item-value" :style="{ color: themeColors.textSecondary }">{{currentTheme}}</text>
					<text class="item-arrow" :style="{ color: themeColors.textSecondary }">></text>
				</view>
			</view>
			
			<view class="setting-item" @click="toggleNotification">
				<view class="item-left">
					<text class="item-icon">🔔</text>
					<text class="item-title">提醒通知</text>
				</view>
				<switch :checked="notificationEnabled" @change="onNotificationChange" color="#667eea" />
			</view>
			
			<view class="setting-item" @click="toggleAutoSync">
				<view class="item-left">
					<text class="item-icon">🔄</text>
					<text class="item-title">自动同步</text>
				</view>
				<switch :checked="autoSyncEnabled" @change="onAutoSyncChange" color="#667eea" />
			</view>
			
			<view class="setting-item" @click="setPassword">
				<view class="item-left">
					<text class="item-icon">🔒</text>
					<text class="item-title">安全设置</text>
				</view>
				<text class="item-arrow">></text>
			</view>
			
			<view class="setting-item" @click="clearData">
				<view class="item-left">
					<text class="item-icon">🗑️</text>
					<text class="item-title">清空数据</text>
				</view>
				<text class="item-arrow">></text>
			</view>
		</view>
		
		<!-- 关于 -->
		<view class="section">
			<view class="section-title">
				<text>关于</text>
			</view>
			
			<view class="setting-item" @click="showHelp">
				<view class="item-left">
					<text class="item-icon">❓</text>
					<text class="item-title">使用帮助</text>
				</view>
				<text class="item-arrow">></text>
			</view>
			
			<view class="setting-item">
				<view class="item-left">
					<text class="item-icon">📱</text>
					<text class="item-title">版本信息</text>
				</view>
				<text class="item-value">v1.0.0</text>
			</view>
		</view>
	</view>
</template>
<script>
import StorageManager from '@/utils/storageManager.js';
import SyncManager from '@/utils/sync.js';
import ThemeManager from '@/utils/themeManager.js';

export default {
	data() {
		return {
			lastSync: 0,
			currentTheme: ThemeManager.getCurrentThemeName(),
			notificationEnabled: true,
			autoSyncEnabled: true,
			themeColors: ThemeManager.getColors()
		};
	},
	computed: {
		lastSyncText() {
			return this.lastSync ? new Date(this.lastSync).toLocaleString() : '未同步';
		},
		currentThemeName() {
			const themeNames = {
				'light': '清新蓝',
				'orange': '温暖橙',
				'purple': '优雅紫',
				'dark': '深色模式',
				'green': '自然绿',
				'pink': '浪漫粉'
			};
			return themeNames[this.currentTheme] || '清新蓝';
		}
	},
	onLoad() {
		this.lastSync = StorageManager.get('lastSyncTime', 0);
		this.currentTheme = ThemeManager.getCurrentThemeName();
		this.notificationEnabled = StorageManager.get('notificationEnabled', true);
		this.autoSyncEnabled = StorageManager.get('autoSyncEnabled', true);
		this.themeColors = ThemeManager.getColors();
		
		// 设置主题监听器
		ThemeManager.addListener(this.onThemeChange);
	},
	
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	methods: {
		// 数据同步
		async syncData() {
			uni.showLoading({ title: '同步中...' });
			try {
				const res = await SyncManager.syncData();
				uni.hideLoading();
				uni.showToast({ 
					title: res.message || '同步完成', 
					icon: 'success',
					duration: 2000
				});
				this.lastSync = Date.now();
			} catch (error) {
				uni.hideLoading();
				uni.showToast({ 
					title: '同步失败', 
					icon: 'error' 
				});
			}
		},
		
		// 从云端拉取数据
		async pullFromCloud() {
			uni.showModal({
				title: '确认操作',
				content: '从云端拉取数据会覆盖本地数据，确定继续吗？',
				success: async (res) => {
					if (res.confirm) {
						uni.showLoading({ title: '拉取中...' });
						try {
							const result = await SyncManager.pullFromCloud();
							uni.hideLoading();
							uni.showToast({ 
								title: result.message || '拉取完成', 
								icon: 'success' 
							});
							this.lastSync = Date.now();
						} catch (error) {
							uni.hideLoading();
							uni.showToast({ 
								title: '拉取失败', 
								icon: 'error' 
							});
						}
					}
				}
			});
		},
		
		// 导出数据
		exportData() {
			try {
				const records = StorageManager.getRecords();
				const dataStr = JSON.stringify(records, null, 2);
				
				// 在小程序中，可以将数据复制到剪贴板
				uni.setClipboardData({
					data: dataStr,
					success: () => {
						uni.showToast({ 
							title: '数据已复制到剪贴板', 
							icon: 'success' 
						});
					}
				});
			} catch (error) {
				uni.showToast({ 
					title: '导出失败', 
					icon: 'error' 
				});
			}
		},
		
		// 跳转到分类管理
		goToCategories() {
			uni.navigateTo({
				url: '/pages/categories/categories'
			});
		},
		
		// 跳转到标签管理
		goToTags() {
			uni.navigateTo({ url: '/pages/tags/tags' });
		},
		
		// 主题设置
		toggleTheme() {
			// 跳转到主题设置页面
			uni.navigateTo({
				url: '/pages/settings/theme'
			});
		},
		
		// 主题变化回调
		onThemeChange(theme) {
			this.currentTheme = theme.name;
			this.themeColors = theme.colors;
		},
		
		// 通知设置
		toggleNotification() {
			this.notificationEnabled = !this.notificationEnabled;
		},
		onNotificationChange(e) {
			this.notificationEnabled = e.detail.value;
			StorageManager.set('notificationEnabled', this.notificationEnabled);
			uni.showToast({ 
				title: this.notificationEnabled ? '已开启提醒通知' : '已关闭提醒通知', 
				icon: 'success' 
			});
		},
		
		// 自动同步设置
		toggleAutoSync() {
			this.autoSyncEnabled = !this.autoSyncEnabled;
		},
		onAutoSyncChange(e) {
			this.autoSyncEnabled = e.detail.value;
			StorageManager.set('autoSyncEnabled', this.autoSyncEnabled);
			uni.showToast({ 
				title: this.autoSyncEnabled ? '已开启自动同步' : '已关闭自动同步', 
				icon: 'success' 
			});
		},
		
		// 安全设置
		setPassword() {
			uni.navigateTo({
				url: '/pages/security/security'
			});
		},
		
		// 显示帮助
		showHelp() {
			uni.navigateTo({ url: '/pages/help/help' });
		},
		
		// 清空数据
		clearData() {
			uni.showModal({
				title: '危险操作',
				content: '此操作将清空所有本地数据，且无法恢复，确定继续吗？',
				confirmColor: '#ff4757',
				success: (res) => {
					if (res.confirm) {
						StorageManager.clearAllData();
						uni.showToast({ 
							title: '数据已清空', 
							icon: 'success' 
						});
						// 返回首页
						setTimeout(() => {
							uni.switchTab({ url: '/pages/index/index' });
						}, 1500);
					}
				}
			});
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-title {
	font-size: 44rpx;
	font-weight: bold;
	text-align: center;
}

/* 统计卡片 */
.stats-card {
	background: white;
	margin: -40rpx 40rpx 40rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-label {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.stat-value {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
}

.stat-divider {
	width: 2rpx;
	background-color: #eee;
	margin: 0 30rpx;
}

/* 分组样式 */
.section {
	margin: 0 40rpx 40rpx;
}

.section-title {
	padding: 30rpx 0 20rpx;
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

/* 设置项样式 */
.setting-item {
	background: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-radius: 20rpx;
	margin-bottom: 2rpx;
	transition: background-color 0.2s;
}

.setting-item:first-child {
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
}

.setting-item:last-child {
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
	margin-bottom: 0;
}

.setting-item:active {
	background-color: #f8f9fa;
}

.item-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.item-right {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.item-icon {
	font-size: 32rpx;
	margin-right: 24rpx;
}

.item-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.item-value {
	font-size: 28rpx;
	color: #666;
}

.item-arrow {
	font-size: 28rpx;
	color: #ccc;
	font-weight: bold;
}

/* 同步信息 */
.sync-info {
	padding: 20rpx 40rpx;
	text-align: center;
}

.sync-text {
	font-size: 24rpx;
	color: #999;
}
</style>
