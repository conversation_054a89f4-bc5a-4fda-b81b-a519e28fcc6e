
.recurring-container[data-v-6166ed01] {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header[data-v-6166ed01] {
  padding: 44px 20px 20px;
  background: rgba(255, 255, 255, 0.1);
}
.header-content[data-v-6166ed01] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title[data-v-6166ed01] {
  font-size: 28px;
  font-weight: bold;
  color: white;
}
.add-btn[data-v-6166ed01] {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-icon[data-v-6166ed01] {
  font-size: 24px;
  color: white;
  font-weight: bold;
}
.template-list[data-v-6166ed01] {
  margin: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.template-item[data-v-6166ed01] {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.item-header[data-v-6166ed01] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.item-info[data-v-6166ed01] {
  flex: 1;
}
.item-name[data-v-6166ed01] {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}
.item-frequency[data-v-6166ed01] {
  font-size: 12px;
  color: #999;
}
.item-details[data-v-6166ed01] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}
.detail-row[data-v-6166ed01] {
  text-align: center;
  flex: 1;
}
.detail-label[data-v-6166ed01] {
  display: block;
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}
.detail-value[data-v-6166ed01] {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}
.item-actions[data-v-6166ed01] {
  display: flex;
  gap: 12px;
}
.action-btn[data-v-6166ed01] {
  flex: 1;
  padding: 8px 16px;
  background: #f0f0f0;
  border-radius: 20px;
  text-align: center;
  font-size: 14px;
  color: #666;
}
.modal[data-v-6166ed01] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content[data-v-6166ed01] {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}
.modal-header[data-v-6166ed01] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title[data-v-6166ed01] {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.modal-close[data-v-6166ed01] {
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-body[data-v-6166ed01] {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}
.form-group[data-v-6166ed01] {
  margin-bottom: 20px;
}
.form-label[data-v-6166ed01] {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.form-input[data-v-6166ed01] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}
.form-textarea[data-v-6166ed01] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
}
.type-options[data-v-6166ed01] {
  display: flex;
  gap: 12px;
}
.type-option[data-v-6166ed01] {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
}
.type-option.selected[data-v-6166ed01] {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}
.frequency-options[data-v-6166ed01] {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.frequency-option[data-v-6166ed01] {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}
.frequency-option.selected[data-v-6166ed01] {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}
.picker-view[data-v-6166ed01] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
}
.picker-text[data-v-6166ed01] {
  color: #333;
}
.picker-arrow[data-v-6166ed01] {
  color: #999;
}
.modal-footer[data-v-6166ed01] {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}
.btn[data-v-6166ed01] {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
}
.btn-cancel[data-v-6166ed01] {
  background: #f0f0f0;
  color: #666;
}
.btn-primary[data-v-6166ed01] {
  background: #667eea;
  color: white;
}
