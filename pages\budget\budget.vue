<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">预算管理</text>
				<text class="header-action" @click="addBudget">+</text>
			</view>
		</view>
		
		<!-- 预算总览 -->
		<view class="budget-overview">
			<view class="overview-item">
				<text class="overview-label">本月预算</text>
				<text class="overview-value">¥{{totalBudget.toFixed(2)}}</text>
			</view>
			<view class="overview-item">
				<text class="overview-label">已支出</text>
				<text class="overview-value expense">¥{{totalSpent.toFixed(2)}}</text>
			</view>
			<view class="overview-item">
				<text class="overview-label">剩余</text>
				<text class="overview-value" :class="remaining >= 0 ? 'positive' : 'negative'">
					¥{{remaining.toFixed(2)}}
				</text>
			</view>
		</view>
		
		<!-- 预算进度 -->
		<view class="budget-progress">
			<view class="progress-header">
				<text class="progress-title">总体进度</text>
				<text class="progress-percent">{{overallProgress}}%</text>
			</view>
			<view class="progress-bar">
				<view 
					class="progress-fill" 
					:class="overallProgress > 100 ? 'over-budget' : ''"
					:style="{width: Math.min(overallProgress, 100) + '%'}"
				></view>
			</view>
		</view>
		
		<!-- 预算列表 -->
		<view class="budgets-section">
			<view class="section-title">
				<text>预算详情</text>
			</view>
			
			<view v-if="budgets.length === 0" class="empty-state">
				<text class="empty-icon">📊</text>
				<text class="empty-text">暂无预算，点击右上角添加</text>
			</view>
			
			<view v-else class="budgets-list">
				<view 
					v-for="budget in budgetsWithProgress" 
					:key="budget.id"
					class="budget-item"
					@click="editBudget(budget)"
				>
					<view class="budget-header">
						<view class="budget-info">
							<text class="budget-name">{{budget.name}}</text>
							<text class="budget-category">{{getCategoryName(budget.categoryId)}}</text>
						</view>
						<view class="budget-amount">
							<text class="spent-amount">¥{{budget.spent.toFixed(2)}}</text>
							<text class="total-amount">/ ¥{{budget.amount.toFixed(2)}}</text>
						</view>
					</view>
					
					<view class="budget-progress-bar">
						<view 
							class="budget-progress-fill"
							:class="budget.progress > 100 ? 'over-budget' : ''"
							:style="{width: Math.min(budget.progress, 100) + '%'}"
						></view>
					</view>
					
					<view class="budget-footer">
						<text class="progress-text">{{budget.progress.toFixed(1)}}%</text>
						<text class="remaining-text" :class="budget.remaining >= 0 ? 'positive' : 'negative'">
							剩余 ¥{{budget.remaining.toFixed(2)}}
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加/编辑预算弹窗 -->
		<view v-if="showBudgetPopup" class="popup-overlay" @click="closeBudgetPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingBudget ? '编辑预算' : '添加预算'}}</text>
					<text class="popup-close" @click="closeBudgetPopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">预算名称</text>
						<input 
							class="form-input" 
							v-model="budgetForm.name" 
							placeholder="请输入预算名称"
							maxlength="20"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">关联分类</text>
						<view class="category-selector" @click="selectCategory">
							<text class="selector-text" :class="budgetForm.categoryId ? '' : 'placeholder'">
								{{budgetForm.categoryId ? getCategoryName(budgetForm.categoryId) : '请选择分类'}}
							</text>
							<text class="selector-arrow">></text>
						</view>
					</view>
					
					<view class="form-item">
						<text class="form-label">预算金额</text>
						<input 
							class="form-input" 
							v-model="budgetForm.amount" 
							type="digit"
							placeholder="0.00"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">预算周期</text>
						<view class="period-selector">
							<view 
								v-for="period in budgetPeriods" 
								:key="period.value"
								class="period-item"
								:class="{active: budgetForm.period === period.value}"
								@click="selectPeriod(period.value)"
							>
								<text class="period-text">{{period.name}}</text>
							</view>
						</view>
					</view>
				</view>
				
				<view class="popup-actions">
					<button v-if="editingBudget" class="delete-btn" @click="deleteBudget">删除</button>
					<button class="cancel-btn" @click="closeBudgetPopup">取消</button>
					<button class="save-btn" @click="saveBudget">保存</button>
				</view>
			</view>
		</view>
		
		<!-- 分类选择弹窗 -->
		<view v-if="showCategoryPopup" class="popup-overlay" @click="closeCategoryPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">选择分类</text>
					<text class="popup-close" @click="closeCategoryPopup">×</text>
				</view>
				<view class="categories-list">
					<view 
						v-for="category in expenseCategories" 
						:key="category.id"
						class="category-item"
						@click="selectCategoryItem(category)"
					>
						<text class="category-icon">{{category.icon}}</text>
						<text class="category-name">{{category.name}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createBudget } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			budgets: [],
			records: [],
			categories: [],
			showBudgetPopup: false,
			showCategoryPopup: false,
			editingBudget: null,
			budgetForm: {
				name: '',
				categoryId: '',
				amount: '',
				period: 'month'
			},
			budgetPeriods: [
				{ value: 'month', name: '月度' },
				{ value: 'week', name: '周度' },
				{ value: 'year', name: '年度' }
			]
		};
	},
	computed: {
		// 支出分类
		expenseCategories() {
			return this.categories.filter(cat => cat.type === 'expense');
		},
		
		// 当前月份记录
		currentMonthRecords() {
			const now = new Date();
			const currentMonth = now.getMonth();
			const currentYear = now.getFullYear();
			
			return this.records.filter(record => {
				const recordDate = new Date(record.date);
				return record.type === 'expense' && 
					   recordDate.getMonth() === currentMonth && 
					   recordDate.getFullYear() === currentYear;
			});
		},
		
		// 总预算
		totalBudget() {
			return this.budgets.reduce((sum, budget) => sum + budget.amount, 0);
		},
		
		// 总支出
		totalSpent() {
			return this.currentMonthRecords.reduce((sum, record) => sum + record.amount, 0);
		},
		
		// 剩余预算
		remaining() {
			return this.totalBudget - this.totalSpent;
		},
		
		// 总体进度
		overallProgress() {
			if (this.totalBudget === 0) return 0;
			return Math.round((this.totalSpent / this.totalBudget) * 100);
		},
		
		// 带进度的预算列表
		budgetsWithProgress() {
			return this.budgets.map(budget => {
				const spent = this.currentMonthRecords
					.filter(record => record.categoryId === budget.categoryId)
					.reduce((sum, record) => sum + record.amount, 0);
				
				const progress = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
				const remaining = budget.amount - spent;
				
				return {
					...budget,
					spent,
					progress,
					remaining
				};
			});
		}
	},
	onLoad() {
		this.loadData();
	},
	onShow() {
		this.loadData();
	},
	methods: {
		loadData() {
			this.budgets = StorageManager.getBudgets();
			this.records = StorageManager.getRecords();
			this.categories = this.getDefaultCategories();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		addBudget() {
			this.editingBudget = null;
			this.resetBudgetForm();
			this.$refs.budgetPopup.open();
		},
		
		editBudget(budget) {
			this.editingBudget = budget;
			this.budgetForm = {
				name: budget.name,
				categoryId: budget.categoryId,
				amount: budget.amount.toString(),
				period: budget.period
			};
			this.$refs.budgetPopup.open();
		},
		
		resetBudgetForm() {
			this.budgetForm = {
				name: '',
				categoryId: '',
				amount: '',
				period: 'month'
			};
		},
		
		selectCategory() {
			this.$refs.categoryPopup.open();
		},
		
		selectCategoryItem(category) {
			this.budgetForm.categoryId = category.id;
			if (!this.budgetForm.name) {
				this.budgetForm.name = `${category.name}预算`;
			}
			this.closeCategoryPopup();
		},
		
		selectPeriod(period) {
			this.budgetForm.period = period;
		},
		
		closeBudgetPopup() {
			this.$refs.budgetPopup.close();
		},
		
		closeCategoryPopup() {
			this.$refs.categoryPopup.close();
		},
		
		saveBudget() {
			// 验证表单
			if (!this.budgetForm.name.trim()) {
				uni.showToast({
					title: '请输入预算名称',
					icon: 'none'
				});
				return;
			}
			
			if (!this.budgetForm.categoryId) {
				uni.showToast({
					title: '请选择关联分类',
					icon: 'none'
				});
				return;
			}
			
			const amount = parseFloat(this.budgetForm.amount);
			if (!amount || amount <= 0) {
				uni.showToast({
					title: '请输入正确的预算金额',
					icon: 'none'
				});
				return;
			}
			
			// 检查是否已存在相同分类的预算
			const existingBudget = this.budgets.find(b => 
				b.categoryId === this.budgetForm.categoryId && 
				(!this.editingBudget || b.id !== this.editingBudget.id)
			);
			
			if (existingBudget) {
				uni.showToast({
					title: '该分类已存在预算',
					icon: 'none'
				});
				return;
			}
			
			if (this.editingBudget) {
				// 编辑预算
				const index = this.budgets.findIndex(b => b.id === this.editingBudget.id);
				if (index !== -1) {
					this.budgets[index] = {
						...this.editingBudget,
						name: this.budgetForm.name.trim(),
						categoryId: this.budgetForm.categoryId,
						amount: amount,
						period: this.budgetForm.period,
						updatedAt: Date.now()
					};
				}
			} else {
				// 添加新预算
				const newBudget = createBudget({
					name: this.budgetForm.name.trim(),
					categoryId: this.budgetForm.categoryId,
					amount: amount,
					period: this.budgetForm.period
				});
				this.budgets.push(newBudget);
			}
			
			StorageManager.saveBudgets(this.budgets);
			this.closeBudgetPopup();
			
			uni.showToast({
				title: this.editingBudget ? '预算已更新' : '预算已添加',
				icon: 'success'
			});
		},
		
		deleteBudget() {
			if (!this.editingBudget) return;
			
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个预算吗？',
				success: (res) => {
					if (res.confirm) {
						const index = this.budgets.findIndex(b => b.id === this.editingBudget.id);
						if (index !== -1) {
							this.budgets.splice(index, 1);
							StorageManager.saveBudgets(this.budgets);
							this.closeBudgetPopup();
							
							uni.showToast({
								title: '预算已删除',
								icon: 'success'
							});
						}
					}
				}
			});
		},
		
		getCategoryName(categoryId) {
			const category = this.categories.find(c => c.id === categoryId);
			return category ? category.name : '未知分类';
		},
		
		getDefaultCategories() {
			return [
				// 支出分类
				{ id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily' },
				{ id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily' },
				{ id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily' },
				{ id: 'entertainment', name: '娱乐', icon: '🎬', type: 'expense', groupId: 'daily' },
				{ id: 'healthcare', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily' },
				{ id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily' },
				{ id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily' },
				{ id: 'utilities', name: '水电费', icon: '💡', type: 'expense', groupId: 'daily' },
				{ id: 'other_expense', name: '其他支出', icon: '💸', type: 'expense', groupId: 'daily' },
				
				// 报销分类
				{ id: 'business_meal', name: '商务餐饮', icon: '🍽️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'business_transport', name: '差旅交通', icon: '✈️', type: 'expense', groupId: 'reimbursement' },
				{ id: 'accommodation', name: '住宿费', icon: '🏨', type: 'expense', groupId: 'reimbursement' },
				{ id: 'office_supplies', name: '办公用品', icon: '📎', type: 'expense', groupId: 'reimbursement' },
				{ id: 'communication', name: '通讯费', icon: '📱', type: 'expense', groupId: 'reimbursement' },
				{ id: 'training', name: '培训费', icon: '🎓', type: 'expense', groupId: 'reimbursement' },
				{ id: 'entertainment_business', name: '商务招待', icon: '🍷', type: 'expense', groupId: 'reimbursement' },
				{ id: 'other_reimbursement', name: '其他报销', icon: '📋', type: 'expense', groupId: 'reimbursement' }
			];
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 预算总览 */
.budget-overview {
	background: white;
	margin: -20rpx 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.overview-item {
	flex: 1;
	text-align: center;
}

.overview-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.overview-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.overview-value.expense {
	color: #ff4757;
}

.overview-value.positive {
	color: #4CAF50;
}

.overview-value.negative {
	color: #ff4757;
}

/* 预算进度 */
.budget-progress {
	background: white;
	margin: 0 40rpx 20rpx;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.progress-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.progress-percent {
	font-size: 28rpx;
	font-weight: bold;
	color: #667eea;
}

.progress-bar {
	height: 16rpx;
	background: #f0f0f0;
	border-radius: 8rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
	border-radius: 8rpx;
	transition: width 0.3s ease;
}

.progress-fill.over-budget {
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
}

/* 预算列表 */
.budgets-section {
	margin: 0 40rpx;
}

.section-title {
	padding: 20rpx 0;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.empty-state {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.budgets-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.budget-item {
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.budget-item:last-child {
	border-bottom: none;
}

.budget-item:active {
	background-color: #f8f9fa;
}

.budget-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.budget-info {
	flex: 1;
}

.budget-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.budget-category {
	font-size: 26rpx;
	color: #666;
}

.budget-amount {
	text-align: right;
}

.spent-amount {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff4757;
}

.total-amount {
	font-size: 28rpx;
	color: #666;
}

.budget-progress-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.budget-progress-fill {
	height: 100%;
	background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.budget-progress-fill.over-budget {
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
}

.budget-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.progress-text {
	font-size: 24rpx;
	color: #666;
}

.remaining-text {
	font-size: 24rpx;
	font-weight: 500;
}

.remaining-text.positive {
	color: #4CAF50;
}

.remaining-text.negative {
	color: #ff4757;
}

/* 弹窗样式 */
.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

.category-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.selector-text {
	font-size: 28rpx;
	color: #333;
}

.selector-text.placeholder {
	color: #999;
}

.selector-arrow {
	font-size: 28rpx;
	color: #ccc;
}

.period-selector {
	display: flex;
	gap: 16rpx;
}

.period-item {
	flex: 1;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s;
}

.period-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.period-text {
	font-size: 28rpx;
	font-weight: 500;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
}

.delete-btn, .cancel-btn, .save-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.delete-btn {
	background: #ff4757;
	color: white;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

/* 分类列表 */
.categories-list {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	max-height: 400rpx;
	overflow-y: auto;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	transition: background-color 0.2s;
}

.category-item:active {
	background-color: #e9ecef;
}

.category-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.category-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
}
</style>
