/**
 * 创建账户
 * @param {Object} options - 账户参数
 * @param {string} options.name - 账户名称
 * @param {string} options.type - 账户类型
 * @param {number} options.balance - 初始余额
 * @param {string} options.icon - 图标
 * @param {string} options.color - 颜色
 * @returns {Object} 账户对象
 */
export function createAccount({
  name = '',
  type = 'cash',
  balance = 0,
  icon = '💰',
  color = '#4CAF50'
} = {}) {
  const now = Date.now();
  return {
    id: `account_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    type,
    balance: Number(balance),
    icon,
    color,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建记账记录
 * @param {Object} options - 记录参数
 * @param {string} options.type - 类型：expense(支出)、income(收入)、transfer(转账)
 * @param {string} options.categoryId - 分类 ID
 * @param {string} options.accountId - 账户 ID
 * @param {string} options.toAccountId - 转账目标账户 ID（仅转账时使用）
 * @param {number} options.amount - 金额
 * @param {string} options.note - 备注
 * @param {Array} options.tags - 标签数组
 * @param {number} options.date - 日期时间戳
 * @returns {Object} 记录对象
 */
export function createRecord({
  type = 'expense',
  categoryId = '',
  accountId = '',
  toAccountId = '',
  amount = 0,
  note = '',
  tags = [],
  date = Date.now()
} = {}) {
  const now = Date.now();
  return {
    id: `record_${now}_${Math.random().toString(36).slice(2, 8)}`,
    type,
    categoryId,
    accountId,
    toAccountId,
    amount: Number(amount),
    note,
    tags: Array.isArray(tags) ? tags : [],
    date: Number(date),
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建分类
 * @param {Object} options - 分类参数
 * @returns {Object} 分类对象
 */
export function createCategory({
  name = '',
  icon = '💰',
  color = '#4CAF50',
  type = 'expense',
  parentId = null,
  groupId = 'daily'
} = {}) {
  const now = Date.now();
  return {
    id: `category_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    icon,
    color,
    type,
    parentId,
    groupId,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 创建预算
 * @param {Object} options - 预算参数
 * @returns {Object} 预算对象
 */
export function createBudget({
  name = '',
  categoryId = '',
  amount = 0,
  period = 'month',
  startDate = Date.now(),
  endDate = null
} = {}) {
  const now = Date.now();
  return {
    id: `budget_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    categoryId,
    amount: Number(amount),
    period,
    startDate: Number(startDate),
    endDate: endDate ? Number(endDate) : null,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 验证记录数据
 * @param {Object} record - 记录对象
 * @returns {Object} 验证结果
 */
export function validateRecord(record) {
  const errors = [];
  
  if (!record.amount || record.amount <= 0) {
    errors.push('金额必须大于0');
  }
  
  if (!record.accountId) {
    errors.push('必须选择账户');
  }
  
  if (record.type === 'transfer') {
    if (!record.toAccountId) {
      errors.push('转账必须选择目标账户');
    }
    if (record.accountId === record.toAccountId) {
      errors.push('转账的源账户和目标账户不能相同');
    }
  } else {
    if (!record.categoryId) {
      errors.push('必须选择分类');
    }
  }
  
  if (!['expense', 'income', 'transfer'].includes(record.type)) {
    errors.push('类型必须是 expense、income 或 transfer');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 验证账户数据
 * @param {Object} account - 账户对象
 * @returns {Object} 验证结果
 */
export function validateAccount(account) {
  const errors = [];
  
  if (!account.name || account.name.trim() === '') {
    errors.push('账户名称不能为空');
  }
  
  if (!account.type) {
    errors.push('必须选择账户类型');
  }
  
  if (isNaN(account.balance)) {
    errors.push('初始余额必须是数字');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 创建标签
 * @param {Object} options - 标签参数
 * @returns {Object} 标签对象
 */
export function createTag({
  name = '',
  color = '#4CAF50',
  icon = '🏷️'
} = {}) {
  const now = Date.now();
  return {
    id: `tag_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    color,
    icon,
    usageCount: 0,
    createdAt: now,
    updatedAt: now,
    synced: false,
    deleted: false
  };
}

/**
 * 验证标签数据
 * @param {Object} tag - 标签对象
 * @returns {Object} 验证结果
 */
export function validateTag(tag) {
  if (!tag || typeof tag !== 'object') {
    return { valid: false, message: '标签对象不能为空' };
  }
  
  if (!tag.name || tag.name.trim() === '') {
    return { valid: false, message: '标签名称不能为空' };
  }
  
  if (tag.name.length > 10) {
    return { valid: false, message: '标签名称不能超过10个字符' };
  }
  
  return { valid: true };
}

/**
 * 格式化记录显示
 * @param {Object} record - 记录对象
 * @returns {Object} 格式化后的显示数据
 */
export function formatRecord(record) {
  const date = new Date(record.date);
  
  return {
    ...record,
    formattedAmount: record.amount.toFixed(2),
    formattedDate: `${date.getMonth() + 1}月${date.getDate()}日`,
    formattedTime: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`,
    displayAmount: `${record.type === 'expense' ? '-' : '+'}¥${record.amount.toFixed(2)}`
  };
}

/**
 * 创建储蓄目标
 * @param {Object} options - 储蓄目标选项
 * @returns {Object} 储蓄目标对象
 */
export function createSavingsGoal({
  name = '',
  targetAmount = 0,
  currentAmount = 0,
  deadline = null,
  icon = '🎯',
  color = '#4CAF50'
} = {}) {
  const now = Date.now();
  return {
    id: `goal_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    targetAmount: Number(targetAmount),
    currentAmount: Number(currentAmount),
    deadline: deadline ? Number(deadline) : null,
    icon,
    color,
    createdAt: now,
    updatedAt: now,
    completed: false
  };
}

/**
 * 验证储蓄目标
 * @param {Object} goal - 储蓄目标对象
 * @returns {Object} 验证结果
 */
export function validateSavingsGoal(goal) {
  if (!goal || typeof goal !== 'object') {
    return { valid: false, message: '储蓄目标对象不能为空' };
  }
  
  if (!goal.name || goal.name.trim() === '') {
    return { valid: false, message: '目标名称不能为空' };
  }
  
  if (goal.targetAmount <= 0) {
    return { valid: false, message: '目标金额必须大于0' };
  }
  
  if (goal.currentAmount < 0) {
    return { valid: false, message: '当前金额不能为负数' };
  }
  
  if (goal.deadline && goal.deadline < Date.now()) {
    return { valid: false, message: '截止日期不能早于今天' };
  }
  
  return { valid: true };
}

/**
 * 创建提醒
 * @param {Object} options - 提醒选项
 * @returns {Object} 提醒对象
 */
export function createReminder({
  name = '',
  amount = 0,
  type = 'expense',
  cycle = 'monthly',
  firstDate = Date.now(),
  advanceDays = 1,
  icon = '⏰',
  color = '#FF9800',
  enabled = true
} = {}) {
  const now = Date.now();
  return {
    id: `reminder_${now}_${Math.random().toString(36).slice(2, 8)}`,
    name,
    amount: Number(amount),
    type,
    cycle,
    firstDate: Number(firstDate),
    nextDate: Number(firstDate),
    advanceDays: Number(advanceDays),
    icon,
    color,
    enabled,
    createdAt: now,
    updatedAt: now
  };
}

/**
 * 验证提醒
 * @param {Object} reminder - 提醒对象
 * @returns {Object} 验证结果
 */
export function validateReminder(reminder) {
  if (!reminder || typeof reminder !== 'object') {
    return { valid: false, message: '提醒对象不能为空' };
  }
  
  if (!reminder.name || reminder.name.trim() === '') {
    return { valid: false, message: '提醒名称不能为空' };
  }
  
  if (reminder.amount < 0) {
    return { valid: false, message: '金额不能为负数' };
  }
  
  if (!['expense', 'income'].includes(reminder.type)) {
    return { valid: false, message: '提醒类型必须是支出或收入' };
  }
  
  if (!['weekly', 'monthly', 'yearly', 'once'].includes(reminder.cycle)) {
    return { valid: false, message: '提醒周期无效' };
  }
  
  if (reminder.advanceDays < 0 || reminder.advanceDays > 30) {
    return { valid: false, message: '提前提醒天数必须在0-30之间' };
  }
  
  return { valid: true };
}
