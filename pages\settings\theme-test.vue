<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="header-title">主题测试</text>
		</view>
		
		<!-- 当前主题信息 -->
		<view class="theme-info" :style="{ backgroundColor: themeColors.backgroundCard }">
			<text class="info-title" :style="{ color: themeColors.textPrimary }">当前主题</text>
			<text class="info-value" :style="{ color: themeColors.textSecondary }">{{ currentThemeName }}</text>
		</view>
		
		<!-- 颜色展示 -->
		<view class="color-section">
			<view class="section-title" :style="{ color: themeColors.textPrimary }">
				<text>主题颜色</text>
			</view>
			
			<view class="color-grid">
				<view 
					v-for="(color, key) in themeColors" 
					:key="key"
					class="color-item"
					:style="{ backgroundColor: themeColors.backgroundCard }"
				>
					<view class="color-preview" :style="{ backgroundColor: color }"></view>
					<text class="color-name" :style="{ color: themeColors.textPrimary }">{{ key }}</text>
					<text class="color-value" :style="{ color: themeColors.textSecondary }">{{ color }}</text>
				</view>
			</view>
		</view>
		
		<!-- 组件测试 -->
		<view class="component-section">
			<view class="section-title" :style="{ color: themeColors.textPrimary }">
				<text>组件测试</text>
			</view>
			
			<!-- 按钮测试 -->
			<view class="test-group">
				<text class="group-title" :style="{ color: themeColors.textPrimary }">按钮</text>
				<view class="button-group">
					<view class="btn primary" :style="{ backgroundColor: themeColors.primary }">
						<text class="btn-text" :style="{ color: themeColors.textReverse }">主要按钮</text>
					</view>
					<view class="btn secondary" :style="{ backgroundColor: themeColors.backgroundCard, borderColor: themeColors.border }">
						<text class="btn-text" :style="{ color: themeColors.textPrimary }">次要按钮</text>
					</view>
				</view>
			</view>
			
			<!-- 卡片测试 -->
			<view class="test-group">
				<text class="group-title" :style="{ color: themeColors.textPrimary }">卡片</text>
				<view class="card" :style="{ backgroundColor: themeColors.backgroundCard, borderColor: themeColors.border }">
					<text class="card-title" :style="{ color: themeColors.textPrimary }">卡片标题</text>
					<text class="card-content" :style="{ color: themeColors.textSecondary }">这是卡片内容，用于测试主题颜色的显示效果。</text>
				</view>
			</view>
			
			<!-- 状态颜色测试 -->
			<view class="test-group">
				<text class="group-title" :style="{ color: themeColors.textPrimary }">状态颜色</text>
				<view class="status-group">
					<view class="status-item">
						<view class="status-dot" :style="{ backgroundColor: themeColors.success }"></view>
						<text class="status-text" :style="{ color: themeColors.textPrimary }">成功</text>
					</view>
					<view class="status-item">
						<view class="status-dot" :style="{ backgroundColor: themeColors.warning }"></view>
						<text class="status-text" :style="{ color: themeColors.textPrimary }">警告</text>
					</view>
					<view class="status-item">
						<view class="status-dot" :style="{ backgroundColor: themeColors.error }"></view>
						<text class="status-text" :style="{ color: themeColors.textPrimary }">错误</text>
					</view>
					<view class="status-item">
						<view class="status-dot" :style="{ backgroundColor: themeColors.info }"></view>
						<text class="status-text" :style="{ color: themeColors.textPrimary }">信息</text>
					</view>
				</view>
			</view>
			
			<!-- 收支颜色测试 -->
			<view class="test-group">
				<text class="group-title" :style="{ color: themeColors.textPrimary }">收支颜色</text>
				<view class="income-expense-group">
					<view class="amount-item">
						<text class="amount-label" :style="{ color: themeColors.textSecondary }">收入</text>
						<text class="amount-value" :style="{ color: themeColors.income }">+¥1,000.00</text>
					</view>
					<view class="amount-item">
						<text class="amount-label" :style="{ color: themeColors.textSecondary }">支出</text>
						<text class="amount-value" :style="{ color: themeColors.expense }">-¥500.00</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 快速切换主题 -->
		<view class="quick-switch">
			<view class="section-title" :style="{ color: themeColors.textPrimary }">
				<text>快速切换</text>
			</view>
			
			<view class="theme-buttons">
				<view 
					v-for="theme in availableThemes" 
					:key="theme.key"
					class="theme-btn"
					:class="{ active: theme.key === currentThemeKey }"
					:style="{ 
						backgroundColor: themeColors.backgroundCard,
						borderColor: theme.key === currentThemeKey ? themeColors.primary : themeColors.border
					}"
					@click="switchTheme(theme.key)"
				>
					<view class="theme-preview-mini" :style="{ background: theme.colors.gradient }"></view>
					<text class="theme-btn-text" :style="{ color: themeColors.textPrimary }">{{ theme.name }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import ThemeManager from '@/utils/themeManager.js';

export default {
	name: 'ThemeTestPage',
	data() {
		return {
			themeColors: ThemeManager.getColors(),
			currentThemeName: ThemeManager.getCurrentThemeName(),
			currentThemeKey: ThemeManager.getCurrentTheme().key,
			availableThemes: ThemeManager.getThemeList()
		};
	},
	
	onLoad() {
		// 设置主题监听器
		ThemeManager.addListener(this.onThemeChange);
	},
	
	onUnload() {
		// 移除主题监听器
		ThemeManager.removeListener(this.onThemeChange);
	},
	
	methods: {
		/**
		 * 主题变化回调
		 */
		onThemeChange(theme) {
			this.themeColors = theme.colors;
			this.currentThemeName = theme.name;
			this.currentThemeKey = theme.key;
		},
		
		/**
		 * 切换主题
		 */
		switchTheme(themeKey) {
			ThemeManager.setTheme(themeKey);
			uni.showToast({
				title: `已切换为${ThemeManager.getCurrentThemeName()}`,
				icon: 'success'
			});
		}
	}
};
</script>

<style scoped>
.page {
	min-height: 100vh;
	padding-bottom: 60rpx;
}

.header {
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
}

.header-title {
	color: #ffffff;
	font-size: 36rpx;
	font-weight: bold;
}

.theme-info {
	margin: 30rpx;
	padding: 30rpx;
	border-radius: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.info-title {
	font-size: 28rpx;
	font-weight: bold;
}

.info-value {
	font-size: 26rpx;
}

.color-section, .component-section, .quick-switch {
	margin: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
}

.color-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.color-item {
	padding: 20rpx;
	border-radius: 16rpx;
	text-align: center;
}

.color-preview {
	width: 60rpx;
	height: 60rpx;
	border-radius: 12rpx;
	margin: 0 auto 15rpx;
}

.color-name {
	font-size: 24rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.color-value {
	font-size: 20rpx;
	word-break: break-all;
}

.test-group {
	margin-bottom: 40rpx;
}

.group-title {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.button-group {
	display: flex;
	gap: 20rpx;
}

.btn {
	padding: 24rpx 40rpx;
	border-radius: 16rpx;
	text-align: center;
	flex: 1;
}

.btn.secondary {
	border: 2rpx solid;
}

.btn-text {
	font-size: 28rpx;
	font-weight: bold;
}

.card {
	padding: 30rpx;
	border-radius: 20rpx;
	border: 2rpx solid;
}

.card-title {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 15rpx;
	display: block;
}

.card-content {
	font-size: 26rpx;
	line-height: 1.5;
}

.status-group {
	display: flex;
	gap: 30rpx;
	flex-wrap: wrap;
}

.status-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.status-dot {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
}

.status-text {
	font-size: 26rpx;
}

.income-expense-group {
	display: flex;
	gap: 40rpx;
}

.amount-item {
	text-align: center;
}

.amount-label {
	font-size: 24rpx;
	margin-bottom: 10rpx;
	display: block;
}

.amount-value {
	font-size: 32rpx;
	font-weight: bold;
}

.theme-buttons {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.theme-btn {
	padding: 20rpx;
	border-radius: 16rpx;
	border: 2rpx solid;
	text-align: center;
	transition: all 0.3s ease;
}

.theme-btn.active {
	transform: scale(1.02);
}

.theme-preview-mini {
	width: 100%;
	height: 40rpx;
	border-radius: 8rpx;
	margin-bottom: 15rpx;
}

.theme-btn-text {
	font-size: 24rpx;
	font-weight: bold;
}
</style>
