export default class StorageManager {
  static get(key, defVal = null) {
    try {
      const val = uni.getStorageSync(key);
      return val || defVal;
    } catch (e) {
      return defVal;
    }
  }

  static set(key, val) {
    uni.setStorageSync(key, val);
  }

  // 记账记录
  static getRecords() {
    return this.get("records", []);
  }
  static saveRecords(list) {
    this.set("records", list);
  }

  // 分类
  static getCategories() {
    return this.get("categories", []);
  }
  static saveCategories(list) {
    this.set("categories", list);
  }

  // 同步时间
  static getLastSyncTime() {
    return this.get("last_sync_time", 0);
  }
  static setLastSyncTime(ts) {
    this.set("last_sync_time", ts);
  }

  // 账户管理
  static getAccounts() {
    return this.get("accounts", this.getDefaultAccounts());
  }
  static saveAccounts(list) {
    this.set("accounts", list);
  }

  // 预算管理
  static getBudgets() {
    return this.get("budgets", []);
  }
  static saveBudgets(list) {
    this.set("budgets", list);
  }

  // 标签管理
  static getTags() {
    return this.get("tags", []);
  }
  static saveTags(list) {
    this.set("tags", list);
  }

  // 提醒管理
  static getReminders() {
    return this.get("reminders", []);
  }
  static saveReminders(list) {
    this.set("reminders", list);
  }

  // 获取默认账户
  static getDefaultAccounts() {
    return [
      {
        id: 'cash',
        name: '现金',
        type: 'cash',
        balance: 0,
        icon: '💵',
        color: '#4CAF50'
      },
      {
        id: 'bank_card',
        name: '储蓄卡',
        type: 'bank_card',
        balance: 0,
        icon: '💳',
        color: '#2196F3'
      },
      {
        id: 'credit_card',
        name: '信用卡',
        type: 'credit_card',
        balance: 0,
        icon: '💳',
        color: '#FF9800'
      },
      {
        id: 'alipay',
        name: '支付宝',
        type: 'alipay',
        balance: 0,
        icon: '📱',
        color: '#1677FF'
      },
      {
        id: 'wechat',
        name: '微信支付',
        type: 'wechat',
        balance: 0,
        icon: '📱',
        color: '#07C160'
      }
    ];
  }

  // 储蓄目标管理
  static getSavingsGoals() {
    return this.get("savings_goals", []);
  }
  static saveSavingsGoals(list) {
    this.set("savings_goals", list);
  }

  // 提醒管理
  static getReminders() {
    return this.get("reminders", []);
  }
  static saveReminders(list) {
    this.set("reminders", list);
  }

  // 获取默认分类
  static getDefaultCategories() {
    return [
      // 支出分类 - 日常
      { id: 'expense_food', name: '餐饮', icon: '🍽️', color: '#FF9800', type: 'expense', groupId: 'daily' },
      { id: 'expense_transport', name: '交通', icon: '🚗', color: '#2196F3', type: 'expense', groupId: 'daily' },
      { id: 'expense_shopping', name: '购物', icon: '🛍️', color: '#E91E63', type: 'expense', groupId: 'daily' },
      { id: 'expense_entertainment', name: '娱乐', icon: '🎮', color: '#9C27B0', type: 'expense', groupId: 'daily' },
      { id: 'expense_medical', name: '医疗', icon: '🏥', color: '#F44336', type: 'expense', groupId: 'daily' },
      { id: 'expense_education', name: '教育', icon: '📚', color: '#3F51B5', type: 'expense', groupId: 'daily' },
      { id: 'expense_housing', name: '住房', icon: '🏠', color: '#795548', type: 'expense', groupId: 'daily' },
      { id: 'expense_utilities', name: '水电煤', icon: '💡', color: '#607D8B', type: 'expense', groupId: 'daily' },
      
      // 支出分类 - 报销
      { id: 'expense_business_meal', name: '商务餐饮', icon: '🍽️', color: '#FF9800', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_travel', name: '差旅交通', icon: '✈️', color: '#2196F3', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_accommodation', name: '住宿费', icon: '🏨', color: '#9C27B0', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_office_supplies', name: '办公用品', icon: '📎', color: '#4CAF50', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_communication', name: '通讯费', icon: '📱', color: '#00BCD4', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_training', name: '培训费', icon: '🎓', color: '#3F51B5', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_entertainment_business', name: '商务招待', icon: '🤝', color: '#FF5722', type: 'expense', groupId: 'reimbursement' },
      { id: 'expense_other_reimbursement', name: '其他报销', icon: '📋', color: '#795548', type: 'expense', groupId: 'reimbursement' },
      
      // 收入分类
      { id: 'income_salary', name: '工资', icon: '💰', color: '#4CAF50', type: 'income', groupId: 'daily' },
      { id: 'income_bonus', name: '奖金', icon: '🎁', color: '#FF9800', type: 'income', groupId: 'daily' },
      { id: 'income_investment', name: '投资收益', icon: '📈', color: '#2196F3', type: 'income', groupId: 'daily' },
      { id: 'income_part_time', name: '兼职收入', icon: '💼', color: '#9C27B0', type: 'income', groupId: 'daily' },
      { id: 'income_gift', name: '礼金红包', icon: '🧧', color: '#F44336', type: 'income', groupId: 'daily' },
      { id: 'income_other', name: '其他收入', icon: '💎', color: '#00BCD4', type: 'income', groupId: 'daily' }
    ];
  }

  // 清空所有数据
  static clearAllData() {
    try {
      uni.clearStorageSync();
    } catch (e) {
      console.error('清空数据失败:', e);
    }
  }

  // 家庭账本管理
  static getFamilyBooks() {
    return this.get("family_books", []);
  }
  static saveFamilyBooks(list) {
    this.set("family_books", list);
  }

  // 家庭成员管理
  static getFamilyMembers() {
    return this.get("family_members", []);
  }
  static saveFamilyMembers(list) {
    this.set("family_members", list);
  }

  // 分摊记录管理
  static getSplitRecords() {
    return this.get("split_records", []);
  }
  static saveSplitRecords(list) {
    this.set("split_records", list);
  }

  // 投资记录管理
  static getInvestments() {
    return this.get("investments", []);
  }
  static saveInvestments(list) {
    this.set("investments", list);
  }

  // 借债记录管理
  static getDebts() {
    return this.get("debts", []);
  }
  static saveDebts(list) {
    this.set("debts", list);
  }

  // 还款记录管理
  static getRepayments() {
    return this.get("repayments", []);
  }
  static saveRepayments(list) {
    this.set("repayments", list);
  }

  // 报销记录管理
  static getReimbursements() {
    return this.get("reimbursements", []);
  }
  static saveReimbursements(list) {
    this.set("reimbursements", list);
  }

  // 周期记账模板管理
  static getRecurringTemplates() {
    return this.get("recurring_templates", []);
  }
  static saveRecurringTemplates(list) {
    this.set("recurring_templates", list);
  }

  // OCR识别结果管理
  static getOCRResults() {
    return this.get("ocr_results", []);
  }
  static saveOCRResults(list) {
    this.set("ocr_results", list);
  }

  // 当前用户设置
  static getCurrentUser() {
    return this.get("current_user", {
      id: 'default_user',
      name: '我',
      avatar: '',
      currentFamilyBookId: null
    });
  }
  static saveCurrentUser(user) {
    this.set("current_user", user);
  }

  // 应用设置
  static getAppSettings() {
    return this.get("app_settings", {
      theme: 'light', // light, dark
      currency: 'CNY',
      language: 'zh-CN',
      enableBiometric: false,
      enableNotification: true,
      autoSync: true,
      syncInterval: 300000, // 5分钟
      enableOCR: true,
      enableVoice: true
    });
  }
  static saveAppSettings(settings) {
    this.set("app_settings", settings);
  }

  // 获取默认投资类型
  static getDefaultInvestmentTypes() {
    return [
      { id: 'stock', name: '股票', icon: '📈', color: '#4CAF50' },
      { id: 'fund', name: '基金', icon: '📊', color: '#2196F3' },
      { id: 'bond', name: '债券', icon: '📋', color: '#FF9800' },
      { id: 'crypto', name: '数字货币', icon: '₿', color: '#FF5722' },
      { id: 'real_estate', name: '房地产', icon: '🏠', color: '#795548' },
      { id: 'gold', name: '黄金', icon: '🥇', color: '#FFC107' },
      { id: 'other', name: '其他', icon: '💎', color: '#9C27B0' }
    ];
  }

  // 获取默认货币列表
  static getDefaultCurrencies() {
    return [
      { code: 'CNY', name: '人民币', symbol: '¥' },
      { code: 'USD', name: '美元', symbol: '$' },
      { code: 'EUR', name: '欧元', symbol: '€' },
      { code: 'JPY', name: '日元', symbol: '¥' },
      { code: 'GBP', name: '英镑', symbol: '£' },
      { code: 'HKD', name: '港币', symbol: 'HK$' },
      { code: 'KRW', name: '韩元', symbol: '₩' }
    ];
  }

  // 获取默认主题列表
  static getDefaultThemes() {
    return [
      {
        id: 'light',
        name: '浅色主题',
        primaryColor: '#007AFF',
        backgroundColor: '#FFFFFF',
        textColor: '#000000'
      },
      {
        id: 'dark',
        name: '深色主题',
        primaryColor: '#0A84FF',
        backgroundColor: '#1C1C1E',
        textColor: '#FFFFFF'
      },
      {
        id: 'green',
        name: '绿色主题',
        primaryColor: '#4CAF50',
        backgroundColor: '#FFFFFF',
        textColor: '#000000'
      },
      {
        id: 'purple',
        name: '紫色主题',
        primaryColor: '#9C27B0',
        backgroundColor: '#FFFFFF',
        textColor: '#000000'
      }
    ];
  }

  // ==================== 进阶功能存储方法 ====================
  
  // 借债管理
  static getDebts() {
    return this.get("debts", []);
  }
  static saveDebts(list) {
    this.set("debts", list);
  }
  static async addDebt(debt) {
    const debts = this.getDebts();
    debts.push(debt);
    this.saveDebts(debts);
    return debt;
  }
  static async updateDebt(debtId, updates) {
    const debts = this.getDebts();
    const index = debts.findIndex(d => d.id === debtId);
    if (index !== -1) {
      debts[index] = { ...debts[index], ...updates, updatedAt: Date.now() };
      this.saveDebts(debts);
      return debts[index];
    }
    return null;
  }
  static async deleteDebt(debtId) {
    const debts = this.getDebts();
    const filteredDebts = debts.filter(d => d.id !== debtId);
    this.saveDebts(filteredDebts);
  }

  // 报销管理
  static getReimbursements() {
    return this.get("reimbursements", []);
  }
  static saveReimbursements(list) {
    this.set("reimbursements", list);
  }
  static async addReimbursement(reimbursement) {
    const reimbursements = this.getReimbursements();
    reimbursements.push(reimbursement);
    this.saveReimbursements(reimbursements);
    return reimbursement;
  }
  static async updateReimbursement(reimbursementId, updates) {
    const reimbursements = this.getReimbursements();
    const index = reimbursements.findIndex(r => r.id === reimbursementId);
    if (index !== -1) {
      reimbursements[index] = { ...reimbursements[index], ...updates, updatedAt: Date.now() };
      this.saveReimbursements(reimbursements);
      return reimbursements[index];
    }
    return null;
  }
  static async deleteReimbursement(reimbursementId) {
    const reimbursements = this.getReimbursements();
    const filteredReimbursements = reimbursements.filter(r => r.id !== reimbursementId);
    this.saveReimbursements(filteredReimbursements);
  }

  // 家庭账本管理
  static getFamilyBooks() {
    return this.get("family_books", []);
  }
  static saveFamilyBooks(list) {
    this.set("family_books", list);
  }
  static async addFamilyBook(familyBook) {
    const familyBooks = this.getFamilyBooks();
    familyBooks.push(familyBook);
    this.saveFamilyBooks(familyBooks);
    return familyBook;
  }

  // 投资管理
  static getInvestments() {
    return this.get("investments", []);
  }
  static saveInvestments(list) {
    this.set("investments", list);
  }
  static async addInvestment(investment) {
    const investments = this.getInvestments();
    investments.push(investment);
    this.saveInvestments(investments);
    return investment;
  }

  // 周期记账模板
  static getRecurringTemplates() {
    return this.get("recurring_templates", []);
  }
  static saveRecurringTemplates(list) {
    this.set("recurring_templates", list);
  }
  static async addRecurringTemplate(template) {
    const templates = this.getRecurringTemplates();
    templates.push(template);
    this.saveRecurringTemplates(templates);
    return template;
  }
}
