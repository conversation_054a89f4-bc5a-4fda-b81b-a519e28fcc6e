
.test-page[data-v-27778e35] {
  min-height: 100vh;
  background: #f5f5f5;
}
.custom-navbar[data-v-27778e35] {
  padding-top: var(--status-bar-height);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.navbar-content[data-v-27778e35] {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.navbar-left[data-v-27778e35] {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.icon-back[data-v-27778e35] {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}
.navbar-title[data-v-27778e35] {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}
.navbar-right[data-v-27778e35] {
  width: 60px;
}
.content[data-v-27778e35] {
  padding: 20px;
}
.section-title[data-v-27778e35] {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}
.status-section[data-v-27778e35] {
  margin-bottom: 30px;
}
.status-card[data-v-27778e35] {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.status-item[data-v-27778e35] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.status-item[data-v-27778e35]:last-child {
  margin-bottom: 0;
}
.status-label[data-v-27778e35] {
  font-size: 14px;
  color: #666;
}
.status-value[data-v-27778e35] {
  font-size: 14px;
  font-weight: 500;
}
.status-value.success[data-v-27778e35] {
  color: #4CAF50;
}
.status-value.error[data-v-27778e35] {
  color: #FF5722;
}
.status-value.warning[data-v-27778e35] {
  color: #FF9800;
}
.test-section[data-v-27778e35] {
  margin-bottom: 30px;
}
.test-btn[data-v-27778e35] {
  width: 100%;
  height: 45px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.test-btn[data-v-27778e35]:active {
  transform: scale(0.98);
  background: #f5f5f5;
}
.test-btn.danger[data-v-27778e35] {
  background: #fff5f5;
  border-color: #ffcdd2;
  color: #d32f2f;
}
.result-section[data-v-27778e35] {
  margin-bottom: 30px;
}
.result-card[data-v-27778e35] {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.result-content[data-v-27778e35] {
  height: 300px;
  padding: 15px;
}
.log-item[data-v-27778e35] {
  display: flex;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}
.log-time[data-v-27778e35] {
  font-size: 12px;
  color: #999;
  width: 60px;
  flex-shrink: 0;
  margin-right: 10px;
}
.log-message[data-v-27778e35] {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}
.log-message.info[data-v-27778e35] {
  color: #333;
}
.log-message.success[data-v-27778e35] {
  color: #4CAF50;
}
.log-message.error[data-v-27778e35] {
  color: #FF5722;
}
.log-message.warning[data-v-27778e35] {
  color: #FF9800;
}
.no-logs[data-v-27778e35] {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 50px 0;
}
.clear-btn[data-v-27778e35] {
  width: 100%;
  height: 40px;
  background: #f5f5f5;
  border: none;
  border-top: 1px solid #e0e0e0;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}
.clear-btn[data-v-27778e35]:active {
  background: #e0e0e0;
}
