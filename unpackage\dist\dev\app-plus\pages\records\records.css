
.page[data-v-cb371200] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-cb371200] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-cb371200] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-cb371200] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-cb371200] {
	font-size: 1.125rem;
	font-weight: bold;
}
.header-action[data-v-cb371200] {
	font-size: 0.875rem;
}

/* 筛选栏 */
.filter-bar[data-v-cb371200] {
	background: white;
	padding: 0.625rem 1.25rem;
	display: flex;
	align-items: center;
	border-bottom: 0.03125rem solid #eee;
}
.filter-item[data-v-cb371200] {
	margin-right: 1.25rem;
	padding: 0.3125rem 0.625rem;
	background: #f8f9fa;
	border-radius: 0.625rem;
}
.filter-text[data-v-cb371200] {
	font-size: 0.8125rem;
	color: #333;
}
.filter-reset[data-v-cb371200] {
	margin-left: auto;
	color: #667eea;
	font-size: 0.8125rem;
}

/* 统计卡片 */
.summary-card[data-v-cb371200] {
	background: white;
	margin: 0.625rem 1.25rem;
	border-radius: 0.625rem;
	padding: 0.9375rem;
	display: flex;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.summary-item[data-v-cb371200] {
	flex: 1;
	text-align: center;
}
.summary-label[data-v-cb371200] {
	font-size: 0.8125rem;
	color: #666;
	display: block;
	margin-bottom: 0.25rem;
}
.summary-value[data-v-cb371200] {
	font-size: 1rem;
	font-weight: bold;
	display: block;
}
.summary-value.expense[data-v-cb371200] {
	color: #ff4757;
}
.summary-value.income[data-v-cb371200] {
	color: #2ed573;
}
.summary-divider[data-v-cb371200] {
	width: 0.0625rem;
	background-color: #eee;
	margin: 0 0.9375rem;
}

/* 记录容器 */
.records-container[data-v-cb371200] {
	margin: 0 1.25rem 1.25rem;
}
.empty[data-v-cb371200] {
	background: white;
	border-radius: 0.625rem;
	padding: 2.5rem 1.25rem;
	text-align: center;
}
.empty-text[data-v-cb371200] {
	color: #999;
	font-size: 0.875rem;
}

/* 日期分组 */
.date-group[data-v-cb371200] {
	margin-bottom: 0.9375rem;
}
.date-header[data-v-cb371200] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem 0;
}
.date-text[data-v-cb371200] {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
}
.date-amount[data-v-cb371200] {
	font-size: 0.75rem;
	color: #666;
}

/* 记录列表 */
.records-list[data-v-cb371200] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
}
.record-item[data-v-cb371200] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.record-item[data-v-cb371200]:last-child {
	border-bottom: none;
}
.record-item[data-v-cb371200]:active {
	background-color: #f8f9fa;
}
.record-left[data-v-cb371200] {
	flex: 1;
}
.record-category[data-v-cb371200] {
	display: flex;
	align-items: center;
	margin-bottom: 0.25rem;
}
.category-icon[data-v-cb371200] {
	font-size: 1rem;
	margin-right: 0.5rem;
}
.category-name[data-v-cb371200] {
	font-size: 1rem;
	font-weight: 500;
	color: #333;
}
.record-note[data-v-cb371200] {
	font-size: 0.8125rem;
	color: #666;
	margin-bottom: 0.25rem;
	display: block;
}
.record-time[data-v-cb371200] {
	font-size: 0.75rem;
	color: #999;
}
.record-right[data-v-cb371200] {
	text-align: right;
}
.record-amount[data-v-cb371200] {
	font-size: 1rem;
	font-weight: bold;
}
.record-amount.expense[data-v-cb371200] {
	color: #ff4757;
}
.record-amount.income[data-v-cb371200] {
	color: #2ed573;
}
