{"bsonType": "object", "required": ["name", "type", "group_id"], "properties": {"name": {"bsonType": "string", "description": "分类名称"}, "type": {"bsonType": "string", "enum": ["expense", "income", "reimburse"], "description": "分类类型"}, "icon": {"bsonType": "string", "description": "图标"}, "group_id": {"bsonType": "string", "description": "分组ID: daily/company"}, "order": {"bsonType": "int", "description": "排序", "default": 0}, "created_at": {"bsonType": "date", "default": {"$func": "now"}}, "updated_at": {"bsonType": "date", "default": {"$func": "now"}}, "is_deleted": {"bsonType": "bool", "default": false}}, "uniCloud": {"indexes": {"idx_name": {"fields": {"name": 1}}}}}