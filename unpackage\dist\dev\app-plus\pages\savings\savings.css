
.page[data-v-993f3c72] {
	background-color: #f5f5f5;
	min-height: 100vh;
}
.header[data-v-993f3c72] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-content[data-v-993f3c72] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-993f3c72], .header-action[data-v-993f3c72] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-993f3c72] {
	font-size: 1.125rem;
	font-weight: bold;
}
.overview-section[data-v-993f3c72] {
	margin: -0.625rem 1.25rem 1.25rem;
}
.overview-card[data-v-993f3c72] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	display: flex;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.overview-item[data-v-993f3c72] {
	flex: 1;
	text-align: center;
}
.overview-value[data-v-993f3c72] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.overview-label[data-v-993f3c72] {
	font-size: 0.75rem;
	color: #666;
}
.overview-divider[data-v-993f3c72] {
	width: 0.03125rem;
	background: #f0f0f0;
	margin: 0 0.9375rem;
}
.goals-section[data-v-993f3c72] {
	margin: 0 1.25rem;
}
.empty-state[data-v-993f3c72] {
	background: white;
	border-radius: 0.625rem;
	padding: 3.125rem 1.25rem;
	text-align: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.empty-icon[data-v-993f3c72] {
	font-size: 2.5rem;
	display: block;
	margin-bottom: 0.625rem;
}
.empty-text[data-v-993f3c72] {
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 1.25rem;
}
.add-goal-btn[data-v-993f3c72] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 1.5625rem;
	padding: 0.75rem 1.5rem;
	font-size: 0.875rem;
	font-weight: bold;
}
.goals-list[data-v-993f3c72] {
	display: flex;
	flex-direction: column;
	gap: 0.9375rem;
}
.goal-item[data-v-993f3c72] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.goal-header[data-v-993f3c72] {
	display: flex;
	align-items: center;
	margin-bottom: 0.9375rem;
}
.goal-icon[data-v-993f3c72] {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 1.25rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.75rem;
}
.icon-text[data-v-993f3c72] {
	font-size: 1rem;
}
.goal-info[data-v-993f3c72] {
	flex: 1;
}
.goal-name[data-v-993f3c72] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.goal-deadline[data-v-993f3c72] {
	font-size: 0.75rem;
	color: #666;
}
.action-btn[data-v-993f3c72] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 0.5rem 1rem;
	border-radius: 0.625rem;
	font-size: 0.75rem;
	font-weight: bold;
}
.goal-progress[data-v-993f3c72] {
	margin-bottom: 0.625rem;
}
.progress-info[data-v-993f3c72] {
	display: flex;
	align-items: baseline;
	margin-bottom: 0.5rem;
}
.current-amount[data-v-993f3c72] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.target-amount[data-v-993f3c72] {
	font-size: 0.875rem;
	color: #666;
	margin-left: 0.25rem;
}
.progress-bar[data-v-993f3c72] {
	height: 0.375rem;
	background: #f0f0f0;
	border-radius: 0.1875rem;
	overflow: hidden;
	margin-bottom: 0.5rem;
}
.progress-fill[data-v-993f3c72] {
	height: 100%;
	border-radius: 0.1875rem;
	transition: width 0.3s;
}
.progress-stats[data-v-993f3c72] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.progress-percent[data-v-993f3c72] {
	font-size: 0.75rem;
	font-weight: bold;
	color: #4CAF50;
}
.remaining-amount[data-v-993f3c72] {
	font-size: 0.75rem;
	color: #666;
}
.popup-overlay[data-v-993f3c72] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.popup-content[data-v-993f3c72] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	width: 100%;
	max-height: 80vh;
}
.popup-header[data-v-993f3c72] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-993f3c72] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-993f3c72] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-section[data-v-993f3c72] {
	margin-bottom: 1.25rem;
}
.form-item[data-v-993f3c72] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-993f3c72] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-input[data-v-993f3c72] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
}
.date-picker[data-v-993f3c72] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
}
.picker-text[data-v-993f3c72] {
	font-size: 0.875rem;
	color: #333;
}
.popup-actions[data-v-993f3c72] {
	display: flex;
	gap: 0.625rem;
}
.cancel-btn[data-v-993f3c72], .save-btn[data-v-993f3c72], .confirm-btn[data-v-993f3c72] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.cancel-btn[data-v-993f3c72] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-993f3c72], .confirm-btn[data-v-993f3c72] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
