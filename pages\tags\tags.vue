<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">标签管理</text>
				<text class="header-action" @click="addTag">+</text>
			</view>
		</view>
		
		<!-- 标签统计 -->
		<view class="stats-section">
			<view class="stats-card">
				<view class="stats-item">
					<text class="stats-number">{{tags.length}}</text>
					<text class="stats-label">标签总数</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">{{usedTagsCount}}</text>
					<text class="stats-label">已使用</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">{{totalUsage}}</text>
					<text class="stats-label">使用次数</text>
				</view>
			</view>
		</view>
		
		<!-- 标签列表 -->
		<view class="tags-section">
			<view v-if="tags.length === 0" class="empty-state">
				<text class="empty-icon">🏷️</text>
				<text class="empty-text">暂无标签，点击右上角添加</text>
			</view>
			
			<view v-else class="tags-list">
				<view 
					v-for="tag in sortedTags" 
					:key="tag.id"
					class="tag-item"
					@click="editTag(tag)"
				>
					<view class="tag-left">
						<view class="tag-icon" :style="{backgroundColor: tag.color}">
							<text class="icon-text">{{tag.icon}}</text>
						</view>
						<view class="tag-info">
							<text class="tag-name">{{tag.name}}</text>
							<text class="tag-usage">使用 {{tag.usageCount}} 次</text>
						</view>
					</view>
					<view class="tag-right">
						<text class="tag-arrow">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加/编辑标签弹窗 -->
		<view v-if="showPopup" class="popup-overlay" @click="closePopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{editingTag ? '编辑标签' : '添加标签'}}</text>
					<text class="popup-close" @click="closePopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">标签名称</text>
						<input 
							class="form-input" 
							v-model="tagForm.name" 
							placeholder="请输入标签名称"
							maxlength="10"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">选择图标</text>
						<view class="icon-selector">
							<view 
								v-for="icon in tagIcons" 
								:key="icon"
								class="icon-item"
								:class="{active: tagForm.icon === icon}"
								@click="selectIcon(icon)"
							>
								<text class="icon-text">{{icon}}</text>
							</view>
						</view>
					</view>
					
					<view class="form-item">
						<text class="form-label">选择颜色</text>
						<view class="color-selector">
							<view 
								v-for="color in tagColors" 
								:key="color"
								class="color-item"
								:class="{active: tagForm.color === color}"
								:style="{backgroundColor: color}"
								@click="selectColor(color)"
							></view>
						</view>
					</view>
				</view>
				
				<view class="popup-actions">
					<button v-if="editingTag" class="delete-btn" @click="deleteTag">删除</button>
					<button class="cancel-btn" @click="closePopup">取消</button>
					<button class="save-btn" @click="saveTag">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createTag, validateTag } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			tags: [],
			showPopup: false,
			editingTag: null,
			tagForm: {
				name: '',
				icon: '🏷️',
				color: '#4CAF50'
			},
			tagIcons: [
				'🏷️', '📌', '⭐', '❤️', '🔥', '💡', '🎯', '📝', '🎨',
				'🍕', '🚗', '🏠', '💼', '🎮', '📚', '🎵', '🏃', '✈️'
			],
			tagColors: [
				'#4CAF50', '#2196F3', '#FF9800', '#F44336', '#9C27B0',
				'#607D8B', '#795548', '#FF5722', '#3F51B5', '#009688',
				'#E91E63', '#FFEB3B', '#00BCD4', '#8BC34A', '#FFC107'
			]
		};
	},
	computed: {
		sortedTags() {
			return [...this.tags].sort((a, b) => b.usageCount - a.usageCount);
		},
		usedTagsCount() {
			return this.tags.filter(tag => tag.usageCount > 0).length;
		},
		totalUsage() {
			return this.tags.reduce((sum, tag) => sum + tag.usageCount, 0);
		}
	},
	onLoad() {
		this.loadTags();
	},
	methods: {
		loadTags() {
			this.tags = StorageManager.getTags();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		addTag() {
			this.editingTag = null;
			this.resetForm();
			this.showPopup = true;
		},
		
		editTag(tag) {
			this.editingTag = tag;
			this.tagForm = {
				name: tag.name,
				icon: tag.icon,
				color: tag.color
			};
			this.showPopup = true;
		},
		
		closePopup() {
			this.showPopup = false;
		},
		
		resetForm() {
			this.tagForm = {
				name: '',
				icon: '🏷️',
				color: '#4CAF50'
			};
		},
		
		selectIcon(icon) {
			this.tagForm.icon = icon;
		},
		
		selectColor(color) {
			this.tagForm.color = color;
		},
		
		saveTag() {
			// 验证表单
			const validation = validateTag(this.tagForm);
			if (!validation.valid) {
				uni.showToast({
					title: validation.errors[0],
					icon: 'none'
				});
				return;
			}
			
			// 检查标签名是否重复
			const existingTag = this.tags.find(tag => 
				tag.name === this.tagForm.name.trim() && 
				(!this.editingTag || tag.id !== this.editingTag.id)
			);
			
			if (existingTag) {
				uni.showToast({
					title: '标签名称已存在',
					icon: 'none'
				});
				return;
			}
			
			if (this.editingTag) {
				// 编辑标签
				const index = this.tags.findIndex(t => t.id === this.editingTag.id);
				if (index !== -1) {
					this.tags[index] = {
						...this.editingTag,
						name: this.tagForm.name.trim(),
						icon: this.tagForm.icon,
						color: this.tagForm.color,
						updatedAt: Date.now()
					};
				}
			} else {
				// 添加新标签
				const newTag = createTag({
					name: this.tagForm.name.trim(),
					icon: this.tagForm.icon,
					color: this.tagForm.color
				});
				this.tags.push(newTag);
			}
			
			// 保存到本地存储
			StorageManager.saveTags(this.tags);
			
			this.closePopup();
			
			uni.showToast({
				title: this.editingTag ? '标签已更新' : '标签已添加',
				icon: 'success'
			});
		},
		
		deleteTag() {
			if (!this.editingTag) return;
			
			uni.showModal({
				title: '确认删除',
				content: '删除标签后，相关记录的标签信息也会被移除，确定要删除吗？',
				success: (res) => {
					if (res.confirm) {
						// 从标签列表中删除
						const index = this.tags.findIndex(t => t.id === this.editingTag.id);
						if (index !== -1) {
							this.tags.splice(index, 1);
							StorageManager.saveTags(this.tags);
						}
						
						// 从所有记录中移除该标签
						this.removeTagFromRecords(this.editingTag.id);
						
						this.closePopup();
						
						uni.showToast({
							title: '标签已删除',
							icon: 'success'
						});
					}
				}
			});
		},
		
		removeTagFromRecords(tagId) {
			const records = StorageManager.getRecords();
			const updatedRecords = records.map(record => ({
				...record,
				tags: record.tags ? record.tags.filter(id => id !== tagId) : []
			}));
			StorageManager.saveRecords(updatedRecords);
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 20rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 统计卡片 */
.stats-section {
	margin: 20rpx 40rpx;
}

.stats-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	justify-content: space-around;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
	text-align: center;
}

.stats-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
}

/* 标签列表 */
.tags-section {
	margin: 0 40rpx;
}

.empty-state {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.tags-list {
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
}

.tag-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.tag-item:last-child {
	border-bottom: none;
}

.tag-item:active {
	background-color: #f8f9fa;
}

.tag-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.tag-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.icon-text {
	font-size: 24rpx;
}

.tag-info {
	flex: 1;
}

.tag-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.tag-usage {
	font-size: 26rpx;
	color: #666;
}

.tag-arrow {
	font-size: 28rpx;
	color: #ccc;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

/* 图标选择器 */
.icon-selector {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	gap: 16rpx;
}

.icon-item {
	width: 80rpx;
	height: 80rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}

.icon-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	transform: scale(1.1);
}

.icon-item .icon-text {
	font-size: 32rpx;
}

/* 颜色选择器 */
.color-selector {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 16rpx;
}

.color-item {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	border: 4rpx solid transparent;
	transition: all 0.3s;
}

.color-item.active {
	border-color: #333;
	transform: scale(1.2);
}

/* 操作按钮 */
.popup-actions {
	display: flex;
	gap: 20rpx;
}

.delete-btn, .cancel-btn, .save-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.delete-btn {
	background: #ff4757;
	color: white;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
</style>
