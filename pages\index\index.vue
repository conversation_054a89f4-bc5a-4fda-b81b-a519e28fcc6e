<template>
	<view class="page" :style="{ backgroundColor: themeColors.background }">
		<!-- 头部 -->
		<view class="header" :style="{ background: themeColors.gradient }">
			<text class="header-title">{{title}}</text>
			<text class="help-btn" @click="showQuickGuide" :style="{ color: themeColors.textReverse }">❓</text>
		</view>
		
		<!-- 统计卡片 -->
		<view class="stats-card" :style="{ backgroundColor: themeColors.backgroundCard }">
			<view class="stat-item">
				<text class="stat-label">本月支出</text>
				<text class="stat-value expense">¥{{monthExpense.toFixed(2)}}</text>
			</view>
			<view class="stat-divider"></view>
			<view class="stat-item">
				<text class="stat-label">本月收入</text>
				<text class="stat-value income">¥{{monthIncome.toFixed(2)}}</text>
			</view>
			<view class="stat-divider"></view>
			<view class="stat-item">
				<text class="stat-label">本月结余</text>
				<text class="stat-value" :class="monthBalance >= 0 ? 'income' : 'expense'">¥{{monthBalance.toFixed(2)}}</text>
			</view>
		</view>
		
		<!-- 账户总览 -->
		<view class="accounts-overview">
			<view class="section-header">
				<text class="section-title">账户总览</text>
				<text class="view-all" @click="manageAccounts">管理</text>
			</view>
			<view class="accounts-summary">
				<view class="summary-item">
					<text class="summary-label">总资产</text>
					<text class="summary-value">¥{{totalAssets.toFixed(2)}}</text>
				</view>
				<view class="summary-item">
					<text class="summary-label">账户数</text>
					<text class="summary-value">{{accounts.length}}</text>
				</view>
			</view>
			<view class="accounts-list">
				<view v-for="account in topAccounts" :key="account.id" class="account-item" @click="viewAccountDetail(account)">
					<view class="account-icon" :style="{backgroundColor: account.color}">
						<text class="icon-text">{{account.icon}}</text>
					</view>
					<view class="account-info">
						<text class="account-name">{{account.name}}</text>
						<text class="account-balance">¥{{account.balance.toFixed(2)}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 快速操作 -->
		<view class="quick-actions">
			<view class="action-item" @click="quickExpense">
				<text class="action-icon">💸</text>
				<text class="action-text">快速支出</text>
			</view>
			<view class="action-item" @click="quickIncome">
				<text class="action-icon">💰</text>
				<text class="action-text">快速收入</text>
			</view>
			<view class="action-item" @click="viewReport">
				<text class="action-icon">📈</text>
				<text class="action-text">统计报表</text>
			</view>
			<view class="action-item" @click="searchRecords">
				<text class="action-icon">🔍</text>
				<text class="action-text">搜索记录</text>
			</view>
			<view class="action-item" @click="manageAccounts">
				<text class="action-icon">🏦</text>
				<text class="action-text">账户管理</text>
			</view>
			<view class="action-item" @click="manageBudget">
				<text class="action-icon">📊</text>
				<text class="action-text">预算管理</text>
			</view>
			<view class="action-item" @click="manageSavings">
				<text class="action-icon">🎯</text>
				<text class="action-text">储蓄目标</text>
			</view>
			<view class="action-item" @click="manageReminders">
				<text class="action-icon">⏰</text>
				<text class="action-text">账单提醒</text>
			</view>
			<view class="action-item" @click="manageFamily">
				<text class="action-icon">👨‍👩‍👧‍👦</text>
				<text class="action-text">家庭账本</text>
			</view>
			<view class="action-item" @click="manageInvestment">
				<text class="action-icon">📊</text>
				<text class="action-text">投资追踪</text>
			</view>
			<view class="action-item" @click="manageDebt">
				<text class="action-icon">💳</text>
				<text class="action-text">借债管理</text>
			</view>
			<view class="action-item" @click="manageReimbursement">
				<text class="action-icon">🧾</text>
				<text class="action-text">报销管理</text>
			</view>
			<view class="action-item" @click="manageRecurring">
				<text class="action-icon">🔄</text>
				<text class="action-text">周期记账</text>
			</view>
		</view>
		
		<!-- 记录列表 -->
		<view class="records-section">
			<view class="section-header">
				<text class="section-title">最近记录</text>
				<text class="view-all" @click="viewAllRecords">查看全部</text>
			</view>
			
			<view v-if="records.length === 0" class="empty">
				<text class="empty-text">暂无记录，点击右下角开始记账</text>
			</view>
			
			<view v-else class="records-list">
				<view v-for="record in displayRecords" :key="record.id" class="record-item" @click="viewRecord(record)">
					<view class="record-left">
						<view class="record-category">
							<text class="category-icon">{{getCategoryIcon(record.categoryId)}}</text>
							<text class="category-name">{{getCategoryName(record.categoryId)}}</text>
						</view>
						<text class="record-note" v-if="record.note">{{record.note}}</text>
						<text class="record-time">{{formatTime(record.date)}}</text>
					</view>
					<view class="record-right">
						<text class="record-amount" :class="record.type === 'expense' ? 'expense' : 'income'">
							{{record.type === 'expense' ? '-' : '+'}}¥{{record.amount.toFixed(2)}}
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 悬浮记账按钮 -->
		<view class="fab" @click="startRecord">
			<text class="fab-icon">+</text>
		</view>
	</view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import ThemeManager from '@/utils/themeManager.js';

export default {
  data() {
    return {
      title: '碎米记账',
      records: [],
      categories: [],
      accounts: [],
      themeColors: ThemeManager.getColors()
    };
  },
  computed: {
    // 账户总资产
    totalAssets() {
      return this.accounts.reduce((total, account) => {
        if (account.type === 'credit_card' || account.type === 'loan') {
          return total - Math.abs(account.balance);
        }
        return total + account.balance;
      }, 0);
    },
    // 显示前3个账户
    topAccounts() {
      return this.accounts.slice(0, 3);
    },
    // 显示最近10条记录
    displayRecords() {
      return this.records.slice(0, 10);
    },
    // 本月支出
    monthExpense() {
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      
      return this.records
        .filter(record => {
          const recordDate = new Date(record.date);
          return record.type === 'expense' && 
                 recordDate.getMonth() === currentMonth && 
                 recordDate.getFullYear() === currentYear;
        })
        .reduce((sum, record) => sum + record.amount, 0);
    },
    // 本月收入
    monthIncome() {
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      
      return this.records
        .filter(record => {
          const recordDate = new Date(record.date);
          return record.type === 'income' && 
                 recordDate.getMonth() === currentMonth && 
                 recordDate.getFullYear() === currentYear;
        })
        .reduce((sum, record) => sum + record.amount, 0);
    },
    // 本月结余
    monthBalance() {
      return this.monthIncome - this.monthExpense;
    }
  },
  onShow() {
    this.loadData();
  },
  onLoad() {
    this.loadData();
    // 设置主题监听器
    ThemeManager.addListener(this.onThemeChange);
  },
  
  onUnload() {
    // 移除主题监听器
    ThemeManager.removeListener(this.onThemeChange);
  },
  methods: {
    loadData() {
      this.records = StorageManager.getRecords().slice(0, 10);
      this.categories = StorageManager.getCategories();
      this.accounts = StorageManager.getAccounts();
    },
    startRecord() {
      uni.navigateTo({ url: '/pages/add-record/add-record' });
    },
    viewAllRecords() {
      uni.navigateTo({ url: '/pages/records/records' });
    },
    viewRecord(record) {
      uni.navigateTo({ 
        url: `/pages/add-record/add-record?id=${record.id}&mode=view` 
      });
    },
    // 快速支出
    quickExpense() {
      uni.navigateTo({ 
        url: '/pages/add-record/add-record?type=expense' 
      });
    },
    // 快速收入
    quickIncome() {
      uni.navigateTo({ 
        url: '/pages/add-record/add-record?type=income' 
      });
    },
    // 查看报表
    viewReport() {
      uni.switchTab({ url: '/pages/report/report' });
    },
    // 搜索记录
    searchRecords() {
      uni.navigateTo({ url: '/pages/search/search' });
    },
    // 账户管理
    manageAccounts() {
      uni.navigateTo({ url: '/pages/accounts/accounts' });
    },
    // 预算管理
    manageBudget() {
      uni.navigateTo({ url: '/pages/budget/budget' });
    },
    // 储蓄目标管理
    manageSavings() {
      uni.navigateTo({ url: '/pages/savings/savings' });
    },
    // 账单提醒管理
    manageReminders() {
      uni.navigateTo({ url: '/pages/reminders/reminders' });
    },
    // 家庭账本管理
    manageFamily() {
      uni.navigateTo({ url: '/pages/family/family' });
    },
    // 投资追踪管理
    manageInvestment() {
      uni.navigateTo({ url: '/pages/investment/investment' });
    },
    // 借债管理
    manageDebt() {
      uni.navigateTo({ url: '/pages/debt/debt' });
    },
    // 报销管理
    manageReimbursement() {
      uni.navigateTo({ url: '/pages/reimbursement/reimbursement' });
    },
    // 周期记账管理
    manageRecurring() {
      uni.navigateTo({ url: '/pages/recurring/recurring' });
    },
    // 查看账户详情
    viewAccountDetail(account) {
      uni.navigateTo({ url: `/pages/account-detail/account-detail?id=${account.id}` });
    },
    getCategoryName(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.name : '未知分类';
    },
    getCategoryIcon(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.icon : '💰';
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      
      if (date >= today) {
        return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } else if (date >= yesterday) {
        return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    },
    getDefaultCategories() {
      return [
        // 支出分类
        { id: 'food', name: '餐饮', icon: '🍽️', type: 'expense', groupId: 'daily' },
        { id: 'transport', name: '交通', icon: '🚗', type: 'expense', groupId: 'daily' },
        { id: 'shopping', name: '购物', icon: '🛍️', type: 'expense', groupId: 'daily' },
        { id: 'entertainment', name: '娱乐', icon: '🎮', type: 'expense', groupId: 'daily' },
        { id: 'medical', name: '医疗', icon: '🏥', type: 'expense', groupId: 'daily' },
        { id: 'education', name: '教育', icon: '📚', type: 'expense', groupId: 'daily' },
        { id: 'housing', name: '住房', icon: '🏠', type: 'expense', groupId: 'daily' },
        { id: 'utilities', name: '水电', icon: '💡', type: 'expense', groupId: 'daily' },
        
        // 收入分类
        { id: 'salary', name: '工资', icon: '💰', type: 'income', groupId: 'daily' },
        { id: 'bonus', name: '奖金', icon: '🎁', type: 'income', groupId: 'daily' },
        { id: 'investment', name: '投资', icon: '📈', type: 'income', groupId: 'daily' },
        { id: 'other_income', name: '其他收入', icon: '💵', type: 'income', groupId: 'daily' },
        
        // 报销分类
        { id: 'business_meal', name: '商务餐饮', icon: '🍽️', type: 'expense', groupId: 'reimbursement' },
        { id: 'business_transport', name: '差旅交通', icon: '✈️', type: 'expense', groupId: 'reimbursement' },
        { id: 'accommodation', name: '住宿费', icon: '🏨', type: 'expense', groupId: 'reimbursement' },
        { id: 'office_supplies', name: '办公用品', icon: '📎', type: 'expense', groupId: 'reimbursement' },
        { id: 'communication', name: '通讯费', icon: '📱', type: 'expense', groupId: 'reimbursement' },
        { id: 'training', name: '培训费', icon: '🎓', type: 'expense', groupId: 'reimbursement' },
        { id: 'entertainment_business', name: '商务招待', icon: '🍷', type: 'expense', groupId: 'reimbursement' },
        { id: 'other_reimbursement', name: '其他报销', icon: '📋', type: 'expense', groupId: 'reimbursement' }
      ];
    },
    
    // 主题变化回调
    onThemeChange(theme) {
      this.themeColors = theme.colors;
    },
    
    // 显示快速操作指南
    showQuickGuide() {
      uni.navigateTo({ url: '/pages/help/quick-guide' });
    }
  }
};
</script>

<style scoped>
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  font-size: 44rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
}

.help-btn {
  font-size: 36rpx;
  padding: 10rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.help-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 统计卡片 */
.stats-card {
  background: white;
  margin: -40rpx 40rpx 40rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
}

.stat-value.expense {
  color: #ff4757;
}

.stat-value.income {
  color: #2ed573;
}

.stat-divider {
  width: 2rpx;
  background-color: #eee;
  margin: 0 30rpx;
}

/* 快速操作 */
.quick-actions {
  margin: 0 40rpx 40rpx;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12rpx;
}

.action-item {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx 8rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.action-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 32rpx;
  display: block;
  margin-bottom: 6rpx;
}

.action-text {
  font-size: 20rpx;
  color: #333;
  font-weight: 500;
}

/* 账户总览 */
.accounts-overview {
  margin: 0 40rpx 40rpx;
}

.accounts-summary {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.accounts-list {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.account-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.account-item:last-child {
  border-bottom: none;
}

.account-item:active {
  background-color: #f8f9fa;
}

.account-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 28rpx;
}

.account-info {
  flex: 1;
}

.account-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.account-balance {
  font-size: 24rpx;
  color: #666;
}

/* 记录区域 */
.records-section {
  margin: 0 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  font-size: 28rpx;
  color: #667eea;
}

.empty {
  background: white;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 记录列表 */
.records-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f8f9fa;
}

.record-left {
  flex: 1;
}

.record-category {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.record-note {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
}

.record-amount.expense {
  color: #ff4757;
}

.record-amount.income {
  color: #2ed573;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
  transition: transform 0.2s;
}

.fab:active {
  transform: scale(0.95);
}

.fab-icon {
  color: white;
  font-size: 48rpx;
  font-weight: 300;
  line-height: 1;
}
</style>
