
.page[data-v-cc45d2ba] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header[data-v-cc45d2ba] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 1.25rem;
	color: white;
}
.header-content[data-v-cc45d2ba] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-cc45d2ba] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-cc45d2ba] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 语音识别区域 */
.voice-section[data-v-cc45d2ba] {
	padding: 2.5rem 1.25rem;
	text-align: center;
}
.voice-container[data-v-cc45d2ba] {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.voice-circle[data-v-cc45d2ba] {
	width: 6.25rem;
	height: 6.25rem;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 1.25rem;
	transition: all 0.3s;
	box-shadow: 0 0.25rem 0.9375rem rgba(102, 126, 234, 0.3);
}
.voice-circle.recording[data-v-cc45d2ba] {
	animation: pulse-cc45d2ba 1s infinite;
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}
.voice-circle.processing[data-v-cc45d2ba] {
	animation: rotate-cc45d2ba 2s linear infinite;
	background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}
@keyframes pulse-cc45d2ba {
0% { transform: scale(1);
}
50% { transform: scale(1.1);
}
100% { transform: scale(1);
}
}
@keyframes rotate-cc45d2ba {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.voice-icon[data-v-cc45d2ba] {
	font-size: 1.875rem;
	color: white;
}
.voice-status[data-v-cc45d2ba] {
	font-size: 1rem;
	color: #333;
	margin-bottom: 1.25rem;
}
.voice-controls[data-v-cc45d2ba] {
	display: flex;
	gap: 0.625rem;
}
.record-btn[data-v-cc45d2ba], .stop-btn[data-v-cc45d2ba], .cancel-btn[data-v-cc45d2ba] {
	padding: 0.75rem 1.5rem;
	border-radius: 1.5625rem;
	font-size: 0.875rem;
	font-weight: bold;
	border: none;
	margin: 0 0.3125rem;
}
.record-btn[data-v-cc45d2ba] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.stop-btn[data-v-cc45d2ba] {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	color: white;
}
.cancel-btn[data-v-cc45d2ba] {
	background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
	color: white;
}

/* 识别结果 */
.result-section[data-v-cc45d2ba] {
	margin: 0 1.25rem 1.25rem;
}
.result-card[data-v-cc45d2ba] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.result-header[data-v-cc45d2ba] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.9375rem;
}
.result-title[data-v-cc45d2ba] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.result-edit[data-v-cc45d2ba] {
	font-size: 0.875rem;
	color: #667eea;
}
.result-content[data-v-cc45d2ba] {
	background: #f8f9fa;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.9375rem;
}
.result-text[data-v-cc45d2ba] {
	font-size: 0.9375rem;
	color: #333;
	line-height: 1.5;
}
.parsed-info[data-v-cc45d2ba] {
	border-top: 0.03125rem solid #f0f0f0;
	padding-top: 0.9375rem;
	margin-bottom: 0.9375rem;
}
.parsed-item[data-v-cc45d2ba] {
	display: flex;
	margin-bottom: 0.625rem;
}
.parsed-label[data-v-cc45d2ba] {
	font-size: 0.875rem;
	color: #666;
	width: 3.75rem;
}
.parsed-value[data-v-cc45d2ba] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
}
.result-actions[data-v-cc45d2ba] {
	display: flex;
	gap: 0.625rem;
}
.retry-btn[data-v-cc45d2ba], .save-btn[data-v-cc45d2ba] {
	flex: 1;
	height: 2.5rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	font-weight: bold;
	border: none;
}
.retry-btn[data-v-cc45d2ba] {
	background: #f8f9fa;
	color: #666;
}
.save-btn[data-v-cc45d2ba] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.save-btn[data-v-cc45d2ba]:disabled {
	background: #ccc;
	color: #999;
}

/* 使用说明 */
.tips-section[data-v-cc45d2ba] {
	margin: 0 1.25rem;
}
.tips-card[data-v-cc45d2ba] {
	background: white;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.tips-header[data-v-cc45d2ba] {
	margin-bottom: 0.9375rem;
}
.tips-title[data-v-cc45d2ba] {
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.tips-content[data-v-cc45d2ba] {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}
.tip-item[data-v-cc45d2ba] {
	font-size: 0.8125rem;
	color: #666;
	line-height: 1.5;
}

/* 弹窗样式 */
.popup-overlay[data-v-cc45d2ba] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.popup-content[data-v-cc45d2ba] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	width: 100%;
	max-height: 80vh;
}
.popup-header[data-v-cc45d2ba] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-cc45d2ba] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-cc45d2ba] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-section[data-v-cc45d2ba] {
	margin-bottom: 1.25rem;
}
.form-item[data-v-cc45d2ba] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-cc45d2ba] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-textarea[data-v-cc45d2ba] {
	width: 100%;
	min-height: 6.25rem;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
	line-height: 1.5;
}
.popup-actions[data-v-cc45d2ba] {
	display: flex;
	gap: 0.625rem;
}
.cancel-btn[data-v-cc45d2ba], .confirm-btn[data-v-cc45d2ba] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
	border: none;
}
.cancel-btn[data-v-cc45d2ba] {
	background: #f8f9fa;
	color: #666;
}
.confirm-btn[data-v-cc45d2ba] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
