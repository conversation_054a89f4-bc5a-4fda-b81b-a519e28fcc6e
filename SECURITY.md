# 碎米记账 - 安全功能说明

## 功能概述

碎米记账应用提供了完整的安全保护功能，包括PIN码解锁和指纹解锁，确保您的财务数据安全。

## 主要功能

### 1. PIN码解锁
- **6位数字PIN码**：设置6位数字作为主要解锁方式
- **加密存储**：PIN码经过加密后存储在本地
- **失败次数限制**：连续输入错误5次后提示用户
- **备用解锁**：作为指纹解锁的备用方式

### 2. 指纹解锁
- **快速解锁**：支持指纹快速解锁应用
- **设备检测**：自动检测设备是否支持指纹识别
- **指纹验证**：检查用户是否已录入指纹
- **依赖PIN码**：必须先设置PIN码才能启用指纹解锁

### 3. 自动锁定
- **后台锁定**：应用进入后台5分钟后自动锁定
- **启动检查**：应用启动时自动检查是否需要解锁
- **恢复检查**：从后台恢复时检查解锁状态

## 使用流程

### 首次设置
1. 进入 **设置 → 安全设置**
2. 点击 **PIN码设置**
3. 输入6位数字PIN码
4. 确认PIN码
5. PIN码设置完成

### 启用指纹解锁
1. 确保已设置PIN码
2. 确保设备支持指纹识别
3. 确保已在系统设置中录入指纹
4. 进入 **安全设置 → 指纹解锁**
5. 点击 **启用指纹解锁**

### 解锁应用
1. **指纹解锁**：轻触指纹传感器
2. **PIN码解锁**：输入6位数字PIN码
3. **切换方式**：可在指纹和PIN码之间切换

## 安全特性

### 数据保护
- PIN码采用异或加密算法存储
- 解锁状态有时效性（5分钟）
- 支持重置安全设置

### 用户体验
- 现代化UI设计
- 流畅的动画效果
- 详细的状态提示
- 友好的错误处理

### 兼容性
- 支持APP-PLUS环境的真实指纹识别
- 非APP环境提供模拟功能用于测试
- 自动检测设备能力

## 页面结构

```
pages/security/
├── security.vue          # 安全设置主页面
├── pin-setup.vue         # PIN码设置页面
├── fingerprint-setup.vue # 指纹设置页面
└── unlock.vue            # 解锁页面
```

## 核心文件

### SecurityManager (utils/securityManager.js)
安全管理器，提供以下核心功能：
- PIN码设置和验证
- 指纹解锁管理
- 设备能力检测
- 自动锁定逻辑

### App.vue
应用入口，负责：
- 启动时安全检查
- 后台恢复时解锁检查
- 自动跳转到解锁页面

## 使用注意事项

### 设置要求
1. **PIN码必须是6位数字**
2. **指纹解锁需要先设置PIN码**
3. **设备必须支持指纹识别**
4. **系统中必须已录入指纹**

### 安全提醒
1. **请妥善保管PIN码**
2. **忘记PIN码需要重置所有安全设置**
3. **重置安全设置不会删除应用数据**
4. **建议定期更换PIN码**

### 故障排除
1. **指纹识别失败**：检查手指是否干净，重新尝试
2. **无法启用指纹**：确认设备支持且已录入指纹
3. **PIN码忘记**：使用"忘记PIN码"功能重置
4. **解锁页面卡死**：重启应用或清除应用数据

## 技术实现

### 加密算法
使用简单的异或加密算法对PIN码进行加密存储：
```javascript
// 加密
encrypted = pin XOR key
// 解密  
decrypted = encrypted XOR key
```

### 指纹识别
- APP-PLUS环境：使用plus.fingerprint API
- 其他环境：提供模拟功能用于开发测试

### 自动锁定
- 记录最后解锁时间
- 检查时间差是否超过5分钟
- 应用启动和恢复时自动检查

## 版本历史

### v1.0.0
- 实现PIN码解锁功能
- 实现指纹解锁功能
- 实现自动锁定机制
- 提供完整的安全设置界面

## 后续计划

1. **支持更多生物识别**：面部识别、声纹识别
2. **增强加密算法**：使用更安全的加密方式
3. **远程锁定**：支持远程锁定设备
4. **安全日志**：记录解锁尝试历史
5. **多重验证**：支持多种验证方式组合
