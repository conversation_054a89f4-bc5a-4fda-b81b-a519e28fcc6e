/**
 * 安全管理器
 * 负责PIN码和指纹解锁功能
 */

class SecurityManager {
  constructor() {
    this.PIN_KEY = 'app_pin_code';
    this.FINGERPRINT_KEY = 'app_fingerprint_enabled';
    this.SECURITY_ENABLED_KEY = 'app_security_enabled';
    this.LAST_UNLOCK_TIME_KEY = 'app_last_unlock_time';
    this.AUTO_LOCK_TIMEOUT = 5 * 60 * 1000; // 5分钟自动锁定
  }

  /**
   * 设置PIN码
   * @param {string} pin - 6位数字PIN码
   * @returns {boolean} 设置是否成功
   */
  setPinCode(pin) {
    try {
      if (!this.validatePin(pin)) {
        return false;
      }
      
      // 使用简单的加密存储PIN码
      const encryptedPin = this.encryptPin(pin);
      uni.setStorageSync(this.PIN_KEY, encryptedPin);
      uni.setStorageSync(this.SECURITY_ENABLED_KEY, true);
      
      return true;
    } catch (error) {
      console.error('设置PIN码失败:', error);
      return false;
    }
  }

  /**
   * 验证PIN码
   * @param {string} pin - 输入的PIN码
   * @returns {boolean} 验证是否成功
   */
  verifyPin(pin) {
    try {
      const storedPin = uni.getStorageSync(this.PIN_KEY);
      if (!storedPin) {
        return false;
      }
      
      const decryptedPin = this.decryptPin(storedPin);
      return pin === decryptedPin;
    } catch (error) {
      console.error('验证PIN码失败:', error);
      return false;
    }
  }

  /**
   * 检查是否已设置PIN码
   * @returns {boolean}
   */
  hasPinCode() {
    try {
      const pin = uni.getStorageSync(this.PIN_KEY);
      return !!pin;
    } catch (error) {
      return false;
    }
  }

  /**
   * 启用指纹解锁
   * @returns {Promise<boolean>}
   */
  async enableFingerprint() {
    try {
      // 检查是否已设置PIN码
      if (!this.hasPinCode()) {
        throw new Error('请先设置PIN码');
      }

      // 检查设备是否支持指纹识别
      const isSupported = await this.isFingerprintSupported();
      if (!isSupported) {
        throw new Error('设备不支持指纹识别');
      }

      // 检查是否已录入指纹
      const hasFingerprint = await this.hasEnrolledFingerprint();
      if (!hasFingerprint) {
        throw new Error('请先在系统设置中录入指纹');
      }

      uni.setStorageSync(this.FINGERPRINT_KEY, true);
      return true;
    } catch (error) {
      console.error('启用指纹解锁失败:', error);
      throw error;
    }
  }

  /**
   * 禁用指纹解锁
   */
  disableFingerprint() {
    try {
      uni.setStorageSync(this.FINGERPRINT_KEY, false);
    } catch (error) {
      console.error('禁用指纹解锁失败:', error);
    }
  }

  /**
   * 检查是否启用了指纹解锁
   * @returns {boolean}
   */
  isFingerprintEnabled() {
    try {
      return uni.getStorageSync(this.FINGERPRINT_KEY) === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 指纹验证
   * @returns {Promise<boolean>}
   */
  async verifyFingerprint() {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      plus.fingerprint.authenticate(() => {
        resolve(true);
      }, (error) => {
        console.error('指纹验证失败:', error);
        resolve(false);
      }, {
        message: '请验证指纹以解锁应用'
      });
      // #endif
      
      // #ifndef APP-PLUS
      // 在非APP环境下模拟指纹验证
      uni.showModal({
        title: '指纹验证',
        content: '模拟指纹验证成功',
        showCancel: false,
        success: () => {
          resolve(true);
        }
      });
      // #endif
    });
  }

  /**
   * 检查设备是否支持指纹识别
   * @returns {Promise<boolean>}
   */
  async isFingerprintSupported() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.fingerprint.isKeyguardSecure((result) => {
        resolve(result);
      }, () => {
        resolve(false);
      });
      // #endif
      
      // #ifndef APP-PLUS
      // 在非APP环境下返回true用于测试
      resolve(true);
      // #endif
    });
  }

  /**
   * 检查是否已录入指纹
   * @returns {Promise<boolean>}
   */
  async hasEnrolledFingerprint() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.fingerprint.isKeyguardSecure((result) => {
        if (result) {
          plus.fingerprint.isEnrolledFingerprints((enrolled) => {
            resolve(enrolled);
          }, () => {
            resolve(false);
          });
        } else {
          resolve(false);
        }
      }, () => {
        resolve(false);
      });
      // #endif
      
      // #ifndef APP-PLUS
      // 在非APP环境下返回true用于测试
      resolve(true);
      // #endif
    });
  }

  /**
   * 检查是否启用了安全功能
   * @returns {boolean}
   */
  isSecurityEnabled() {
    try {
      return uni.getStorageSync(this.SECURITY_ENABLED_KEY) === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 禁用安全功能
   */
  disableSecurity() {
    try {
      uni.removeStorageSync(this.PIN_KEY);
      uni.removeStorageSync(this.FINGERPRINT_KEY);
      uni.removeStorageSync(this.SECURITY_ENABLED_KEY);
      uni.removeStorageSync(this.LAST_UNLOCK_TIME_KEY);
    } catch (error) {
      console.error('禁用安全功能失败:', error);
    }
  }

  /**
   * 更新最后解锁时间
   */
  updateLastUnlockTime() {
    try {
      uni.setStorageSync(this.LAST_UNLOCK_TIME_KEY, Date.now());
    } catch (error) {
      console.error('更新解锁时间失败:', error);
    }
  }

  /**
   * 检查是否需要解锁
   * @returns {boolean}
   */
  needsUnlock() {
    try {
      if (!this.isSecurityEnabled()) {
        return false;
      }

      const lastUnlockTime = uni.getStorageSync(this.LAST_UNLOCK_TIME_KEY);
      if (!lastUnlockTime) {
        return true;
      }

      const now = Date.now();
      return (now - lastUnlockTime) > this.AUTO_LOCK_TIMEOUT;
    } catch (error) {
      return true;
    }
  }

  /**
   * 验证PIN码格式
   * @param {string} pin
   * @returns {boolean}
   */
  validatePin(pin) {
    return /^\d{6}$/.test(pin);
  }

  /**
   * 简单加密PIN码
   * @param {string} pin
   * @returns {string}
   */
  encryptPin(pin) {
    // 简单的异或加密
    const key = 'SUIMI_JIZHANG_2024';
    let encrypted = '';
    for (let i = 0; i < pin.length; i++) {
      encrypted += String.fromCharCode(
        pin.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      );
    }
    return btoa(encrypted);
  }

  /**
   * 解密PIN码
   * @param {string} encryptedPin
   * @returns {string}
   */
  decryptPin(encryptedPin) {
    try {
      const key = 'SUIMI_JIZHANG_2024';
      const encrypted = atob(encryptedPin);
      let decrypted = '';
      for (let i = 0; i < encrypted.length; i++) {
        decrypted += String.fromCharCode(
          encrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      return decrypted;
    } catch (error) {
      return '';
    }
  }
}

export default new SecurityManager();
