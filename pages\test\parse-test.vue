<template>
	<view class="container">
		<view class="header">
			<text class="title">语音解析测试</text>
		</view>
		
		<view class="test-section">
			<view class="section-title">输入测试文本</view>
			<textarea 
				v-model="testText" 
				placeholder="输入要测试的语音文本，如：买菜花了30块钱"
				class="test-input"
			></textarea>
			<button @click="testParse" class="test-btn">测试解析</button>
		</view>
		
		<view class="result-section" v-if="parseResult">
			<view class="section-title">解析结果</view>
			<view class="result-item">
				<text class="label">类型:</text>
				<text class="value" :class="parseResult.type">
					{{ parseResult.type === 'income' ? '收入' : '支出' }}
				</text>
			</view>
			<view class="result-item">
				<text class="label">金额:</text>
				<text class="value amount">{{ parseResult.amount }}元</text>
			</view>
			<view class="result-item">
				<text class="label">分类:</text>
				<text class="value">{{ parseResult.category || '未分类' }}</text>
			</view>
			<view v-if="parseResult.date" class="result-item">
				<text class="label">日期:</text>
				<text class="value">{{ parseResult.dateText || parseResult.date }}</text>
			</view>
			<view class="result-item">
				<text class="label">备注:</text>
				<text class="value">{{ parseResult.note || '无' }}</text>
			</view>
		</view>
		
		<view class="examples-section">
			<view class="section-title">测试示例</view>
			<view class="examples">
				<view 
					v-for="(example, index) in examples" 
					:key="index"
					class="example-item"
					@click="useExample(example)"
				>
					<text class="example-text">{{ example }}</text>
				</view>
			</view>
		</view>
		
		<view class="debug-section" v-if="debugInfo">
			<view class="section-title">调试信息</view>
			<text class="debug-text">{{ debugInfo }}</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			testText: '',
			parseResult: null,
			debugInfo: '',
			examples: [
				// 支出示例
				'买菜花了30块钱',
				'午餐50元',
				'打车费用25块',
				'电影票35元',
				'咖啡15块',
				'加油200元',
				'房租2000块',
				'买衣服150元',
				'理发30块钱',
				'水果20元',
				'花了60买零食',
				'交通费40',
				'生活费500',
				'娱乐支出80',

				// 收入示例
				'工资收入5000',
				'奖金收入1000',
				'兼职收入500',
				'红包收入200',
				'退款100元',
				'利息收入50',
				'报销300元',

				// 带日期的示例
				'昨天买菜花了30块钱',
				'今天午餐50元',
				'明天电影票35元',
				'前天打车费用25块',
				'2024年1月15日买书80元',
				'1月20日工资收入5000',
				'上周一买衣服150元',
				'这周三理发30块',
				'下个月房租2000块',
				'上月电费100元',

				// 边界情况
				'30元',
				'花了50',
				'收入1000',
				'买东西100块'
			]
		};
	},
	
	methods: {
		testParse() {
			if (!this.testText.trim()) {
				uni.showToast({
					title: '请输入测试文本',
					icon: 'none'
				});
				return;
			}
			
			try {
				this.parseResult = this.extractRecordInfo(this.testText);
				this.debugInfo = `原文: "${this.testText}"\n解析成功: ${this.parseResult ? '是' : '否'}`;
				
				if (!this.parseResult) {
					uni.showToast({
						title: '解析失败，未识别到有效信息',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('解析错误:', error);
				this.debugInfo = `解析错误: ${error.message}`;
				uni.showToast({
					title: '解析出错',
					icon: 'none'
				});
			}
		},
		
		useExample(example) {
			this.testText = example;
			this.testParse();
		},
		
		// 复制语音记账页面的解析逻辑
		extractRecordInfo(text) {
			console.log('解析语音文本:', text);
			
			// 更灵活的金额匹配
			const amountPatterns = [
				/(\d+(?:\.\d+)?)[元块钱]/,           // 30元, 30块, 30钱
				/(\d+(?:\.\d+)?)块/,                 // 30块
				/(\d+(?:\.\d+)?)元/,                 // 30元
				/花了?(\d+(?:\.\d+)?)/,              // 花了30, 花30
				/(\d+(?:\.\d+)?)$/,                  // 纯数字结尾
				/(\d+(?:\.\d+)?)[费用]/,             // 30费用
				/收入(\d+(?:\.\d+)?)/,               // 收入5000
				/(\d+(?:\.\d+)?)收入/                // 5000收入
			];
			
			let amount = null;
			let amountMatch = null;
			
			for (const pattern of amountPatterns) {
				amountMatch = text.match(pattern);
				if (amountMatch) {
					amount = parseFloat(amountMatch[1]);
					console.log('匹配到金额:', amount, '使用模式:', pattern);
					break;
				}
			}
			
			if (!amount || amount <= 0) {
				console.log('未匹配到有效金额');
				return null;
			}
			
			// 判断收支类型 - 更全面的关键词匹配
			const incomeKeywords = [
				'工资', '收入', '奖金', '红包', '转入', '薪水', '薪资', 
				'分红', '利息', '退款', '报销', '补贴', '津贴', '提成',
				'兼职', '外快', '零花钱', '压岁钱', '礼金'
			];
			
			const isIncome = incomeKeywords.some(keyword => text.includes(keyword));
			const type = isIncome ? 'income' : 'expense';
			console.log('识别类型:', type, '原文:', text);
			
			// 更全面的分类匹配
			let category = null;
			
			const categoryKeywords = {
				'餐饮': [
					'吃饭', '午餐', '晚餐', '早餐', '买菜', '餐饮', '食物', '零食',
					'外卖', '快餐', '火锅', '烧烤', '咖啡', '奶茶', '饮料', '水果',
					'蔬菜', '肉类', '米面', '调料', '厨房', '做饭', '聚餐'
				],
				'交通': [
					'打车', '公交', '地铁', '出租车', '交通', '滴滴', '出行',
					'汽油', '加油', '停车', '过路费', '高速', '火车', '飞机',
					'机票', '车票', '船票', '摩托', '电动车', '自行车'
				],
				'购物': [
					'买', '购物', '商品', '衣服', '鞋子', '包包', '化妆品',
					'护肤', '洗发', '牙膏', '毛巾', '床单', '家具', '电器',
					'手机', '电脑', '数码', '文具', '书籍', '玩具'
				],
				'娱乐': [
					'电影', '游戏', '娱乐', 'KTV', '酒吧', '网吧', '台球',
					'健身', '游泳', '旅游', '景点', '门票', '演出', '音乐会',
					'体育', '运动', '球类', '户外', '爬山', '钓鱼'
				],
				'生活': [
					'电费', '水费', '房租', '生活', '物业', '网费', '话费',
					'燃气', '暖气', '维修', '清洁', '洗衣', '理发', '美容',
					'医疗', '药品', '看病', '体检', '保险', '银行', '手续费'
				],
				'工资': ['工资', '薪水', '薪资', '底薪', '基本工资'],
				'奖金': ['奖金', '红包', '分红', '提成', '津贴', '补贴', '年终奖'],
				'其他收入': ['利息', '退款', '报销', '兼职', '外快', '礼金']
			};
			
			// 分类匹配 - 按优先级匹配
			for (const [cat, keywords] of Object.entries(categoryKeywords)) {
				const matchedKeyword = keywords.find(keyword => text.includes(keyword));
				if (matchedKeyword) {
					category = cat;
					console.log('匹配到分类:', cat, '关键词:', matchedKeyword);
					break;
				}
			}
			
			// 如果没有匹配到分类，根据收支类型设置默认分类
			if (!category) {
				if (type === 'income') {
					category = '其他收入';
				} else {
					category = '其他支出';
				}
				console.log('使用默认分类:', category);
			}
			
			// 智能提取备注
			let note = text;
			
			// 移除金额相关文字
			const amountTexts = ['元', '块', '钱', '花了', '费用', '收入'];
			amountTexts.forEach(txt => {
				note = note.replace(new RegExp(txt, 'g'), '');
			});
			
			// 移除数字
			note = note.replace(/\d+(?:\.\d+)?/g, '');
			
			// 移除匹配到的分类关键词
			if (category && categoryKeywords[category]) {
				categoryKeywords[category].forEach(keyword => {
					note = note.replace(new RegExp(keyword, 'g'), '');
				});
			}
			
			// 清理多余的字符和空格
			note = note.replace(/[的了]/g, '').trim();
			
			// 如果备注为空，使用原始文本
			if (!note) {
				note = text;
			}
			
			const result = {
				type,
				amount,
				category,
				note
			};
			
			console.log('解析结果:', result);
			return result;
		}
	}
};
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.test-section, .result-section, .examples-section, .debug-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.test-input {
	width: 100%;
	height: 120rpx;
	border: 2rpx solid #ddd;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	box-sizing: border-box;
}

.test-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	width: 100%;
}

.result-item {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 10rpx;
}

.label {
	font-size: 28rpx;
	color: #666;
	width: 120rpx;
	flex-shrink: 0;
}

.value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.value.income {
	color: #28a745;
	font-weight: bold;
}

.value.expense {
	color: #dc3545;
	font-weight: bold;
}

.value.amount {
	color: #007bff;
	font-weight: bold;
}

.examples {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.example-item {
	background: #e3f2fd;
	border: 2rpx solid #2196f3;
	border-radius: 30rpx;
	padding: 15rpx 25rpx;
	cursor: pointer;
}

.example-item:active {
	background: #bbdefb;
}

.example-text {
	font-size: 26rpx;
	color: #1976d2;
}

.debug-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	white-space: pre-line;
}
</style>
