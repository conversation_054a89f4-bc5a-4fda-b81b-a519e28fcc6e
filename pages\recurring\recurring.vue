<template>
  <view class="recurring-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-content">
        <text class="title">周期记账</text>
        <view class="add-btn" @click="showAddDialog = true">
          <text class="add-icon">+</text>
        </view>
      </view>
    </view>

    <!-- 模板列表 -->
    <view class="template-list">
      <view class="template-item" 
            v-for="template in templates" 
            :key="template.id"
            @click="viewTemplate(template)">
        <view class="item-header">
          <view class="item-info">
            <text class="item-name">{{template.name}}</text>
            <text class="item-frequency">{{getFrequencyText(template.frequency)}}</text>
          </view>
          <view class="item-toggle">
            <switch :checked="template.enabled" @change="toggleTemplate(template)" />
          </view>
        </view>
        
        <view class="item-details">
          <view class="detail-row">
            <text class="detail-label">类型</text>
            <text class="detail-value">{{template.type === 'expense' ? '支出' : '收入'}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">金额</text>
            <text class="detail-value">¥{{template.amount.toFixed(2)}}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">下次执行</text>
            <text class="detail-value">{{formatDate(template.nextExecution)}}</text>
          </view>
        </view>
        
        <view class="item-actions">
          <view class="action-btn" @click.stop="executeTemplate(template)">立即执行</view>
          <view class="action-btn" @click.stop="editTemplate(template)">编辑</view>
        </view>
      </view>
    </view>

    <!-- 添加模板对话框 -->
    <view class="modal" v-if="showAddDialog" @click="closeAddDialog">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{editingTemplate ? '编辑模板' : '创建模板'}}</text>
          <text class="modal-close" @click="closeAddDialog">×</text>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">模板名称</text>
            <input class="form-input" v-model="templateForm.name" placeholder="请输入模板名称" />
          </view>
          
          <view class="form-group">
            <text class="form-label">类型</text>
            <view class="type-options">
              <view class="type-option" 
                    @click="templateForm.type = 'expense'"
                    :class="{ selected: templateForm.type === 'expense' }">
                <text class="type-text">支出</text>
              </view>
              <view class="type-option" 
                    @click="templateForm.type = 'income'"
                    :class="{ selected: templateForm.type === 'income' }">
                <text class="type-text">收入</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">分类</text>
            <picker @change="onCategoryChange" :value="categoryIndex" :range="categoryNames">
              <view class="picker-view">
                <text class="picker-text">{{selectedCategoryName || '请选择分类'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">账户</text>
            <picker @change="onAccountChange" :value="accountIndex" :range="accountNames">
              <view class="picker-view">
                <text class="picker-text">{{selectedAccountName || '请选择账户'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">金额</text>
            <input class="form-input" v-model="templateForm.amount" type="digit" placeholder="请输入金额" />
          </view>
          
          <view class="form-group">
            <text class="form-label">频率</text>
            <view class="frequency-options">
              <view class="frequency-option" 
                    v-for="freq in frequencyOptions" 
                    :key="freq.value"
                    @click="templateForm.frequency = freq.value"
                    :class="{ selected: templateForm.frequency === freq.value }">
                <text class="frequency-text">{{freq.label}}</text>
              </view>
            </view>
          </view>
          
          <view class="form-group">
            <text class="form-label">开始日期</text>
            <picker mode="date" @change="onStartDateChange" :value="startDateStr">
              <view class="picker-view">
                <text class="picker-text">{{startDateStr || '请选择日期'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">结束日期（可选）</text>
            <picker mode="date" @change="onEndDateChange" :value="endDateStr">
              <view class="picker-view">
                <text class="picker-text">{{endDateStr || '请选择日期'}}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-group">
            <text class="form-label">备注</text>
            <textarea class="form-textarea" v-model="templateForm.note" placeholder="请输入备注信息"></textarea>
          </view>
        </view>
        <view class="modal-footer">
          <view class="btn btn-cancel" @click="closeAddDialog">取消</view>
          <view class="btn btn-primary" @click="saveTemplate">{{editingTemplate ? '保存' : '创建'}}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import StorageManager from '@/utils/storageManager.js';
import { createRecurringTemplate } from '@/utils/advancedModels.js';
import { createRecord } from '@/utils/models.js';

export default {
  data() {
    return {
      templates: [],
      categories: [],
      accounts: [],
      showAddDialog: false,
      editingTemplate: null,
      templateForm: {
        name: '',
        type: 'expense',
        categoryId: '',
        accountId: '',
        amount: '',
        frequency: 'monthly',
        startDate: Date.now(),
        endDate: null,
        note: ''
      },
      categoryIndex: 0,
      accountIndex: 0,
      startDateStr: '',
      endDateStr: '',
      frequencyOptions: [
        { value: 'daily', label: '每日' },
        { value: 'weekly', label: '每周' },
        { value: 'monthly', label: '每月' },
        { value: 'yearly', label: '每年' }
      ]
    };
  },
  
  computed: {
    categoryNames() {
      return this.currentCategories.map(category => category.name);
    },
    
    accountNames() {
      return this.accounts.map(account => account.name);
    },
    
    currentCategories() {
      return this.categories.filter(category => 
        category.type === this.templateForm.type && !category.deleted
      );
    },
    
    selectedCategoryName() {
      if (this.templateForm.categoryId) {
        const category = this.categories.find(c => c.id === this.templateForm.categoryId);
        return category ? category.name : '';
      }
      return '';
    },
    
    selectedAccountName() {
      if (this.templateForm.accountId) {
        const account = this.accounts.find(a => a.id === this.templateForm.accountId);
        return account ? account.name : '';
      }
      return '';
    }
  },
  
  onLoad() {
    this.loadData();
    this.checkAndExecuteTemplates();
  },
  
  methods: {
    loadData() {
      this.templates = StorageManager.getRecurringTemplates().filter(t => !t.deleted);
      this.categories = StorageManager.getCategories();
      this.accounts = StorageManager.getAccounts().filter(a => !a.deleted);
    },
    
    checkAndExecuteTemplates() {
      const now = Date.now();
      let hasUpdates = false;
      
      this.templates.forEach(template => {
        if (template.enabled && template.nextExecution <= now) {
          this.autoExecuteTemplate(template);
          hasUpdates = true;
        }
      });
      
      if (hasUpdates) {
        StorageManager.saveRecurringTemplates(this.templates);
      }
    },
    
    autoExecuteTemplate(template) {
      // 创建记录
      const record = createRecord({
        type: template.type,
        categoryId: template.categoryId,
        accountId: template.accountId,
        amount: template.amount,
        note: `${template.note} (自动记账)`,
        date: Date.now(),
        tags: template.tags || []
      });
      
      // 保存记录
      const records = StorageManager.getRecords();
      records.unshift(record);
      StorageManager.saveRecords(records);
      
      // 更新账户余额
      this.updateAccountBalance(template.accountId, template.type, template.amount);
      
      // 更新模板执行时间
      template.lastExecuted = Date.now();
      template.nextExecution = this.calculateNextExecution(template);
      
      uni.showToast({
        title: `已自动执行: ${template.name}`,
        icon: 'success'
      });
    },
    
    executeTemplate(template) {
      uni.showModal({
        title: '确认执行',
        content: `确定要立即执行模板 ${template.name} 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.autoExecuteTemplate(template);
            
            // 更新模板
            const index = this.templates.findIndex(t => t.id === template.id);
            if (index !== -1) {
              this.templates[index] = template;
              StorageManager.saveRecurringTemplates(this.templates);
            }
          }
        }
      });
    },
    
    toggleTemplate(template) {
      const index = this.templates.findIndex(t => t.id === template.id);
      if (index !== -1) {
        this.templates[index].enabled = !this.templates[index].enabled;
        StorageManager.saveRecurringTemplates(this.templates);
        
        uni.showToast({
          title: this.templates[index].enabled ? '模板已启用' : '模板已禁用',
          icon: 'success'
        });
      }
    },
    
    viewTemplate(template) {
      const category = this.categories.find(c => c.id === template.categoryId);
      const account = this.accounts.find(a => a.id === template.accountId);
      
      uni.showModal({
        title: template.name,
        content: `类型: ${template.type === 'expense' ? '支出' : '收入'}\n分类: ${category ? category.name : '未知'}\n账户: ${account ? account.name : '未知'}\n金额: ¥${template.amount.toFixed(2)}\n频率: ${this.getFrequencyText(template.frequency)}\n状态: ${template.enabled ? '启用' : '禁用'}`,
        showCancel: true,
        cancelText: '编辑',
        confirmText: '关闭',
        success: (res) => {
          if (res.cancel) {
            this.editTemplate(template);
          }
        }
      });
    },
    
    editTemplate(template) {
      this.editingTemplate = template;
      this.templateForm = {
        name: template.name,
        type: template.type,
        categoryId: template.categoryId,
        accountId: template.accountId,
        amount: template.amount.toString(),
        frequency: template.frequency,
        startDate: template.startDate,
        endDate: template.endDate,
        note: template.note
      };
      
      // 设置选择器索引
      const categoryIndex = this.currentCategories.findIndex(c => c.id === template.categoryId);
      this.categoryIndex = categoryIndex >= 0 ? categoryIndex : 0;
      
      const accountIndex = this.accounts.findIndex(a => a.id === template.accountId);
      this.accountIndex = accountIndex >= 0 ? accountIndex : 0;
      
      // 设置日期字符串
      this.startDateStr = this.formatDate(template.startDate);
      this.endDateStr = template.endDate ? this.formatDate(template.endDate) : '';
      
      this.showAddDialog = true;
    },
    
    closeAddDialog() {
      this.showAddDialog = false;
      this.editingTemplate = null;
      this.resetForm();
    },
    
    resetForm() {
      this.templateForm = {
        name: '',
        type: 'expense',
        categoryId: '',
        accountId: '',
        amount: '',
        frequency: 'monthly',
        startDate: Date.now(),
        endDate: null,
        note: ''
      };
      this.categoryIndex = 0;
      this.accountIndex = 0;
      this.startDateStr = this.formatDate(Date.now());
      this.endDateStr = '';
    },
    
    onCategoryChange(e) {
      this.categoryIndex = e.detail.value;
      this.templateForm.categoryId = this.currentCategories[this.categoryIndex]?.id || '';
    },
    
    onAccountChange(e) {
      this.accountIndex = e.detail.value;
      this.templateForm.accountId = this.accounts[this.accountIndex]?.id || '';
    },
    
    onStartDateChange(e) {
      this.startDateStr = e.detail.value;
      this.templateForm.startDate = new Date(e.detail.value).getTime();
    },
    
    onEndDateChange(e) {
      this.endDateStr = e.detail.value;
      this.templateForm.endDate = new Date(e.detail.value).getTime();
    },
    
    saveTemplate() {
      // 验证表单
      if (!this.templateForm.name.trim()) {
        uni.showToast({
          title: '请输入模板名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.templateForm.categoryId) {
        uni.showToast({
          title: '请选择分类',
          icon: 'none'
        });
        return;
      }
      
      if (!this.templateForm.accountId) {
        uni.showToast({
          title: '请选择账户',
          icon: 'none'
        });
        return;
      }
      
      if (!this.templateForm.amount || this.templateForm.amount <= 0) {
        uni.showToast({
          title: '请输入有效的金额',
          icon: 'none'
        });
        return;
      }
      
      if (this.editingTemplate) {
        // 编辑模板
        const index = this.templates.findIndex(t => t.id === this.editingTemplate.id);
        if (index !== -1) {
          this.templates[index] = {
            ...this.templates[index],
            name: this.templateForm.name,
            type: this.templateForm.type,
            categoryId: this.templateForm.categoryId,
            accountId: this.templateForm.accountId,
            amount: Number(this.templateForm.amount),
            frequency: this.templateForm.frequency,
            startDate: this.templateForm.startDate,
            endDate: this.templateForm.endDate,
            note: this.templateForm.note,
            nextExecution: this.calculateNextExecution({
              ...this.templates[index],
              frequency: this.templateForm.frequency,
              startDate: this.templateForm.startDate
            }),
            updatedAt: Date.now()
          };
        }
      } else {
        // 创建新模板
        const newTemplate = createRecurringTemplate({
          name: this.templateForm.name,
          type: this.templateForm.type,
          categoryId: this.templateForm.categoryId,
          accountId: this.templateForm.accountId,
          amount: Number(this.templateForm.amount),
          frequency: this.templateForm.frequency,
          startDate: this.templateForm.startDate,
          endDate: this.templateForm.endDate,
          note: this.templateForm.note
        });
        
        newTemplate.nextExecution = this.calculateNextExecution(newTemplate);
        this.templates.unshift(newTemplate);
      }
      
      StorageManager.saveRecurringTemplates(this.templates);
      this.closeAddDialog();
      
      uni.showToast({
        title: this.editingTemplate ? '模板已更新' : '模板已创建',
        icon: 'success'
      });
    },
    
    calculateNextExecution(template) {
      const now = Date.now();
      let nextTime = template.startDate;
      
      // 如果开始时间已过，计算下一次执行时间
      if (nextTime <= now) {
        const startDate = new Date(template.startDate);
        const currentDate = new Date();
        
        switch (template.frequency) {
          case 'daily':
            nextTime = now + 24 * 60 * 60 * 1000;
            break;
          case 'weekly':
            nextTime = now + 7 * 24 * 60 * 60 * 1000;
            break;
          case 'monthly':
            const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, startDate.getDate());
            nextTime = nextMonth.getTime();
            break;
          case 'yearly':
            const nextYear = new Date(currentDate.getFullYear() + 1, startDate.getMonth(), startDate.getDate());
            nextTime = nextYear.getTime();
            break;
        }
      }
      
      return nextTime;
    },
    
    updateAccountBalance(accountId, type, amount) {
      const accounts = StorageManager.getAccounts();
      const accountIndex = accounts.findIndex(a => a.id === accountId);
      
      if (accountIndex !== -1) {
        if (type === 'expense') {
          accounts[accountIndex].balance -= amount;
        } else {
          accounts[accountIndex].balance += amount;
        }
        
        accounts[accountIndex].updatedAt = Date.now();
        StorageManager.saveAccounts(accounts);
      }
    },
    
    getFrequencyText(frequency) {
      const frequencyMap = {
        daily: '每日',
        weekly: '每周',
        monthly: '每月',
        yearly: '每年'
      };
      return frequencyMap[frequency] || '未知';
    },
    
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  },
  
  onShow() {
    this.resetForm();
    this.checkAndExecuteTemplates();
  }
};
</script>

<style scoped>
.recurring-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  padding: 44px 20px 20px;
  background: rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: white;
}

.add-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.template-list {
  margin: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.template-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.item-frequency {
  font-size: 12px;
  color: #999;
}

.item-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row {
  text-align: center;
  flex: 1;
}

.detail-label {
  display: block;
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}

.detail-value {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 8px 16px;
  background: #f0f0f0;
  border-radius: 20px;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
}

.type-options {
  display: flex;
  gap: 12px;
}

.type-option {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
}

.type-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.frequency-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.frequency-option {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}

.frequency-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.picker-text {
  color: #333;
}

.picker-arrow {
  color: #999;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
}

.btn-cancel {
  background: #f0f0f0;
  color: #666;
}

.btn-primary {
  background: #667eea;
  color: white;
}
</style>
