
.fingerprint-setup[data-v-f47ddb39] {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.custom-navbar[data-v-f47ddb39] {
  padding-top: var(--status-bar-height);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.navbar-content[data-v-f47ddb39] {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.navbar-left[data-v-f47ddb39] {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.icon-back[data-v-f47ddb39] {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}
.navbar-title[data-v-f47ddb39] {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}
.navbar-right[data-v-f47ddb39] {
  width: 60px;
}
.content[data-v-f47ddb39] {
  padding: 40px 30px;
  text-align: center;
}
.fingerprint-icon[data-v-f47ddb39] {
  margin-bottom: 30px;
}
.icon[data-v-f47ddb39] {
  font-size: 80px;
}
.title[data-v-f47ddb39] {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}
.description[data-v-f47ddb39] {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  line-height: 1.5;
}
.status-info[data-v-f47ddb39] {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.status-item[data-v-f47ddb39] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.status-item[data-v-f47ddb39]:last-child {
  margin-bottom: 0;
}
.status-label[data-v-f47ddb39] {
  font-size: 16px;
  color: #fff;
}
.status-value[data-v-f47ddb39] {
  font-size: 16px;
  font-weight: 600;
}
.status-value.success[data-v-f47ddb39] {
  color: #4CAF50;
}
.status-value.error[data-v-f47ddb39] {
  color: #FF5722;
}
.action-buttons[data-v-f47ddb39] {
  margin-bottom: 40px;
}
.btn[data-v-f47ddb39] {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}
.btn-primary[data-v-f47ddb39] {
  background: #fff;
  color: #667eea;
}
.btn-primary[data-v-f47ddb39]:active {
  transform: scale(0.98);
}
.btn-secondary[data-v-f47ddb39] {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.btn-disabled[data-v-f47ddb39] {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.tips[data-v-f47ddb39] {
  text-align: left;
}
.tip-item[data-v-f47ddb39] {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}
.tip-icon[data-v-f47ddb39] {
  font-size: 16px;
  margin-right: 10px;
  margin-top: 2px;
}
.tip-text[data-v-f47ddb39] {
  flex: 1;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}
