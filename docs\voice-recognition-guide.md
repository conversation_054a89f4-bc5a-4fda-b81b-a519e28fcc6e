# 语音记账功能使用指南

## 功能概述

碎米记账集成了硅基流动的语音转文本AI技术，支持通过语音快速记录收支信息。用户只需说出记账内容，系统会自动识别并解析出金额、分类、备注等信息。

## 技术特性

### 语音识别技术
- **服务提供商**: 硅基流动 (SiliconFlow)
- **识别模型**: FunAudioLLM/SenseVoiceSmall
- **支持语言**: 中文
- **识别精度**: 高精度语音识别，支持自然语言表达

### 音频处理
- **采样率**: 16kHz
- **声道**: 单声道
- **格式支持**: WebM (Opus编码)、MP4、WAV
- **降噪**: 支持回声消除、噪声抑制、自动增益控制
- **录音时长**: 1-60秒

## 使用方法

### 基本操作
1. 点击"开始录音"按钮
2. 清晰地说出记账内容
3. 点击"停止录音"完成录制
4. 系统自动识别并解析内容
5. 确认信息后保存记录

### 语音表达示例

#### 支出记录
- "买菜花了30块钱"
- "午餐50元"
- "打车费用25块"
- "买书花了80元"
- "电费200"

#### 收入记录
- "工资收入5000"
- "奖金收入1000"
- "红包收入200"

#### 简洁表达
- "餐饮50"
- "交通25"
- "购物100"

## 智能解析功能

### 自动分类识别
系统会根据关键词自动识别分类：

- **餐饮**: 吃饭、午餐、晚餐、早餐、买菜、餐饮
- **交通**: 打车、公交、地铁、出租车、交通
- **购物**: 买、购物、商品
- **娱乐**: 电影、游戏、娱乐
- **生活**: 电费、水费、房租、生活
- **工资**: 工资、薪水
- **奖金**: 奖金、红包

### 金额提取
- 支持"元"、"块"、"钱"等单位
- 自动识别小数点金额
- 支持整数和浮点数

### 收支判断
- 自动识别收入关键词：工资、收入、奖金、红包、转入
- 其他情况默认为支出

## 浏览器兼容性

### 支持的浏览器
- Chrome 47+
- Firefox 29+
- Safari 14+
- Edge 79+

### 所需权限
- 麦克风访问权限
- 网络访问权限

### 技术要求
- 支持 MediaDevices API
- 支持 MediaRecorder API
- 支持 Fetch API
- 支持 WebRTC

## 使用技巧

### 录音环境
- 保持安静的环境
- 距离麦克风适中（20-30cm）
- 说话清晰，语速适中
- 避免背景噪音干扰

### 语音表达建议
- 使用自然语言表达
- 包含金额和用途信息
- 可以添加简单的备注
- 避免过于复杂的句子

### 常见问题解决
1. **识别不准确**: 重新录音，注意发音清晰
2. **无法识别金额**: 确保说出具体数字和单位
3. **分类错误**: 可以手动编辑识别结果
4. **录音失败**: 检查麦克风权限和网络连接

## 隐私安全

### 数据处理
- 音频数据仅用于语音识别
- 不会永久存储音频文件
- 识别完成后立即清除音频数据

### API安全
- 使用HTTPS加密传输
- API密钥安全存储
- 遵循数据保护规范

## 故障排除

### 常见错误及解决方案

#### 录音权限问题
- **错误**: "请允许访问麦克风权限"
- **解决**: 在浏览器设置中允许麦克风权限

#### 网络连接问题
- **错误**: "网络连接失败"
- **解决**: 检查网络连接，确保能访问外网

#### 浏览器兼容性问题
- **错误**: "当前环境不支持录音功能"
- **解决**: 更换支持的浏览器或更新浏览器版本

#### API调用失败
- **错误**: "API调用失败"
- **解决**: 检查API密钥是否有效，稍后重试

## 更新日志

### v1.0.0
- 集成硅基流动语音识别API
- 支持中文语音识别
- 智能解析记账信息
- 自动分类识别
- 多浏览器兼容性支持

## 技术支持

如果在使用过程中遇到问题，请：
1. 检查浏览器兼容性
2. 确认网络连接正常
3. 验证麦克风权限设置
4. 查看控制台错误信息

更多技术细节请参考源码中的注释和文档。
