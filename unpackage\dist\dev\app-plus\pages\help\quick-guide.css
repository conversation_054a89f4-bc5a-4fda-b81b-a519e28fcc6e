
.page[data-v-052e5ca9] {
	min-height: 100vh;
	padding-bottom: 1.25rem;
}

/* 头部样式 */
.header[data-v-052e5ca9] {
	padding: 1.875rem 1.25rem 1.25rem;
	display: flex;
	align-items: center;
}
.back-btn[data-v-052e5ca9] {
	font-size: 1.125rem;
	margin-right: 0.625rem;
	font-weight: bold;
}
.header-title[data-v-052e5ca9] {
	font-size: 1.375rem;
	font-weight: bold;
	flex: 1;
	text-align: center;
	margin-right: 1.75rem; /* 平衡返回按钮的宽度 */
}

/* 内容区域 */
.content[data-v-052e5ca9] {
	padding: 0 1.25rem;
}
.section[data-v-052e5ca9] {
	margin-bottom: 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.section-header[data-v-052e5ca9] {
	margin-bottom: 0.9375rem;
}
.section-title[data-v-052e5ca9] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 功能项样式 */
.function-item[data-v-052e5ca9] {
	display: flex;
	align-items: flex-start;
	margin-bottom: 0.9375rem;
	padding-bottom: 0.9375rem;
	border-bottom: 0.03125rem solid #f0f0f0;
}
.function-item[data-v-052e5ca9]:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}
.function-icon[data-v-052e5ca9] {
	font-size: 1.5rem;
	margin-right: 0.75rem;
	margin-top: 0.25rem;
	flex-shrink: 0;
}
.function-info[data-v-052e5ca9] {
	flex: 1;
}
.function-name[data-v-052e5ca9] {
	font-size: 1rem;
	font-weight: bold;
	display: block;
	margin-bottom: 0.25rem;
}
.function-desc[data-v-052e5ca9] {
	font-size: 0.875rem;
	line-height: 1.6;
	display: block;
}

/* 使用技巧样式 */
.tip-item[data-v-052e5ca9] {
	margin-bottom: 0.75rem;
	padding: 0.75rem;
	background: rgba(102, 126, 234, 0.05);
	border-radius: 0.5rem;
	border-left: 0.1875rem solid #667eea;
}
.tip-item[data-v-052e5ca9]:last-child {
	margin-bottom: 0;
}
.tip-title[data-v-052e5ca9] {
	font-size: 0.9375rem;
	font-weight: bold;
	display: block;
	margin-bottom: 0.25rem;
}
.tip-content[data-v-052e5ca9] {
	font-size: 0.8125rem;
	line-height: 1.5;
	display: block;
}
