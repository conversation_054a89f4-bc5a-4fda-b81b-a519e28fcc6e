
.page[data-v-cf7ac1ec] {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 弹窗样式 */
.popup-overlay[data-v-cf7ac1ec] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

/* 头部样式 */
.header[data-v-cf7ac1ec] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 1.875rem 1.25rem 0.625rem;
	color: white;
}
.header-content[data-v-cf7ac1ec] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.back-btn[data-v-cf7ac1ec], .header-action[data-v-cf7ac1ec] {
	font-size: 1.25rem;
	font-weight: bold;
}
.header-title[data-v-cf7ac1ec] {
	font-size: 1.125rem;
	font-weight: bold;
}

/* 总资产 */
.total-assets[data-v-cf7ac1ec] {
	background: white;
	margin: -0.625rem 1.25rem 1.25rem;
	border-radius: 0.625rem;
	padding: 1.25rem;
	text-align: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.total-label[data-v-cf7ac1ec] {
	font-size: 0.875rem;
	color: #666;
	display: block;
	margin-bottom: 0.3125rem;
}
.total-amount[data-v-cf7ac1ec] {
	font-size: 1.5rem;
	font-weight: bold;
	color: #333;
}

/* 账户列表 */
.accounts-section[data-v-cf7ac1ec] {
	margin: 0 1.25rem 1.25rem;
}
.section-title[data-v-cf7ac1ec] {
	padding: 0.625rem 0;
	font-size: 1rem;
	font-weight: bold;
	color: #333;
}
.accounts-list[data-v-cf7ac1ec] {
	background: white;
	border-radius: 0.625rem;
	overflow: hidden;
}
.account-item[data-v-cf7ac1ec] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem 1.25rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	transition: background-color 0.2s;
}
.account-item[data-v-cf7ac1ec]:last-child {
	border-bottom: none;
}
.account-item[data-v-cf7ac1ec]:active {
	background-color: #f8f9fa;
}
.account-left[data-v-cf7ac1ec] {
	display: flex;
	align-items: center;
	flex: 1;
}
.account-icon[data-v-cf7ac1ec] {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 1.25rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.75rem;
}
.icon-text[data-v-cf7ac1ec] {
	font-size: 1rem;
}
.account-info[data-v-cf7ac1ec] {
	flex: 1;
}
.account-name[data-v-cf7ac1ec] {
	font-size: 1rem;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 0.25rem;
}
.account-type[data-v-cf7ac1ec] {
	font-size: 0.8125rem;
	color: #666;
}
.account-right[data-v-cf7ac1ec] {
	display: flex;
	align-items: center;
}
.account-balance[data-v-cf7ac1ec] {
	font-size: 1rem;
	font-weight: bold;
	margin-right: 0.5rem;
}
.account-balance.positive[data-v-cf7ac1ec] {
	color: #4CAF50;
}
.account-balance.negative[data-v-cf7ac1ec] {
	color: #F44336;
}
.account-balance.debt[data-v-cf7ac1ec] {
	color: #FF9800;
}
.account-arrow[data-v-cf7ac1ec] {
	font-size: 0.875rem;
	color: #ccc;
}

/* 快速操作 */
.quick-actions[data-v-cf7ac1ec] {
	margin: 0 1.25rem;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.625rem;
}
.action-item[data-v-cf7ac1ec] {
	background: white;
	border-radius: 0.625rem;
	padding: 0.9375rem 0.625rem;
	text-align: center;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}
.action-item[data-v-cf7ac1ec]:active {
	transform: translateY(0.0625rem);
}
.action-icon[data-v-cf7ac1ec] {
	font-size: 1.25rem;
	display: block;
	margin-bottom: 0.375rem;
}
.action-text[data-v-cf7ac1ec] {
	font-size: 0.75rem;
	color: #333;
	font-weight: 500;
}

/* 弹窗样式 */
.popup-content[data-v-cf7ac1ec] {
	background: white;
	border-radius: 0.625rem 0.625rem 0 0;
	padding: 1.25rem;
	max-height: 80vh;
}
.popup-header[data-v-cf7ac1ec] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.25rem;
}
.popup-title[data-v-cf7ac1ec] {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.popup-close[data-v-cf7ac1ec] {
	font-size: 1.5rem;
	color: #999;
	font-weight: bold;
}
.form-section[data-v-cf7ac1ec] {
	margin-bottom: 1.25rem;
}
.form-item[data-v-cf7ac1ec] {
	margin-bottom: 1.25rem;
}
.form-label[data-v-cf7ac1ec] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 0.625rem;
}
.form-input[data-v-cf7ac1ec] {
	width: 100%;
	padding: 0.75rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	border: none;
	font-size: 0.875rem;
	color: #333;
}
.type-selector[data-v-cf7ac1ec] {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 0.5rem;
}
.type-item[data-v-cf7ac1ec] {
	padding: 0.625rem;
	background: #f8f9fa;
	border-radius: 0.375rem;
	text-align: center;
	transition: all 0.3s;
}
.type-item.active[data-v-cf7ac1ec] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.type-icon[data-v-cf7ac1ec] {
	font-size: 0.875rem;
	display: block;
	margin-bottom: 0.25rem;
}
.type-name[data-v-cf7ac1ec] {
	font-size: 0.6875rem;
}
.color-selector[data-v-cf7ac1ec] {
	display: flex;
	flex-wrap: wrap;
	gap: 0.5rem;
}
.color-item[data-v-cf7ac1ec] {
	width: 1.875rem;
	height: 1.875rem;
	border-radius: 0.9375rem;
	border: 0.125rem solid transparent;
	transition: all 0.3s;
}
.color-item.active[data-v-cf7ac1ec] {
	border-color: #333;
	transform: scale(1.1);
}
.popup-actions[data-v-cf7ac1ec] {
	display: flex;
	gap: 0.625rem;
}
.cancel-btn[data-v-cf7ac1ec], .save-btn[data-v-cf7ac1ec] {
	flex: 1;
	height: 2.75rem;
	border-radius: 0.625rem;
	font-size: 1rem;
	font-weight: bold;
}
.cancel-btn[data-v-cf7ac1ec] {
	background: #f8f9fa;
	color: #666;
	border: none;
}
.save-btn[data-v-cf7ac1ec] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
}
