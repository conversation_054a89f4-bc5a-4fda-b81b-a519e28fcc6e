/**
 * 硅基流动语音识别服务
 * 集成硅基流动的语音转文本API
 */

import { config } from './config.js';

class SpeechRecognitionService {
    constructor() {
        this.apiUrl = config.siliconFlow.apiUrl;
        this.apiKey = config.siliconFlow.apiKey;
        this.model = config.siliconFlow.model;
        this.audioConstraints = config.speech.audioConstraints;
        this.supportedMimeTypes = config.speech.supportedMimeTypes;
        this.maxDuration = config.speech.maxRecordingDuration;
        this.minDuration = config.speech.minRecordingDuration;

        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.recordingStartTime = null;
        this.recordingTimer = null;

        // uni-app录音管理器
        this.recorderManager = null;
        this.tempFilePath = null;
    }

    /**
     * 开始录音
     * @returns {Promise<boolean>} 是否成功开始录音
     */
    async startRecording() {
        try {
            // 检查运行环境
            // #ifdef H5
            // H5环境使用浏览器API
            if (!navigator || !navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('当前环境不支持录音功能，请在支持的浏览器中使用');
            }

            // 获取麦克风权限
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: this.audioConstraints
            });
            // #endif

            // #ifndef H5
            // 非H5环境使用uni-app录音API
            return await this.startUniRecording();
            // #endif

            // 选择支持的音频格式
            let selectedMimeType = null;
            for (const mimeType of this.supportedMimeTypes) {
                if (MediaRecorder.isTypeSupported(mimeType)) {
                    selectedMimeType = mimeType;
                    break;
                }
            }

            if (!selectedMimeType) {
                throw new Error('浏览器不支持任何音频录制格式');
            }

            // 创建MediaRecorder
            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: selectedMimeType
            });

            this.audioChunks = [];
            this.isRecording = true;
            this.recordingStartTime = Date.now();

            // 监听数据事件
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            // 设置最大录音时长限制
            this.recordingTimer = setTimeout(() => {
                if (this.isRecording) {
                    console.log('达到最大录音时长，自动停止');
                    this.mediaRecorder.stop();
                }
            }, this.maxDuration * 1000);

            // 开始录音
            this.mediaRecorder.start();

            console.log('开始录音...');
            return true;

        } catch (error) {
            console.error('开始录音失败:', error);
            this.isRecording = false;
            throw error;
        }
    }

    /**
     * 停止录音并进行语音识别
     * @returns {Promise<string>} 识别结果文本
     */
    async stopRecording() {
        // #ifdef H5
        return this.stopH5Recording();
        // #endif

        // #ifndef H5
        return this.stopUniRecording();
        // #endif
    }

    /**
     * H5环境停止录音
     * @returns {Promise<string>}
     */
    async stopH5Recording() {
        return new Promise((resolve, reject) => {
            if (!this.mediaRecorder || !this.isRecording) {
                reject(new Error('未在录音状态'));
                return;
            }

            // 监听停止事件
            this.mediaRecorder.onstop = async () => {
                try {
                    // 清除定时器
                    if (this.recordingTimer) {
                        clearTimeout(this.recordingTimer);
                        this.recordingTimer = null;
                    }

                    // 检查录音时长
                    const recordingDuration = (Date.now() - this.recordingStartTime) / 1000;
                    if (recordingDuration < this.minDuration) {
                        throw new Error(`录音时长太短，至少需要${this.minDuration}秒`);
                    }

                    // 停止所有音频轨道
                    const stream = this.mediaRecorder.stream;
                    if (stream) {
                        stream.getTracks().forEach(track => track.stop());
                    }

                    // 创建音频文件
                    const audioBlob = new Blob(this.audioChunks, {
                        type: this.mediaRecorder.mimeType
                    });

                    console.log('录音完成，音频大小:', audioBlob.size, 'bytes', '时长:', recordingDuration.toFixed(1), '秒');

                    // 调用语音识别API
                    const result = await this.transcribeAudio(audioBlob);
                    resolve(result);

                } catch (error) {
                    console.error('语音识别失败:', error);
                    reject(error);
                } finally {
                    this.isRecording = false;
                    this.mediaRecorder = null;
                    this.audioChunks = [];
                    this.recordingStartTime = null;
                }
            };

            // 停止录音
            this.mediaRecorder.stop();
        });
    }

    /**
     * 调用硅基流动API进行语音识别
     * @param {Blob} audioBlob 音频数据
     * @returns {Promise<string>} 识别结果
     */
    async transcribeAudio(audioBlob) {
        try {
            // 创建FormData
            const formData = new FormData();
            formData.append('model', this.model);
            formData.append('file', audioBlob, 'audio.webm');

            // 设置请求选项
            const options = {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: formData
            };

            console.log('发送语音识别请求...');

            // 发送请求
            const response = await fetch(this.apiUrl, options);
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();
            console.log('语音识别响应:', data);

            // 提取识别结果
            if (data.text) {
                return data.text.trim();
            } else if (data.transcription) {
                return data.transcription.trim();
            } else {
                throw new Error('API响应中未找到识别结果');
            }

        } catch (error) {
            console.error('语音识别API调用失败:', error);
            throw error;
        }
    }

    /**
     * 检查录音状态
     * @returns {boolean} 是否正在录音
     */
    getRecordingStatus() {
        return this.isRecording;
    }

    /**
     * 取消录音
     */
    cancelRecording() {
        // #ifdef H5
        this.cancelH5Recording();
        // #endif

        // #ifndef H5
        this.cancelUniRecording();
        // #endif
    }

    /**
     * 取消H5录音
     */
    cancelH5Recording() {
        if (this.mediaRecorder && this.isRecording) {
            // 清除定时器
            if (this.recordingTimer) {
                clearTimeout(this.recordingTimer);
                this.recordingTimer = null;
            }

            this.mediaRecorder.stop();

            // 停止所有音频轨道
            const stream = this.mediaRecorder.stream;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }

            this.isRecording = false;
            this.mediaRecorder = null;
            this.audioChunks = [];
            this.recordingStartTime = null;

            console.log('H5录音已取消');
        }
    }

    /**
     * 检查兼容性
     * @returns {Object} 兼容性检查结果
     */
    checkCompatibility() {
        const result = {
            supported: true,
            issues: [],
            platform: ''
        };

        // #ifdef H5
        result.platform = 'H5';

        // 检查MediaDevices API
        if (!navigator || !navigator.mediaDevices) {
            result.supported = false;
            result.issues.push('不支持MediaDevices API');
        }

        // 检查getUserMedia
        if (!navigator.mediaDevices?.getUserMedia) {
            result.supported = false;
            result.issues.push('不支持getUserMedia');
        }

        // 检查MediaRecorder
        if (!window.MediaRecorder) {
            result.supported = false;
            result.issues.push('不支持MediaRecorder');
        }

        // 检查Fetch API
        if (!window.fetch) {
            result.supported = false;
            result.issues.push('不支持Fetch API');
        }
        // #endif

        // #ifdef MP-WEIXIN
        result.platform = '微信小程序';

        // 检查录音管理器
        if (!uni.getRecorderManager) {
            result.supported = false;
            result.issues.push('不支持录音管理器');
        }

        // 检查文件系统
        if (!uni.getFileSystemManager) {
            result.supported = false;
            result.issues.push('不支持文件系统');
        }
        // #endif

        // #ifdef APP-PLUS
        result.platform = 'App';

        // 检查录音管理器
        if (!uni.getRecorderManager) {
            result.supported = false;
            result.issues.push('不支持录音管理器');
        }

        // 检查文件系统
        if (!plus.io) {
            result.supported = false;
            result.issues.push('不支持文件系统');
        }
        // #endif

        // #ifndef H5 || MP-WEIXIN || APP-PLUS
        result.platform = '未知平台';
        result.supported = false;
        result.issues.push('当前平台不支持录音功能');
        // #endif

        return result;
    }

    /**
     * uni-app环境开始录音
     * @returns {Promise<boolean>}
     */
    async startUniRecording() {
        return new Promise((resolve, reject) => {
            try {
                // 创建录音管理器
                this.recorderManager = uni.getRecorderManager();

                // 设置录音参数
                const options = {
                    duration: this.maxDuration * 1000, // 最大录音时长（毫秒）
                    sampleRate: this.audioConstraints.sampleRate,
                    numberOfChannels: this.audioConstraints.channelCount,
                    encodeBitRate: 48000,
                    format: 'mp3'
                };

                // 监听录音开始
                this.recorderManager.onStart(() => {
                    console.log('uni录音开始');
                    this.isRecording = true;
                    this.recordingStartTime = Date.now();
                    resolve(true);
                });

                // 监听录音结束
                this.recorderManager.onStop((res) => {
                    console.log('uni录音结束', res);
                    this.tempFilePath = res.tempFilePath;
                    this.isRecording = false;
                });

                // 监听录音错误
                this.recorderManager.onError((err) => {
                    console.error('uni录音错误', err);
                    this.isRecording = false;
                    reject(new Error('录音失败: ' + err.errMsg));
                });

                // 开始录音
                this.recorderManager.start(options);

            } catch (error) {
                console.error('启动uni录音失败:', error);
                reject(error);
            }
        });
    }

    /**
     * uni-app环境停止录音
     * @returns {Promise<string>}
     */
    async stopUniRecording() {
        return new Promise((resolve, reject) => {
            if (!this.recorderManager || !this.isRecording) {
                reject(new Error('未在录音状态'));
                return;
            }

            // 监听录音停止事件
            this.recorderManager.onStop(async (res) => {
                try {
                    // 检查录音时长
                    const recordingDuration = (Date.now() - this.recordingStartTime) / 1000;
                    if (recordingDuration < this.minDuration) {
                        throw new Error(`录音时长太短，至少需要${this.minDuration}秒`);
                    }

                    console.log('uni录音文件路径:', res.tempFilePath);

                    // 读取录音文件并转换为Blob
                    const audioBlob = await this.convertUniAudioToBlob(res.tempFilePath);

                    // 调用语音识别API
                    const result = await this.transcribeAudio(audioBlob);
                    resolve(result);

                } catch (error) {
                    console.error('uni语音识别失败:', error);
                    reject(error);
                } finally {
                    this.isRecording = false;
                    this.recorderManager = null;
                    this.tempFilePath = null;
                    this.recordingStartTime = null;
                }
            });

            // 停止录音
            this.recorderManager.stop();
        });
    }

    /**
     * 将uni-app录音文件转换为Blob
     * @param {string} tempFilePath 临时文件路径
     * @returns {Promise<Blob>}
     */
    async convertUniAudioToBlob(tempFilePath) {
        return new Promise((resolve, reject) => {
            // #ifdef H5
            // H5环境直接返回空Blob（不应该执行到这里）
            resolve(new Blob());
            // #endif

            // #ifdef MP-WEIXIN
            // 微信小程序环境
            const fs = uni.getFileSystemManager();
            fs.readFile({
                filePath: tempFilePath,
                success: (res) => {
                    const arrayBuffer = res.data;
                    const blob = new Blob([arrayBuffer], { type: 'audio/mp3' });
                    resolve(blob);
                },
                fail: (err) => {
                    console.error('读取录音文件失败:', err);
                    reject(new Error('读取录音文件失败'));
                }
            });
            // #endif

            // #ifdef APP-PLUS
            // App环境
            plus.io.resolveLocalFileSystemURL(tempFilePath, (entry) => {
                entry.file((file) => {
                    const reader = new plus.io.FileReader();
                    reader.onloadend = (e) => {
                        const arrayBuffer = e.target.result;
                        const blob = new Blob([arrayBuffer], { type: 'audio/mp3' });
                        resolve(blob);
                    };
                    reader.readAsArrayBuffer(file);
                });
            }, (err) => {
                console.error('读取录音文件失败:', err);
                reject(new Error('读取录音文件失败'));
            });
            // #endif

            // #ifndef H5 || MP-WEIXIN || APP-PLUS
            // 其他环境暂不支持
            reject(new Error('当前环境不支持录音功能'));
            // #endif
        });
    }

    /**
     * 取消uni-app录音
     */
    cancelUniRecording() {
        if (this.recorderManager && this.isRecording) {
            this.recorderManager.stop();
            this.isRecording = false;
            this.recorderManager = null;
            this.tempFilePath = null;
            this.recordingStartTime = null;
            console.log('uni录音已取消');
        }
    }
}

// 创建单例实例
const speechRecognitionService = new SpeechRecognitionService();

export default speechRecognitionService;
