<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">储蓄目标</text>
				<text class="header-action" @click="addGoal">+</text>
			</view>
		</view>
		
		<!-- 总览 -->
		<view class="overview-section">
			<view class="overview-card">
				<view class="overview-item">
					<text class="overview-value">{{totalGoals}}</text>
					<text class="overview-label">储蓄目标</text>
				</view>
				<view class="overview-divider"></view>
				<view class="overview-item">
					<text class="overview-value">¥{{totalSaved.toFixed(2)}}</text>
					<text class="overview-label">已储蓄</text>
				</view>
				<view class="overview-divider"></view>
				<view class="overview-item">
					<text class="overview-value">¥{{totalTarget.toFixed(2)}}</text>
					<text class="overview-label">目标金额</text>
				</view>
			</view>
		</view>
		
		<!-- 储蓄目标列表 -->
		<view class="goals-section">
			<view v-if="savingsGoals.length === 0" class="empty-state">
				<text class="empty-icon">🎯</text>
				<text class="empty-text">还没有储蓄目标</text>
				<button class="add-goal-btn" @click="addGoal">创建第一个目标</button>
			</view>
			
			<view v-else class="goals-list">
				<view 
					v-for="goal in savingsGoals" 
					:key="goal.id"
					class="goal-item"
					@click="viewGoal(goal)"
				>
					<view class="goal-header">
						<view class="goal-icon" :style="{backgroundColor: goal.color}">
							<text class="icon-text">{{goal.icon}}</text>
						</view>
						<view class="goal-info">
							<text class="goal-name">{{goal.name}}</text>
							<text class="goal-deadline" v-if="goal.deadline">
								{{formatDeadline(goal.deadline)}}
							</text>
						</view>
						<view class="goal-actions">
							<text class="action-btn" @click.stop="depositToGoal(goal)">存入</text>
						</view>
					</view>
					
					<view class="goal-progress">
						<view class="progress-info">
							<text class="current-amount">¥{{goal.currentAmount.toFixed(2)}}</text>
							<text class="target-amount">/ ¥{{goal.targetAmount.toFixed(2)}}</text>
						</view>
						<view class="progress-bar">
							<view 
								class="progress-fill" 
								:style="{width: getProgressPercent(goal) + '%', backgroundColor: goal.color}"
							></view>
						</view>
						<view class="progress-stats">
							<text class="progress-percent">{{getProgressPercent(goal)}}%</text>
							<text class="remaining-amount">还需 ¥{{getRemainingAmount(goal)}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 添加目标弹窗 -->
		<view v-if="showGoalPopup" class="popup-overlay" @click="closeGoalPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">新建储蓄目标</text>
					<text class="popup-close" @click="closeGoalPopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">目标名称</text>
						<input 
							class="form-input" 
							v-model="goalForm.name" 
							placeholder="如：紧急备用金、旅行基金"
							maxlength="20"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">目标金额</text>
						<input 
							class="form-input" 
							v-model="goalForm.targetAmount" 
							type="digit"
							placeholder="0.00"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">截止日期（可选）</text>
						<picker 
							mode="date" 
							:value="goalForm.deadline" 
							@change="onDeadlineChange"
							class="date-picker"
						>
							<view class="picker-text">
								{{goalForm.deadline || '请选择截止日期'}}
							</view>
						</picker>
					</view>
				</view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeGoalPopup">取消</button>
					<button class="save-btn" @click="saveGoal">保存</button>
				</view>
			</view>
		</view>
		
		<!-- 存入金额弹窗 -->
		<view v-if="showDepositPopup" class="popup-overlay" @click="closeDepositPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">存入 - {{selectedGoal.name}}</text>
					<text class="popup-close" @click="closeDepositPopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">存入金额</text>
						<input 
							class="form-input" 
							v-model="depositAmount" 
							type="digit"
							placeholder="0.00"
							focus
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">备注（可选）</text>
						<input 
							class="form-input" 
							v-model="depositNote" 
							placeholder="如：工资结余、奖金等"
						/>
					</view>
				</view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeDepositPopup">取消</button>
					<button class="confirm-btn" @click="confirmDeposit">确认存入</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createRecord } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			savingsGoals: [],
			accounts: [],
			showGoalPopup: false,
			showDepositPopup: false,
			selectedGoal: {},
			depositAmount: '',
			depositNote: '',
			goalForm: {
				name: '',
				targetAmount: '',
				deadline: '',
				color: '#4CAF50',
				icon: '🎯'
			}
		};
	},
	
	computed: {
		totalGoals() {
			return this.savingsGoals.length;
		},
		
		totalSaved() {
			return this.savingsGoals.reduce((sum, goal) => sum + goal.currentAmount, 0);
		},
		
		totalTarget() {
			return this.savingsGoals.reduce((sum, goal) => sum + goal.targetAmount, 0);
		}
	},
	
	onLoad() {
		this.loadData();
	},
	
	methods: {
		loadData() {
			this.savingsGoals = StorageManager.getSavingsGoals();
			this.accounts = StorageManager.getAccounts();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		addGoal() {
			this.resetGoalForm();
			this.showGoalPopup = true;
		},
		
		resetGoalForm() {
			this.goalForm = {
				name: '',
				targetAmount: '',
				deadline: '',
				color: '#4CAF50',
				icon: '🎯'
			};
		},
		
		closeGoalPopup() {
			this.showGoalPopup = false;
		},
		
		onDeadlineChange(e) {
			this.goalForm.deadline = e.detail.value;
		},
		
		saveGoal() {
			if (!this.goalForm.name.trim()) {
				uni.showToast({
					title: '请输入目标名称',
					icon: 'none'
				});
				return;
			}
			
			if (!this.goalForm.targetAmount || parseFloat(this.goalForm.targetAmount) <= 0) {
				uni.showToast({
					title: '请输入有效的目标金额',
					icon: 'none'
				});
				return;
			}
			
			const goalData = {
				id: `goal_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
				name: this.goalForm.name.trim(),
				targetAmount: parseFloat(this.goalForm.targetAmount),
				currentAmount: 0,
				deadline: this.goalForm.deadline || null,
				color: this.goalForm.color,
				icon: this.goalForm.icon,
				createdAt: Date.now()
			};
			
			let goals = StorageManager.getSavingsGoals();
			goals.unshift(goalData);
			StorageManager.saveSavingsGoals(goals);
			
			this.loadData();
			uni.showToast({
				title: '目标已创建',
				icon: 'success'
			});
			
			this.closeGoalPopup();
		},
		
		depositToGoal(goal) {
			this.selectedGoal = goal;
			this.depositAmount = '';
			this.depositNote = '';
			this.showDepositPopup = true;
		},
		
		closeDepositPopup() {
			this.showDepositPopup = false;
		},
		
		confirmDeposit() {
			if (!this.depositAmount || parseFloat(this.depositAmount) <= 0) {
				uni.showToast({
					title: '请输入有效金额',
					icon: 'none'
				});
				return;
			}
			
			const amount = parseFloat(this.depositAmount);
			
			// 更新储蓄目标
			let goals = StorageManager.getSavingsGoals();
			const goalIndex = goals.findIndex(g => g.id === this.selectedGoal.id);
			if (goalIndex !== -1) {
				goals[goalIndex].currentAmount += amount;
				StorageManager.saveSavingsGoals(goals);
			}
			
			this.loadData();
			uni.showToast({
				title: `成功存入¥${amount.toFixed(2)}`,
				icon: 'success'
			});
			
			this.closeDepositPopup();
		},
		
		getProgressPercent(goal) {
			if (!goal.targetAmount) return 0;
			const percent = (goal.currentAmount / goal.targetAmount) * 100;
			return Math.min(Math.round(percent), 100);
		},
		
		getRemainingAmount(goal) {
			const remaining = goal.targetAmount - goal.currentAmount;
			return Math.max(remaining, 0).toFixed(2);
		},
		
		formatDeadline(deadline) {
			if (!deadline) return '';
			const date = new Date(deadline);
			const now = new Date();
			const diffTime = date - now;
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			
			if (diffDays < 0) {
				return '已过期';
			} else if (diffDays === 0) {
				return '今天到期';
			} else if (diffDays <= 30) {
				return `${diffDays}天后到期`;
			} else {
				return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		
		viewGoal(goal) {
			// 可以跳转到详情页面
			console.log('查看目标详情:', goal);
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn, .header-action {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

.overview-section {
	margin: -20rpx 40rpx 40rpx;
}

.overview-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.overview-item {
	flex: 1;
	text-align: center;
}

.overview-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.overview-label {
	font-size: 24rpx;
	color: #666;
}

.overview-divider {
	width: 1rpx;
	background: #f0f0f0;
	margin: 0 30rpx;
}

.goals-section {
	margin: 0 40rpx;
}

.empty-state {
	background: white;
	border-radius: 20rpx;
	padding: 100rpx 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.add-goal-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 48rpx;
	font-size: 28rpx;
	font-weight: bold;
}

.goals-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.goal-item {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.goal-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.goal-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.icon-text {
	font-size: 32rpx;
}

.goal-info {
	flex: 1;
}

.goal-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.goal-deadline {
	font-size: 24rpx;
	color: #666;
}

.action-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 16rpx 32rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: bold;
}

.goal-progress {
	margin-bottom: 20rpx;
}

.progress-info {
	display: flex;
	align-items: baseline;
	margin-bottom: 16rpx;
}

.current-amount {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.target-amount {
	font-size: 28rpx;
	color: #666;
	margin-left: 8rpx;
}

.progress-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 16rpx;
}

.progress-fill {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.3s;
}

.progress-stats {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.progress-percent {
	font-size: 24rpx;
	font-weight: bold;
	color: #4CAF50;
}

.remaining-amount {
	font-size: 24rpx;
	color: #666;
}

.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	width: 100%;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

.date-picker {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
}

.cancel-btn, .save-btn, .confirm-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn, .confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
</style>
