<script>
	import SecurityManager from '@/utils/securityManager.js';
	import ThemeManager from '@/utils/themeManager.js';
	
	export default {
		onLaunch: function() {
			console.log('App Launch');
			// 初始化主题管理器
			ThemeManager.init();
			// 应用启动时检查安全设置
			this.checkSecurity();
		},
		onShow: function() {
			console.log('App Show');
			// 应用从后台恢复时检查是否需要解锁
			this.checkSecurity();
		},
		onHide: function() {
			console.log('App Hide');
		},
		methods: {
			/**
			 * 检查安全设置
			 */
			checkSecurity() {
				try {
					// 检查是否需要解锁
					if (SecurityManager.needsUnlock()) {
						// 延迟执行，确保页面加载完成
						setTimeout(() => {
							this.navigateToUnlock();
						}, 100);
					}
				} catch (error) {
					console.error('检查安全设置失败:', error);
				}
			},
			
			/**
			 * 跳转到解锁页面
			 */
			navigateToUnlock() {
				// 获取当前页面
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				
				// 如果当前不在解锁页面，则跳转到解锁页面
				if (currentPage && !currentPage.route.includes('security/unlock')) {
					uni.navigateTo({
						url: '/pages/security/unlock',
						fail: (err) => {
							console.error('跳转解锁页面失败:', err);
							// 如果跳转失败，尝试重定向
							uni.reLaunch({
								url: '/pages/security/unlock'
							});
						}
					});
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
