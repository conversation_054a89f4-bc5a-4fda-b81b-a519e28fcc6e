<template>
  <view class="pin-setup">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="iconfont icon-back">‹</text>
        </view>
        <view class="navbar-title">设置PIN码</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <view class="content">
      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step" :class="{ active: currentStep >= 1 }">
          <view class="step-number">1</view>
          <text class="step-text">设置PIN码</text>
        </view>
        <view class="step-line" :class="{ active: currentStep >= 2 }"></view>
        <view class="step" :class="{ active: currentStep >= 2 }">
          <view class="step-number">2</view>
          <text class="step-text">确认PIN码</text>
        </view>
      </view>

      <!-- PIN码输入区域 -->
      <view class="pin-input-section">
        <view class="pin-title">
          {{ currentStep === 1 ? '请设置6位数字PIN码' : '请再次输入PIN码确认' }}
        </view>
        <view class="pin-subtitle">
          PIN码将用于保护您的财务数据安全
        </view>

        <!-- PIN码显示 -->
        <view class="pin-display">
          <view 
            v-for="(item, index) in 6" 
            :key="index"
            class="pin-dot"
            :class="{ filled: index < currentPin.length }"
          >
            <text v-if="index < currentPin.length">●</text>
          </view>
        </view>

        <!-- 数字键盘 -->
        <view class="number-keyboard">
          <view class="keyboard-row" v-for="(row, rowIndex) in keyboard" :key="rowIndex">
            <view 
              v-for="(key, keyIndex) in row" 
              :key="keyIndex"
              class="keyboard-key"
              :class="{ 
                'key-number': typeof key === 'number',
                'key-action': typeof key === 'string'
              }"
              @click="handleKeyPress(key)"
            >
              <text v-if="key === 'delete'" class="iconfont">⌫</text>
              <text v-else>{{ key }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 错误提示 -->
      <view v-if="errorMessage" class="error-message">
        <text>{{ errorMessage }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import SecurityManager from '@/utils/securityManager.js';

export default {
  name: 'PinSetup',
  data() {
    return {
      currentStep: 1, // 1: 设置PIN码, 2: 确认PIN码
      currentPin: '',
      firstPin: '',
      errorMessage: '',
      keyboard: [
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9],
        ['', 0, 'delete']
      ]
    };
  },
  methods: {
    /**
     * 处理按键点击
     */
    handleKeyPress(key) {
      if (key === '') return;
      
      if (key === 'delete') {
        this.currentPin = this.currentPin.slice(0, -1);
        this.clearError();
      } else if (typeof key === 'number') {
        if (this.currentPin.length < 6) {
          this.currentPin += key.toString();
          this.clearError();
          
          // 当输入满6位时自动处理
          if (this.currentPin.length === 6) {
            setTimeout(() => {
              this.handlePinComplete();
            }, 200);
          }
        }
      }
    },

    /**
     * 处理PIN码输入完成
     */
    handlePinComplete() {
      if (this.currentStep === 1) {
        // 第一步：保存PIN码，进入确认步骤
        this.firstPin = this.currentPin;
        this.currentPin = '';
        this.currentStep = 2;
      } else {
        // 第二步：验证PIN码是否一致
        if (this.currentPin === this.firstPin) {
          this.savePinCode();
        } else {
          this.showError('两次输入的PIN码不一致，请重新设置');
          this.resetToFirstStep();
        }
      }
    },

    /**
     * 保存PIN码
     */
    async savePinCode() {
      try {
        uni.showLoading({
          title: '设置中...'
        });

        const success = SecurityManager.setPinCode(this.firstPin);
        
        uni.hideLoading();
        
        if (success) {
          uni.showToast({
            title: 'PIN码设置成功',
            icon: 'success'
          });
          
          // 延迟跳转到安全设置页面
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          this.showError('PIN码设置失败，请重试');
          this.resetToFirstStep();
        }
      } catch (error) {
        uni.hideLoading();
        this.showError('设置失败：' + error.message);
        this.resetToFirstStep();
      }
    },

    /**
     * 重置到第一步
     */
    resetToFirstStep() {
      this.currentStep = 1;
      this.currentPin = '';
      this.firstPin = '';
    },

    /**
     * 显示错误信息
     */
    showError(message) {
      this.errorMessage = message;
      setTimeout(() => {
        this.clearError();
      }, 3000);
    },

    /**
     * 清除错误信息
     */
    clearError() {
      this.errorMessage = '';
    },

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
.pin-setup {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-navbar {
  padding-top: var(--status-bar-height);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.navbar-left {
  width: 60px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.icon-back {
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  width: 60px;
}

.content {
  padding: 40px 30px;
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.step.active .step-number {
  background: #fff;
  color: #667eea;
}

.step-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.step.active .step-text {
  color: #fff;
}

.step-line {
  width: 60px;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 20px;
  margin-bottom: 20px;
}

.step-line.active {
  background: #fff;
}

.pin-input-section {
  text-align: center;
}

.pin-title {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}

.pin-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
}

.pin-display {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 50px;
}

.pin-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
}

.pin-dot.filled {
  border-color: #fff;
  background: #fff;
  color: #667eea;
}

.number-keyboard {
  max-width: 300px;
  margin: 0 auto;
}

.keyboard-row {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.keyboard-key {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.keyboard-key:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.key-number {
  background: rgba(255, 255, 255, 0.2);
}

.key-action {
  background: rgba(255, 255, 255, 0.1);
}

.error-message {
  position: fixed;
  bottom: 100px;
  left: 30px;
  right: 30px;
  background: rgba(255, 59, 48, 0.9);
  color: #fff;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  font-size: 14px;
  backdrop-filter: blur(10px);
}
</style>
