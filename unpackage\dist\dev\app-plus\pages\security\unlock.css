
.unlock-page[data-v-7a2845a0] {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.content[data-v-7a2845a0] {
  width: 100%;
  max-width: 400px;
  padding: 40px 30px;
  text-align: center;
}
.app-info[data-v-7a2845a0] {
  margin-bottom: 60px;
}
.app-icon[data-v-7a2845a0] {
  font-size: 60px;
  margin-bottom: 20px;
}
.app-name[data-v-7a2845a0] {
  font-size: 28px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10px;
}
.app-subtitle[data-v-7a2845a0] {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}
.fingerprint-section[data-v-7a2845a0] {
  margin-bottom: 40px;
}
.fingerprint-icon[data-v-7a2845a0] {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.fingerprint-icon[data-v-7a2845a0]:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.fingerprint-icon .icon[data-v-7a2845a0] {
  font-size: 40px;
}
.fingerprint-text[data-v-7a2845a0] {
  font-size: 18px;
  color: #fff;
  margin-bottom: 20px;
}
.switch-to-pin[data-v-7a2845a0] {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  text-decoration: underline;
}
.pin-section[data-v-7a2845a0] {
  margin-bottom: 40px;
}
.pin-title[data-v-7a2845a0] {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 30px;
}
.pin-display[data-v-7a2845a0] {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}
.pin-dot[data-v-7a2845a0] {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  transition: all 0.3s ease;
}
.pin-dot.filled[data-v-7a2845a0] {
  border-color: #fff;
  background: #fff;
  color: #667eea;
}
.pin-dot.error[data-v-7a2845a0] {
  border-color: #FF5722;
  background: #FF5722;
  animation: shake-7a2845a0 0.5s ease-in-out;
}
@keyframes shake-7a2845a0 {
0%, 100% { transform: translateX(0);
}
25% { transform: translateX(-5px);
}
75% { transform: translateX(5px);
}
}
.error-text[data-v-7a2845a0] {
  font-size: 14px;
  color: #FF5722;
  margin-bottom: 20px;
  min-height: 20px;
}
.number-keyboard[data-v-7a2845a0] {
  max-width: 300px;
  margin: 0 auto;
}
.keyboard-row[data-v-7a2845a0] {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}
.keyboard-key[data-v-7a2845a0] {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}
.keyboard-key[data-v-7a2845a0]:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.key-number[data-v-7a2845a0] {
  background: rgba(255, 255, 255, 0.2);
}
.key-action[data-v-7a2845a0] {
  background: rgba(255, 255, 255, 0.1);
  font-size: 20px;
}
.forgot-pin[data-v-7a2845a0] {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  text-decoration: underline;
}
