<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">拍照记账</text>
			</view>
		</view>
		
		<!-- 拍照区域 -->
		<view class="photo-section">
			<view v-if="!photoPath" class="photo-placeholder" @click="takePhoto">
				<text class="photo-icon">📷</text>
				<text class="photo-text">点击拍照或选择图片</text>
				<text class="photo-tip">支持识别小票、发票等</text>
			</view>
			
			<view v-else class="photo-preview">
				<image :src="photoPath" class="preview-image" mode="aspectFit"></image>
				<view class="photo-actions">
					<button class="retake-btn" @click="retakePhoto">重新拍照</button>
					<button class="analyze-btn" @click="analyzePhoto" :disabled="isAnalyzing">
						{{isAnalyzing ? '识别中...' : '识别内容'}}
					</button>
				</view>
			</view>
		</view>
		
		<!-- 识别结果 -->
		<view v-if="analysisResult" class="result-section">
			<view class="result-card">
				<view class="result-header">
					<text class="result-title">识别结果</text>
					<text class="result-confidence">置信度: {{analysisResult.confidence}}%</text>
				</view>
				
				<!-- 识别到的文本 -->
				<view v-if="analysisResult.text" class="recognized-text">
					<text class="text-label">识别文本：</text>
					<text class="text-content">{{analysisResult.text}}</text>
				</view>
				
				<!-- 解析后的记账信息 -->
				<view v-if="analysisResult.records && analysisResult.records.length > 0" class="parsed-records">
					<text class="records-title">识别到的记账信息：</text>
					<view 
						v-for="(record, index) in analysisResult.records" 
						:key="index"
						class="record-item"
						:class="{selected: selectedRecords.includes(index)}"
						@click="toggleRecord(index)"
					>
						<view class="record-info">
							<text class="record-type">{{record.type === 'expense' ? '支出' : '收入'}}</text>
							<text class="record-amount">¥{{record.amount}}</text>
						</view>
						<view v-if="record.description" class="record-desc">{{record.description}}</view>
						<view v-if="record.category" class="record-category">分类: {{record.category}}</view>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="result-actions">
					<button class="edit-btn" @click="editResults">手动编辑</button>
					<button 
						class="save-btn" 
						@click="saveRecords" 
						:disabled="selectedRecords.length === 0"
					>
						保存选中记录 ({{selectedRecords.length}})
					</button>
				</view>
			</view>
		</view>
		
		<!-- 使用说明 -->
		<view class="tips-section">
			<view class="tips-card">
				<view class="tips-header">
					<text class="tips-title">📝 使用说明</text>
				</view>
				<view class="tips-content">
					<text class="tip-item">• 支持识别购物小票、餐饮发票等</text>
					<text class="tip-item">• 拍照时请保持图片清晰，光线充足</text>
					<text class="tip-item">• 支持从相册选择已有图片</text>
					<text class="tip-item">• 可手动编辑识别结果</text>
				</view>
			</view>
		</view>
		
		<!-- 编辑弹窗 -->
		<view v-if="showEditPopup" class="popup-overlay" @click="closeEditPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">编辑记账信息</text>
					<text class="popup-close" @click="closeEditPopup">×</text>
				</view>
				
				<scroll-view class="form-scroll" scroll-y>
					<view class="form-section">
						<view 
							v-for="(record, index) in editingRecords" 
							:key="index"
							class="edit-record"
						>
							<view class="record-header">
								<text class="record-index">记录 {{index + 1}}</text>
								<text class="delete-record" @click="deleteEditRecord(index)">删除</text>
							</view>
							
							<view class="form-item">
								<text class="form-label">类型</text>
								<view class="type-selector">
									<view 
										class="type-option"
										:class="{active: record.type === 'expense'}"
										@click="record.type = 'expense'"
									>
										<text>支出</text>
									</view>
									<view 
										class="type-option"
										:class="{active: record.type === 'income'}"
										@click="record.type = 'income'"
									>
										<text>收入</text>
									</view>
								</view>
							</view>
							
							<view class="form-item">
								<text class="form-label">金额</text>
								<input 
									class="form-input" 
									v-model="record.amount" 
									type="digit"
									placeholder="0.00"
								/>
							</view>
							
							<view class="form-item">
								<text class="form-label">描述</text>
								<input 
									class="form-input" 
									v-model="record.description" 
									placeholder="请输入描述"
								/>
							</view>
							
							<view class="form-item">
								<text class="form-label">分类</text>
								<input 
									class="form-input" 
									v-model="record.category" 
									placeholder="请输入分类"
								/>
							</view>
						</view>
						
						<button class="add-record-btn" @click="addEditRecord">+ 添加记录</button>
					</view>
				</scroll-view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeEditPopup">取消</button>
					<button class="confirm-btn" @click="confirmEdit">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createRecord } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';

export default {
	data() {
		return {
			photoPath: '',
			isAnalyzing: false,
			analysisResult: null,
			selectedRecords: [],
			showEditPopup: false,
			editingRecords: [],
			accounts: [],
			categories: []
		};
	},
	
	onLoad() {
		this.loadData();
	},
	
	methods: {
		loadData() {
			this.accounts = StorageManager.getAccounts();
			this.categories = StorageManager.getCategories();
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		// 拍照或选择图片
		takePhoto() {
			uni.showActionSheet({
				itemList: ['拍照', '从相册选择'],
				success: (res) => {
					if (res.tapIndex === 0) {
						this.chooseImage('camera');
					} else {
						this.chooseImage('album');
					}
				}
			});
		},
		
		// 选择图片
		chooseImage(sourceType) {
			uni.chooseImage({
				count: 1,
				sourceType: [sourceType],
				success: (res) => {
					this.photoPath = res.tempFilePaths[0];
					this.analysisResult = null;
					this.selectedRecords = [];
				},
				fail: (error) => {
					console.error('选择图片失败:', error);
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 重新拍照
		retakePhoto() {
			this.photoPath = '';
			this.analysisResult = null;
			this.selectedRecords = [];
		},
		
		// 分析图片
		async analyzePhoto() {
			if (!this.photoPath) return;
			
			this.isAnalyzing = true;
			
			try {
				// 模拟图片识别过程（实际项目中需要调用OCR服务）
				await this.simulateOCR();
			} catch (error) {
				console.error('图片识别失败:', error);
				uni.showToast({
					title: '识别失败',
					icon: 'none'
				});
			} finally {
				this.isAnalyzing = false;
			}
		},
		
		// 模拟OCR识别（实际项目中需要集成真实的OCR服务）
		async simulateOCR() {
			return new Promise((resolve) => {
				setTimeout(() => {
					// 模拟识别结果
					const mockResults = [
						{
							confidence: 95,
							text: '沃尔玛超市\n牛奶 25.80\n面包 12.50\n鸡蛋 18.90\n合计: 57.20',
							records: [
								{ type: 'expense', amount: '25.80', description: '牛奶', category: '餐饮' },
								{ type: 'expense', amount: '12.50', description: '面包', category: '餐饮' },
								{ type: 'expense', amount: '18.90', description: '鸡蛋', category: '餐饮' }
							]
						},
						{
							confidence: 88,
							text: '麦当劳\n巨无霸套餐 35.00\n可乐 8.00\n合计: 43.00',
							records: [
								{ type: 'expense', amount: '43.00', description: '麦当劳套餐', category: '餐饮' }
							]
						},
						{
							confidence: 92,
							text: '中石化加油站\n92号汽油 280.00\n洗车费 20.00\n合计: 300.00',
							records: [
								{ type: 'expense', amount: '280.00', description: '加油', category: '交通' },
								{ type: 'expense', amount: '20.00', description: '洗车', category: '交通' }
							]
						}
					];
					
					const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)];
					this.analysisResult = randomResult;
					
					// 默认选中所有识别到的记录
					this.selectedRecords = randomResult.records.map((_, index) => index);
					
					resolve();
				}, 2000);
			});
		},
		
		// 切换记录选择状态
		toggleRecord(index) {
			const selectedIndex = this.selectedRecords.indexOf(index);
			if (selectedIndex > -1) {
				this.selectedRecords.splice(selectedIndex, 1);
			} else {
				this.selectedRecords.push(index);
			}
		},
		
		// 编辑识别结果
		editResults() {
			if (!this.analysisResult || !this.analysisResult.records) return;
			
			this.editingRecords = JSON.parse(JSON.stringify(this.analysisResult.records));
			this.showEditPopup = true;
		},
		
		// 关闭编辑弹窗
		closeEditPopup() {
			this.showEditPopup = false;
			this.editingRecords = [];
		},
		
		// 添加编辑记录
		addEditRecord() {
			this.editingRecords.push({
				type: 'expense',
				amount: '',
				description: '',
				category: ''
			});
		},
		
		// 删除编辑记录
		deleteEditRecord(index) {
			this.editingRecords.splice(index, 1);
		},
		
		// 确认编辑
		confirmEdit() {
			if (this.editingRecords.length === 0) {
				uni.showToast({
					title: '至少保留一条记录',
					icon: 'none'
				});
				return;
			}
			
			// 验证数据
			for (let i = 0; i < this.editingRecords.length; i++) {
				const record = this.editingRecords[i];
				if (!record.amount || parseFloat(record.amount) <= 0) {
					uni.showToast({
						title: `记录${i + 1}的金额无效`,
						icon: 'none'
					});
					return;
				}
			}
			
			// 更新分析结果
			this.analysisResult.records = this.editingRecords;
			this.selectedRecords = this.editingRecords.map((_, index) => index);
			
			this.closeEditPopup();
		},
		
		// 保存记录
		async saveRecords() {
			if (this.selectedRecords.length === 0) {
				uni.showToast({
					title: '请选择要保存的记录',
					icon: 'none'
				});
				return;
			}
			
			try {
				const records = StorageManager.getRecords();
				const accounts = StorageManager.getAccounts();
				const defaultAccount = accounts.find(a => a.type === 'cash') || accounts[0];
				
				// 保存选中的记录
				for (const index of this.selectedRecords) {
					const recordData = this.analysisResult.records[index];
					
					// 查找匹配的分类
					let categoryId = '';
					if (recordData.category) {
						const category = this.categories.find(c => 
							c.name.includes(recordData.category) || 
							recordData.category.includes(c.name)
						);
						if (category) {
							categoryId = category.id;
						}
					}
					
					const record = createRecord({
						type: recordData.type,
						categoryId,
						accountId: defaultAccount?.id || '',
						amount: parseFloat(recordData.amount),
						note: recordData.description || '',
						date: Date.now()
					});
					
					records.unshift(record);
					
					// 更新账户余额
					if (defaultAccount) {
						const accountIndex = accounts.findIndex(a => a.id === defaultAccount.id);
						if (accountIndex !== -1) {
							if (recordData.type === 'expense') {
								accounts[accountIndex].balance -= parseFloat(recordData.amount);
							} else {
								accounts[accountIndex].balance += parseFloat(recordData.amount);
							}
						}
					}
				}
				
				// 保存数据
				StorageManager.saveRecords(records);
				StorageManager.saveAccounts(accounts);
				
				uni.showToast({
					title: `成功保存${this.selectedRecords.length}条记录`,
					icon: 'success'
				});
				
				// 延迟返回
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				
			} catch (error) {
				console.error('保存记录失败:', error);
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			}
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 拍照区域 */
.photo-section {
	margin: 40rpx;
}

.photo-placeholder {
	background: white;
	border-radius: 20rpx;
	padding: 100rpx 40rpx;
	text-align: center;
	border: 2rpx dashed #ddd;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.photo-icon {
	font-size: 80rpx;
	display: block;
	margin-bottom: 20rpx;
}

.photo-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 16rpx;
}

.photo-tip {
	font-size: 26rpx;
	color: #666;
}

.photo-preview {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.preview-image {
	width: 100%;
	max-height: 400rpx;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}

.photo-actions {
	display: flex;
	gap: 20rpx;
}

.retake-btn, .analyze-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
	border: none;
}

.retake-btn {
	background: #f8f9fa;
	color: #666;
}

.analyze-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.analyze-btn:disabled {
	background: #ccc;
	color: #999;
}

/* 识别结果 */
.result-section {
	margin: 0 40rpx 40rpx;
}

.result-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.result-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.result-confidence {
	font-size: 24rpx;
	color: #4CAF50;
	background: rgba(76, 175, 80, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.recognized-text {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.text-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 16rpx;
}

.text-content {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	white-space: pre-line;
}

.parsed-records {
	margin-bottom: 30rpx;
}

.records-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.record-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s;
}

.record-item.selected {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.1);
}

.record-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.record-type {
	font-size: 24rpx;
	color: #666;
	background: rgba(102, 126, 234, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.record-amount {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.record-desc {
	font-size: 26rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.record-category {
	font-size: 24rpx;
	color: #666;
}

.result-actions {
	display: flex;
	gap: 20rpx;
}

.edit-btn, .save-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
	border: none;
}

.edit-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.save-btn:disabled {
	background: #ccc;
	color: #999;
}

/* 使用说明 */
.tips-section {
	margin: 0 40rpx;
}

.tips-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tips-header {
	margin-bottom: 30rpx;
}

.tips-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.tips-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.tip-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	width: 100%;
	max-height: 90vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-scroll {
	flex: 1;
	max-height: 60vh;
}

.form-section {
	padding-bottom: 40rpx;
}

.edit-record {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.record-index {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.delete-record {
	font-size: 26rpx;
	color: #ff4757;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 16rpx;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	background: white;
	border-radius: 8rpx;
	border: 1rpx solid #ddd;
	font-size: 28rpx;
	color: #333;
}

.type-selector {
	display: flex;
	gap: 16rpx;
}

.type-option {
	flex: 1;
	padding: 20rpx;
	background: white;
	border-radius: 8rpx;
	text-align: center;
	border: 1rpx solid #ddd;
	transition: all 0.3s;
}

.type-option.active {
	background: #667eea;
	color: white;
	border-color: #667eea;
}

.add-record-btn {
	width: 100%;
	height: 80rpx;
	background: #f8f9fa;
	color: #666;
	border: 2rpx dashed #ddd;
	border-radius: 12rpx;
	font-size: 28rpx;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
</style>
