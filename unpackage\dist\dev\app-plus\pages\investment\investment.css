
.investment-container[data-v-b3a7e233] {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header[data-v-b3a7e233] {
  padding: 44px 20px 20px;
  background: rgba(255, 255, 255, 0.1);
}
.header-content[data-v-b3a7e233] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title[data-v-b3a7e233] {
  font-size: 28px;
  font-weight: bold;
  color: white;
}
.add-btn[data-v-b3a7e233] {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-icon[data-v-b3a7e233] {
  font-size: 24px;
  color: white;
  font-weight: bold;
}
.overview-section[data-v-b3a7e233] {
  margin: 20px;
}
.overview-card[data-v-b3a7e233] {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.overview-item[data-v-b3a7e233] {
  flex: 1;
  min-width: 120px;
  text-align: center;
}
.overview-label[data-v-b3a7e233] {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.overview-value[data-v-b3a7e233] {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.overview-value.profit[data-v-b3a7e233] {
  color: #4CAF50;
}
.overview-value.loss[data-v-b3a7e233] {
  color: #F44336;
}
.filter-section[data-v-b3a7e233] {
  margin: 20px;
}
.filter-scroll[data-v-b3a7e233] {
  white-space: nowrap;
}
.filter-item[data-v-b3a7e233] {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  margin-right: 12px;
  text-align: center;
  min-width: 80px;
}
.filter-item.active[data-v-b3a7e233] {
  background: white;
}
.filter-icon[data-v-b3a7e233] {
  display: block;
  font-size: 20px;
  margin-bottom: 4px;
}
.filter-name[data-v-b3a7e233] {
  display: block;
  font-size: 12px;
  color: white;
}
.filter-item.active .filter-name[data-v-b3a7e233] {
  color: #333;
}
.investment-list[data-v-b3a7e233] {
  margin: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.investment-item[data-v-b3a7e233] {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.item-header[data-v-b3a7e233] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.item-info[data-v-b3a7e233] {
  flex: 1;
}
.item-name[data-v-b3a7e233] {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}
.item-symbol[data-v-b3a7e233] {
  font-size: 12px;
  color: #999;
}
.type-icon[data-v-b3a7e233] {
  font-size: 24px;
}
.item-details[data-v-b3a7e233] {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}
.detail-row[data-v-b3a7e233] {
  flex: 1;
  min-width: 100px;
}
.detail-label[data-v-b3a7e233] {
  display: block;
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}
.detail-value[data-v-b3a7e233] {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}
.item-profit[data-v-b3a7e233] {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.profit-info[data-v-b3a7e233] {
  flex: 1;
  min-width: 100px;
}
.profit-label[data-v-b3a7e233] {
  display: block;
  font-size: 10px;
  color: #999;
  margin-bottom: 4px;
}
.profit-value[data-v-b3a7e233] {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}
.profit-value.profit[data-v-b3a7e233] {
  color: #4CAF50;
}
.profit-value.loss[data-v-b3a7e233] {
  color: #F44336;
}
.modal[data-v-b3a7e233] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content[data-v-b3a7e233] {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}
.modal-header[data-v-b3a7e233] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title[data-v-b3a7e233] {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.modal-close[data-v-b3a7e233] {
  font-size: 24px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-body[data-v-b3a7e233] {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}
.form-group[data-v-b3a7e233] {
  margin-bottom: 20px;
}
.form-label[data-v-b3a7e233] {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}
.form-input[data-v-b3a7e233] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}
.form-textarea[data-v-b3a7e233] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  min-height: 80px;
  resize: vertical;
}
.type-options[data-v-b3a7e233] {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.type-option[data-v-b3a7e233] {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}
.type-option.selected[data-v-b3a7e233] {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}
.type-icon[data-v-b3a7e233] {
  margin-right: 4px;
}
.picker-view[data-v-b3a7e233] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
}
.picker-text[data-v-b3a7e233] {
  color: #333;
}
.picker-arrow[data-v-b3a7e233] {
  color: #999;
}
.modal-footer[data-v-b3a7e233] {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}
.btn[data-v-b3a7e233] {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
}
.btn-cancel[data-v-b3a7e233] {
  background: #f0f0f0;
  color: #666;
}
.btn-primary[data-v-b3a7e233] {
  background: #667eea;
  color: white;
}
