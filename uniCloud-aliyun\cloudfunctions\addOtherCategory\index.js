"use strict";
const db = uniCloud.database();

/**
 * 添加"其他"支出分类到现有数据库
 * 这个云函数用于在现有系统中添加"其他"分类，避免重复初始化
 */
exports.main = async () => {
  try {
    const collection = db.collection("categories");
    
    // 检查是否已经存在"其他"分类
    const existingOther = await collection.where({
      name: "其他",
      type: "expense",
      group_id: "daily"
    }).get();
    
    if (existingOther.data.length > 0) {
      return {
        code: 0,
        message: "其他分类已存在，无需重复添加"
      };
    }
    
    // 添加"其他"支出分类
    const otherCategory = {
      name: "其他",
      type: "expense",
      group_id: "daily",
      icon: "ellipsis-h",
      order: 6
    };
    
    await collection.add(otherCategory);
    
    return {
      code: 0,
      message: "成功添加其他支出分类"
    };
    
  } catch (error) {
    console.error('添加其他分类失败:', error);
    return {
      code: -1,
      message: "添加失败: " + error.message
    };
  }
};
