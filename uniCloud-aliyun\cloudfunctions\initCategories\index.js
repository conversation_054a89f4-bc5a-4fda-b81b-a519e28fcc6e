"use strict";
const db = uniCloud.database();

// 默认分类
const defaultCategories = [
  // 支出 daily
  { name: "餐饮", type: "expense", group_id: "daily", icon: "knife-fork", order: 1 },
  { name: "交通", type: "expense", group_id: "daily", icon: "car", order: 2 },
  { name: "购物", type: "expense", group_id: "daily", icon: "shopping", order: 3 },
  { name: "娱乐", type: "expense", group_id: "daily", icon: "gamepad", order: 4 },
  { name: "日用品", type: "expense", group_id: "daily", icon: "home", order: 5 },
  { name: "其他", type: "expense", group_id: "daily", icon: "ellipsis-h", order: 6 },
  // 收入 daily
  { name: "工资", type: "income", group_id: "daily", icon: "wallet", order: 1 },
  { name: "红包", type: "income", group_id: "daily", icon: "gift", order: 2 },
  // 报销 company
  { name: "商务餐饮", type: "reimburse", group_id: "company", icon: "utensils", order: 1 },
  { name: "差旅交通", type: "reimburse", group_id: "company", icon: "plane", order: 2 },
  { name: "住宿费", type: "reimburse", group_id: "company", icon: "hotel", order: 3 },
  { name: "办公用品", type: "reimburse", group_id: "company", icon: "clipboard", order: 4 },
  { name: "通讯费", type: "reimburse", group_id: "company", icon: "phone", order: 5 },
  { name: "培训费", type: "reimburse", group_id: "company", icon: "chalkboard-teacher", order: 6 },
  { name: "商务招待", type: "reimburse", group_id: "company", icon: "handshake", order: 7 },
  { name: "其他报销", type: "reimburse", group_id: "company", icon: "ellipsis-h", order: 8 }
];

exports.main = async () => {
  const collection = db.collection("categories");
  // 判断是否已初始化
  const countRes = await collection.count();
  if (countRes.total > 0) {
    return {
      code: 0,
      message: "已初始化，无需重复导入"
    };
  }
  await collection.add(defaultCategories);
  return {
    code: 0,
    message: `成功导入 ${defaultCategories.length} 条分类数据`
  };
};
